"""Test dependency injection container integration."""

from typing import Any

import pytest
from pydantic import BaseModel

from plugginger.api import Plugginger<PERSON><PERSON><PERSON><PERSON>er, PluginBase, plugin, service
from plugginger.api.app import PluggingerAppInstance
from plugginger.implementations.container import D<PERSON><PERSON>r


@plugin(name="config_service", version="1.0.0")
class ConfigServicePlugin(PluginBase):
    """Configuration service plugin."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app=app, **injected_dependencies)
        self.config_data = {
            "database_url": "sqlite:///test.db",
            "api_key": "test_api_key_123",
            "debug_mode": True,
            "max_connections": 10,
        }

    @service()
    async def get_config(self, key: str) -> Any:
        """Get configuration value."""
        return self.config_data.get(key)

    @service()
    async def get_all_config(self) -> dict[str, Any]:
        """Get all configuration."""
        return self.config_data.copy()

    @service()
    async def set_config(self, key: str, value: Any) -> None:
        """Set configuration value."""
        self.config_data[key] = value


@plugin(name="database_service", version="1.0.0")
class DatabaseServicePlugin(PluginBase):
    """Database service that depends on config."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app=app, **injected_dependencies)
        self.connection: str | None = None
        self.is_connected = False

    async def setup(self, plugin_config: BaseModel) -> None:
        """Setup database connection using config service."""
        # Get database URL from config service
        db_url = await self.app.call_service("config_service.get_config", key="database_url")
        debug_mode = await self.app.call_service("config_service.get_config", key="debug_mode")

        self.connection = f"connection_to_{db_url}"
        self.is_connected = True

        if debug_mode:
            print(f"Database connected to: {db_url}")

    @service()
    async def execute_query(self, sql: str) -> dict[str, Any]:
        """Execute database query."""
        if not self.is_connected:
            raise RuntimeError("Database not connected")

        return {
            "sql": sql,
            "connection": self.connection,
            "result": f"mock_result_for_{sql}",
            "rows_affected": 1,
        }

    @service()
    async def get_connection_info(self) -> dict[str, Any]:
        """Get connection information."""
        return {"connection": self.connection, "is_connected": self.is_connected}


@plugin(name="api_service", version="1.0.0")
class ApiServicePlugin(PluginBase):
    """API service that depends on config and database."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app=app, **injected_dependencies)
        self.api_key: str | None = None
        self.max_connections = 0

    async def setup(self, plugin_config: BaseModel) -> None:
        """Setup API service using config."""
        self.api_key = await self.app.call_service("config_service.get_config", key="api_key")
        self.max_connections = await self.app.call_service(
            "config_service.get_config", key="max_connections"
        )

    @service()
    async def process_request(self, endpoint: str, data: dict[str, Any]) -> dict[str, Any]:
        """Process API request using database."""
        # Validate API key
        if not self.api_key:
            raise RuntimeError("API service not configured")

        # Log request to database
        log_result = await self.app.call_service(
            "database_service.execute_query",
            sql=f"INSERT INTO api_logs (endpoint, data) VALUES ('{endpoint}', '{data}')",
        )

        return {
            "endpoint": endpoint,
            "data": data,
            "api_key": self.api_key[:8] + "...",  # Masked
            "log_id": log_result["rows_affected"],
            "status": "success",
        }

    @service()
    async def get_api_info(self) -> dict[str, Any]:
        """Get API service information."""
        return {
            "api_key_configured": self.api_key is not None,
            "max_connections": self.max_connections,
        }


class TestDependencyInjectionIntegration:
    """Test dependency injection integration."""

    def test_di_container_basic_functionality(self) -> None:
        """Test basic DI container functionality."""
        container = DIContainer()

        # Test registration
        container.register_concrete_instance("test_value")
        assert container.has(str)

        # Test retrieval
        value = container.get(str)
        assert value == "test_value"

    def test_plugin_dependency_chain(self) -> None:
        """Test plugin dependency chain setup."""
        app = (
            PluggingerAppBuilder()
            .register_plugin(ConfigServicePlugin)
            .register_plugin(DatabaseServicePlugin)
            .register_plugin(ApiServicePlugin)
            .build()
        )

        # Verify all plugins are registered
        services = app.list_services()

        config_services = [s for s in services if s.startswith("config_service.")]
        database_services = [s for s in services if s.startswith("database_service.")]
        api_services = [s for s in services if s.startswith("api_service.")]

        assert len(config_services) >= 3
        assert len(database_services) >= 2
        assert len(api_services) >= 2

    @pytest.mark.asyncio
    async def test_service_dependency_resolution(self) -> None:
        """Test service dependency resolution."""
        app = (
            PluggingerAppBuilder()
            .register_plugin(ConfigServicePlugin)
            .register_plugin(DatabaseServicePlugin)
            .build()
        )

        # Setup plugins (this would normally be done by app lifecycle)
        # For now, test individual service calls

        # Test config service
        db_url = await app.call_service("config_service.get_config", key="database_url")
        assert db_url == "sqlite:///test.db"

        # Test database service (without setup for now)
        # ServiceExecutionError wraps the original RuntimeError
        from plugginger.core.exceptions import ServiceExecutionError

        with pytest.raises(ServiceExecutionError):
            await app.call_service("database_service.execute_query", sql="SELECT 1")

    @pytest.mark.asyncio
    async def test_configuration_propagation(self) -> None:
        """Test configuration propagation through services."""
        app = PluggingerAppBuilder().register_plugin(ConfigServicePlugin).build()

        # Test getting configuration
        config = await app.call_service("config_service.get_all_config")
        assert config["database_url"] == "sqlite:///test.db"
        assert config["debug_mode"] is True

        # Test setting configuration
        await app.call_service("config_service.set_config", key="new_setting", value="test_value")

        new_value = await app.call_service("config_service.get_config", key="new_setting")
        assert new_value == "test_value"

    @pytest.mark.asyncio
    async def test_multi_level_service_dependencies(self) -> None:
        """Test multi-level service dependencies."""
        app = (
            PluggingerAppBuilder()
            .register_plugin(ConfigServicePlugin)
            .register_plugin(DatabaseServicePlugin)
            .register_plugin(ApiServicePlugin)
            .build()
        )

        # Test that all services are available
        services = app.list_services()

        # Config service
        assert "config_service.get_config" in services
        assert "config_service.set_config" in services

        # Database service
        assert "database_service.execute_query" in services
        assert "database_service.get_connection_info" in services

        # API service
        assert "api_service.process_request" in services
        assert "api_service.get_api_info" in services

    def test_builder_with_dependencies(self) -> None:
        """Test builder with complex dependencies."""
        # Test building app with multiple interdependent plugins
        builder = PluggingerAppBuilder()

        app = (
            builder.with_name("DependencyTestApp")
            .register_plugin(ConfigServicePlugin)
            .register_plugin(DatabaseServicePlugin)
            .register_plugin(ApiServicePlugin)
            .build()
        )

        assert app.app_name == "DependencyTestApp"

        # Verify builder state
        assert builder.is_built
        assert builder.plugin_count == 3

    def test_plugin_isolation(self) -> None:
        """Test that plugins are properly isolated."""
        app1 = PluggingerAppBuilder().register_plugin(ConfigServicePlugin).build()

        app2 = PluggingerAppBuilder().register_plugin(ConfigServicePlugin).build()

        # Apps should be independent
        assert app1 is not app2

        # Services should be independent
        services1 = app1.list_services()
        services2 = app2.list_services()

        assert services1 == services2  # Same service names
        # But different instances (can't easily test without internal access)

    @pytest.mark.asyncio
    async def test_service_error_isolation(self) -> None:
        """Test that service errors don't affect other services."""

        @plugin(name="failing_service", version="1.0.0")
        class FailingServicePlugin(PluginBase):
            def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
                super().__init__(app=app, **injected_dependencies)

            @service()
            async def failing_method(self) -> None:
                raise RuntimeError("Service failed")

            @service()
            async def working_method(self) -> str:
                return "success"

        app = (
            PluggingerAppBuilder()
            .register_plugin(ConfigServicePlugin)
            .register_plugin(FailingServicePlugin)
            .build()
        )

        # Working service should still work
        config = await app.call_service("config_service.get_config", key="debug_mode")
        assert config is True

        success = await app.call_service("failing_service.working_method")
        assert success == "success"

        # Failing service should fail
        # ServiceExecutionError wraps the original RuntimeError
        from plugginger.core.exceptions import ServiceExecutionError

        with pytest.raises(ServiceExecutionError):
            await app.call_service("failing_service.failing_method")

        # Other services should still work after failure
        config2 = await app.call_service("config_service.get_config", key="api_key")
        assert config2 == "test_api_key_123"

    def test_container_registration_types(self) -> None:
        """Test different container registration types."""
        container = DIContainer()

        # Test concrete instance registration
        container.register_concrete_instance(42)
        assert container.get(int) == 42

        # Test multiple registrations
        container.register_concrete_instance("test_string")
        assert container.get(str) == "test_string"
        assert container.get(int) == 42  # Previous registration still works

        # Test has() method
        assert container.has(int)
        assert container.has(str)
        assert not container.has(float)

    def test_container_clear_functionality(self) -> None:
        """Test container clear functionality."""
        container = DIContainer()

        # Register some instances
        container.register_concrete_instance(123)
        container.register_concrete_instance("test")

        assert container.has(int)
        assert container.has(str)

        # Clear container
        container.clear()

        assert not container.has(int)
        assert not container.has(str)

    def test_container_list_registrations(self) -> None:
        """Test container registration listing."""
        container = DIContainer()

        # Register instances
        container.register_concrete_instance(456)
        container.register_concrete_instance("hello")

        registrations = container.list_registrations()

        assert "int" in registrations
        assert "str" in registrations
        assert "(instance)" in registrations["int"]
        assert "(instance)" in registrations["str"]
