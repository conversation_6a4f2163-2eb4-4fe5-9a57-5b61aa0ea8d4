# tests/conftest.py

"""
Shared pytest configuration and fixtures.
"""

from __future__ import annotations

import asyncio
import os
import sys
from collections.abc import AsyncGenerator, Generator
from typing import Any

import pytest

# Add src to path for all tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an event loop for the test session."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        yield loop
    finally:
        loop.close()


@pytest.fixture
async def mock_app() -> AsyncGenerator[Any, None]:
    """Create a mock app instance for testing."""
    from plugginger.testing.mock_app import create_mock_app

    app = create_mock_app("TestApp")
    try:
        yield app
    finally:
        await app.shutdown()


@pytest.fixture
def event_collector() -> Any:
    """Create an event collector for testing."""
    from plugginger.testing.collectors import create_event_collector

    return create_event_collector()


@pytest.fixture
def service_collector() -> Any:
    """Create a service call collector for testing."""
    from plugginger.testing.collectors import create_service_call_collector

    return create_service_call_collector()


@pytest.fixture
async def test_fixture() -> AsyncGenerator[Any, None]:
    """Create a test fixture for comprehensive testing."""
    from plugginger.testing.helpers import create_test_fixture

    async with create_test_fixture() as fixture:
        yield fixture


@pytest.fixture
def sample_plugin_class() -> type:
    """Create a sample plugin class for testing."""
    from pydantic import BaseModel

    from plugginger.api.events import on_event
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.api.service import service

    @plugin(name="sample_plugin", version="1.0.0")
    class SamplePlugin(PluginBase):
        def __init__(self, **injected_dependencies: Any) -> None:
            super().__init__(**injected_dependencies)
            self.setup_called = False
            self.teardown_called = False
            self.events_received: list[tuple[str, dict[str, Any]]] = []

        async def setup(self, plugin_config: BaseModel) -> None:
            self.setup_called = True

        async def teardown(self) -> None:
            self.teardown_called = True

        @service(name="multiply")
        async def multiply_numbers(self, a: int, b: int) -> int:
            return a * b

        @service(name="greet")
        async def greet_user(self, name: str, greeting: str = "Hello") -> str:
            return f"{greeting}, {name}!"

        @on_event("user.created")
        async def on_user_created(self, event_data: dict[str, Any]) -> None:
            self.events_received.append(("user.created", event_data))

        @on_event("system.*")
        async def on_system_event(self, event_data: dict[str, Any], event_type: str) -> None:
            self.events_received.append((event_type, event_data))

    return SamplePlugin


@pytest.fixture
def dependency_container() -> Any:
    """Create a dependency container for testing."""
    from plugginger.implementations.container import DIContainer

    return DIContainer()


@pytest.fixture
def service_dispatcher() -> Any:
    """Create a service dispatcher for testing."""
    from plugginger.implementations.services import SimpleServiceDispatcher

    return SimpleServiceDispatcher()


@pytest.fixture
def event_dispatcher() -> Any:
    """Create an event dispatcher for testing."""
    from plugginger.core.config import EventListenerFaultPolicy
    from plugginger.implementations.events import SimpleEventDispatcher, SimpleEventFaultHandler

    # Create a fault handler for the dispatcher
    fault_handler = SimpleEventFaultHandler(policy=EventListenerFaultPolicy.LOG_AND_CONTINUE)
    return SimpleEventDispatcher(fault_handler=fault_handler)


# Pytest configuration
def pytest_configure(config: Any) -> None:
    """Configure pytest."""
    # Add custom markers
    config.addinivalue_line("markers", "unit: mark test as a unit test")
    config.addinivalue_line("markers", "integration: mark test as an integration test")
    config.addinivalue_line("markers", "e2e: mark test as an end-to-end test")
    config.addinivalue_line("markers", "slow: mark test as slow running")


def pytest_collection_modifyitems(config: Any, items: list[Any]) -> None:
    """Modify test collection."""
    # Auto-mark tests based on their location
    for item in items:
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
