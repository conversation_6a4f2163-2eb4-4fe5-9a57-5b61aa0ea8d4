# tests/unit/utils/test_graph.py

"""
Tests for dependency graph utilities.
"""

from __future__ import annotations

import os
import sys

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_dependency_node() -> bool:
    """Test DependencyNode functionality."""
    try:
        from plugginger.utils.graph import DependencyNode

        # Create node
        node = DependencyNode("test_plugin", {"version": "1.0.0"})
        assert node.name == "test_plugin"
        assert node.data == {"version": "1.0.0"}
        assert len(node.dependencies) == 0
        assert len(node.optional_dependencies) == 0
        assert len(node.dependents) == 0

        # Add dependencies
        node.add_dependency("database", optional=False)
        node.add_dependency("cache", optional=True)

        assert "database" in node.dependencies
        assert "cache" in node.optional_dependencies
        assert len(node.get_all_dependencies()) == 2

        # Add dependents
        node.add_dependent("web_api")
        assert "web_api" in node.dependents

        # Remove dependencies
        node.remove_dependency("cache")
        assert "cache" not in node.optional_dependencies

        print("✅ DependencyNode tests passed!")
        return True

    except Exception as e:
        print(f"❌ DependencyNode test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dependency_graph_basic() -> bool:
    """Test basic DependencyGraph functionality."""
    try:
        from plugginger._internal.graph import DependencyGraph

        # Create graph
        graph: DependencyGraph[str] = DependencyGraph()
        assert len(graph) == 0

        # Add nodes
        graph.add_node("plugin1")
        graph.add_node("plugin2")

        assert len(graph) == 2
        assert "plugin1" in graph
        assert "plugin2" in graph
        assert "plugin3" not in graph

        # Get all nodes
        all_nodes = graph.get_all_nodes()
        assert "plugin1" in all_nodes
        assert "plugin2" in all_nodes

        # Add dependency (plugin2 depends on plugin1)
        graph.add_dependency_edge("plugin1", "plugin2")

        # Check dependencies
        plugin2_deps = graph.get_prerequisites("plugin2")
        assert "plugin1" in plugin2_deps

        plugin1_dependents = graph.get_dependents("plugin1")
        assert "plugin2" in plugin1_dependents

        print("✅ DependencyGraph basic tests passed!")
        return True

    except Exception as e:
        print(f"❌ DependencyGraph basic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_topological_sort() -> bool:
    """Test topological sorting functionality."""
    try:
        from plugginger._internal.graph import DependencyGraph

        # Create graph with dependencies
        graph: DependencyGraph[str] = DependencyGraph()
        graph.add_node("database")
        graph.add_node("cache")
        graph.add_node("user_service")
        graph.add_node("web_api")

        # Set up dependencies: web_api -> user_service -> database, cache
        graph.add_dependency_edge("database", "user_service")
        graph.add_dependency_edge("cache", "user_service")
        graph.add_dependency_edge("user_service", "web_api")

        # Get load order
        load_order = graph.topological_sort()

        # Verify dependencies are loaded before dependents
        db_index = load_order.index("database")
        cache_index = load_order.index("cache")
        user_index = load_order.index("user_service")
        web_index = load_order.index("web_api")

        assert db_index < user_index
        assert cache_index < user_index
        assert user_index < web_index

        print("✅ Topological sort tests passed!")
        return True

    except Exception as e:
        print(f"❌ Topological sort test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_circular_dependency_detection() -> bool:
    """Test circular dependency detection."""
    try:
        from plugginger.utils.graph import DependencyGraph

        # Create graph with circular dependency
        graph = DependencyGraph()
        graph.add_node("plugin1")
        graph.add_node("plugin2")
        graph.add_node("plugin3")

        # Create circular dependency: plugin1 -> plugin2 -> plugin3 -> plugin1
        graph.add_dependency("plugin1", "plugin2")
        graph.add_dependency("plugin2", "plugin3")
        graph.add_dependency("plugin3", "plugin1")

        # Should detect circular dependency
        try:
            graph.detect_circular_dependencies()
            raise AssertionError("Should have detected circular dependency")
        except Exception as e:
            assert "Circular dependencies detected" in str(e)

        # Should also fail on topological sort
        try:
            graph.get_load_order()
            raise AssertionError("Should have failed due to circular dependency")
        except Exception as e:
            assert "Circular dependencies detected" in str(e)

        print("✅ Circular dependency detection tests passed!")
        return True

    except Exception as e:
        print(f"❌ Circular dependency detection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_transitive_dependencies() -> bool:
    """Test transitive dependency resolution."""
    try:
        from plugginger.utils.graph import DependencyGraph

        # Create graph with transitive dependencies
        graph = DependencyGraph()
        graph.add_node("A")
        graph.add_node("B")
        graph.add_node("C")
        graph.add_node("D")

        # A -> B -> C -> D
        graph.add_dependency("A", "B")
        graph.add_dependency("B", "C")
        graph.add_dependency("C", "D")

        # Get direct dependencies
        direct_deps = graph.get_dependencies("A", recursive=False)
        assert direct_deps == {"B"}

        # Get transitive dependencies
        transitive_deps = graph.get_dependencies("A", recursive=True)
        assert transitive_deps == {"B", "C", "D"}

        print("✅ Transitive dependencies tests passed!")
        return True

    except Exception as e:
        print(f"❌ Transitive dependencies test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_graph_validation() -> bool:
    """Test graph validation functionality."""
    try:
        from plugginger.utils.graph import DependencyGraph

        # Create valid graph
        graph = DependencyGraph()
        graph.add_node("plugin1")
        graph.add_node("plugin2")
        graph.add_dependency("plugin2", "plugin1")

        # Should validate successfully
        graph.validate_graph()

        # Create invalid graph with missing dependency
        graph2 = DependencyGraph()
        graph2.add_node("plugin1")
        # Manually add dependency to non-existent node
        graph2._nodes["plugin1"].add_dependency("missing_plugin")

        # Should fail validation
        try:
            graph2.validate_graph()
            raise AssertionError("Should have failed validation")
        except Exception as e:
            assert "doesn't exist" in str(e)

        print("✅ Graph validation tests passed!")
        return True

    except Exception as e:
        print(f"❌ Graph validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests() -> bool:
    """Run all graph tests."""
    tests = [
        test_dependency_node,
        test_dependency_graph_basic,
        test_topological_sort,
        test_circular_dependency_detection,
        test_transitive_dependencies,
        test_graph_validation,
    ]

    passed = 0
    failed = 0

    for test in tests:
        print(f"\n🧪 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            failed += 1

    print(f"\n📊 Test Results: {passed} passed, {failed} failed")
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    if success:
        print("\n🎉 All graph tests passed!")
    else:
        print("\n❌ Some graph tests failed!")
    exit(0 if success else 1)
