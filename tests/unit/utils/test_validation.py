# tests/unit/utils/test_validation.py

"""
Tests for validation utilities.
"""

from __future__ import annotations

import os
import sys

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_plugin_name_validation() -> bool:
    """Test plugin name validation."""
    try:
        from plugginger._internal.validation import validate_plugin_name

        # Valid names
        validate_plugin_name("database")
        validate_plugin_name("user_service")
        validate_plugin_name("web-api")
        validate_plugin_name("cache.redis")
        validate_plugin_name("MyPlugin123")

        # Invalid names
        test_cases = [
            ("", "empty string"),
            ("123invalid", "starts with number"),
            ("invalid space", "contains space"),
            ("invalid@char", "contains invalid character"),
            ("_private", "starts with underscore"),
            ("a" * 101, "too long"),
        ]

        for invalid_name, reason in test_cases:
            try:
                validate_plugin_name(invalid_name)
                raise AssertionError(f"Should have rejected '{invalid_name}' ({reason})")
            except Exception:
                pass  # Expected

        print("✅ Plugin name validation tests passed!")
        return True

    except Exception as e:
        print(f"❌ Plugin name validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_service_name_validation() -> bool:
    """Test service name validation."""
    try:
        from plugginger._internal.validation import validate_service_name

        # Valid names
        validate_service_name("get_user")
        validate_service_name("user.create")
        validate_service_name("api-endpoint")

        # Invalid names should raise exceptions
        try:
            validate_service_name("")
            raise AssertionError("Should have rejected empty string")
        except Exception:
            pass

        try:
            validate_service_name("123invalid")
            raise AssertionError("Should have rejected name starting with number")
        except Exception:
            pass

        print("✅ Service name validation tests passed!")
        return True

    except Exception as e:
        print(f"❌ Service name validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_event_pattern_validation() -> bool:
    """Test event pattern validation."""
    try:
        from plugginger._internal.validation import validate_event_pattern

        # Valid patterns
        validate_event_pattern("user.created")
        validate_event_pattern("user.*")
        validate_event_pattern("*.event")
        validate_event_pattern("system-event")

        # Invalid patterns should raise exceptions
        try:
            validate_event_pattern("")
            raise AssertionError("Should have rejected empty string")
        except Exception:
            pass

        print("✅ Event pattern validation tests passed!")
        return True

    except Exception as e:
        print(f"❌ Event pattern validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_version_validation() -> bool:
    """Test version string validation."""
    try:
        from plugginger._internal.validation import validate_version_string

        # Valid versions
        validate_version_string("1.0.0")
        validate_version_string("2.1.3")
        validate_version_string("1.0.0-alpha.1")
        validate_version_string("3.2.1-beta")

        # Invalid versions should raise exceptions
        invalid_versions = ["", "1.0", "1.0.0.0", "invalid", "1.0.0-"]

        for version in invalid_versions:
            try:
                validate_version_string(version)
                raise AssertionError(f"Should have rejected version '{version}'")
            except Exception:
                pass

        print("✅ Version validation tests passed!")
        return True

    except Exception as e:
        print(f"❌ Version validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_timeout_validation() -> bool:
    """Test timeout value validation."""
    try:
        from plugginger._internal.validation import validate_timeout_value

        # Valid timeouts
        validate_timeout_value(None)
        validate_timeout_value(1.0)
        validate_timeout_value(30)
        validate_timeout_value(3600)

        # Invalid timeouts should raise exceptions
        invalid_timeouts = [0, -1, 3601, "invalid"]

        for timeout in invalid_timeouts:
            try:
                validate_timeout_value(timeout)  # type: ignore
                raise AssertionError(f"Should have rejected timeout '{timeout}'")
            except Exception:
                pass

        print("✅ Timeout validation tests passed!")
        return True

    except Exception as e:
        print(f"❌ Timeout validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_method_signature_validation() -> bool:
    """Test method signature validation."""
    try:
        from plugginger._internal.validation import validate_method_signature

        # Valid async method
        async def valid_async_method(self, param: str) -> None:
            pass

        validate_method_signature(valid_async_method)

        # Invalid sync method
        def invalid_sync_method(self, param: str) -> None:
            pass

        try:
            validate_method_signature(invalid_sync_method)
            raise AssertionError("Should have rejected sync method")
        except Exception:
            pass

        # Method without self
        async def invalid_no_self(param: str) -> None:
            pass

        try:
            validate_method_signature(invalid_no_self)
            raise AssertionError("Should have rejected method without self")
        except Exception:
            pass

        print("✅ Method signature validation tests passed!")
        return True

    except Exception as e:
        print(f"❌ Method signature validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_utility_functions() -> bool:
    """Test utility functions."""
    try:
        from plugginger._internal.validation import is_valid_identifier, sanitize_name

        # Test is_valid_identifier
        assert is_valid_identifier("valid_name")
        assert is_valid_identifier("ValidName")
        assert not is_valid_identifier("123invalid")
        assert not is_valid_identifier("_private")
        assert not is_valid_identifier("invalid-name")

        # Test sanitize_name
        assert sanitize_name("valid_name") == "valid_name"
        assert sanitize_name("invalid-name") == "invalid_name"
        assert sanitize_name("123invalid") == "item_123invalid"

        print("✅ Utility functions tests passed!")
        return True

    except Exception as e:
        print(f"❌ Utility functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests() -> bool:
    """Run all validation tests."""
    tests = [
        test_plugin_name_validation,
        test_service_name_validation,
        test_event_pattern_validation,
        test_version_validation,
        test_timeout_validation,
        test_method_signature_validation,
        test_utility_functions,
    ]

    passed = 0
    failed = 0

    for test in tests:
        print(f"\n🧪 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            failed += 1

    print(f"\n📊 Test Results: {passed} passed, {failed} failed")
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    if success:
        print("\n🎉 All validation tests passed!")
    else:
        print("\n❌ Some validation tests failed!")
    exit(0 if success else 1)
