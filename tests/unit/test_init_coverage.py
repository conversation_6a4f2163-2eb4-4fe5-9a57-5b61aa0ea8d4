"""Test __init__.py coverage."""

def test_init_imports():
    """Test all __getattr__ imports in __init__.py."""
    import plugginger

    # Test all __getattr__ imports
    assert plugginger.DIContainer is not None
    assert plugginger.get_container is not None
    assert plugginger.reset_container is not None
    assert plugginger.EventListenerFaultPolicy is not None
    assert plugginger.PluggingerError is not None
    assert plugginger.ServiceNotFoundError is not None
    assert plugginger.EventListenerError is not None

    # Test invalid attribute
    try:
        _ = plugginger.NonExistentAttribute
        raise AssertionError("Should have raised AttributeError")
    except AttributeError as e:
        assert "has no attribute 'NonExistentAttribute'" in str(e)
