# tests/unit/core/test_core_comprehensive.py

"""
Comprehensive tests for core modules to achieve >92% coverage.
"""

from __future__ import annotations

import os
import sys

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


class TestConstants:
    """Test core constants module."""

    def test_constants_exist(self) -> None:
        """Test that all required constants exist."""
        from plugginger.core.constants import (
            DEFAULT_APP_NAME,
            EVENT_METADATA_KEY,
            PLUGIN_METADATA_KEY,
            SERVICE_METADATA_KEY,
        )

        assert isinstance(DEFAULT_APP_NAME, str)
        assert len(DEFAULT_APP_NAME) > 0

        assert isinstance(PLUGIN_METADATA_KEY, str)
        assert isinstance(SERVICE_METADATA_KEY, str)
        assert isinstance(EVENT_METADATA_KEY, str)

        # Ensure keys are unique
        keys = {PLUGIN_METADATA_KEY, SERVICE_METADATA_KEY, EVENT_METADATA_KEY}
        assert len(keys) == 3

    def test_constants_values(self) -> None:
        """Test constant values are reasonable."""
        from plugginger.core.constants import DEFAULT_APP_NAME

        assert DEFAULT_APP_NAME == "PluggingerApp"


class TestExceptions:
    """Test core exceptions module."""

    def test_base_exception(self) -> None:
        """Test PluggingerError base exception."""
        from plugginger.core.exceptions import PluggingerError

        # Test basic instantiation
        error = PluggingerError("test message")
        assert str(error) == "test message"
        assert isinstance(error, Exception)

    def test_configuration_error(self) -> None:
        """Test ConfigurationError."""
        from plugginger.core.exceptions import ConfigurationError, PluggingerError

        error = ConfigurationError("config error")
        assert str(error) == "config error"
        assert isinstance(error, PluggingerError)

    def test_plugin_registration_error(self) -> None:
        """Test PluginRegistrationError."""
        from plugginger.core.exceptions import PluggingerError, PluginRegistrationError

        error = PluginRegistrationError("plugin error")
        assert str(error) == "plugin error"
        assert isinstance(error, PluggingerError)

    def test_service_definition_error(self) -> None:
        """Test ServiceDefinitionError."""
        from plugginger.core.exceptions import PluggingerError, ServiceDefinitionError

        error = ServiceDefinitionError("service error")
        assert str(error) == "service error"
        assert isinstance(error, PluggingerError)

    def test_service_not_found_error(self) -> None:
        """Test ServiceNotFoundError."""
        from plugginger.core.exceptions import PluggingerError, ServiceNotFoundError

        error = ServiceNotFoundError("service not found")
        assert str(error) == "service not found"
        assert isinstance(error, PluggingerError)

    def test_service_execution_error(self) -> None:
        """Test ServiceExecutionError."""
        from plugginger.core.exceptions import PluggingerError, ServiceExecutionError

        error = ServiceExecutionError("execution error")
        assert str(error) == "execution error"
        assert isinstance(error, PluggingerError)

    def test_event_definition_error(self) -> None:
        """Test EventDefinitionError."""
        from plugginger.core.exceptions import EventDefinitionError, PluggingerError

        error = EventDefinitionError("event error")
        assert str(error) == "event error"
        assert isinstance(error, PluggingerError)

    def test_dependency_error(self) -> None:
        """Test DependencyError."""
        from plugginger.core.exceptions import DependencyError, PluggingerError

        error = DependencyError("dependency error")
        assert str(error) == "dependency error"
        assert isinstance(error, PluggingerError)

    def test_missing_dependency_error(self) -> None:
        """Test MissingDependencyError."""
        from plugginger.core.exceptions import DependencyError, MissingDependencyError

        error = MissingDependencyError("missing dependency")
        assert str(error) == "missing dependency"
        assert isinstance(error, DependencyError)

    def test_circular_dependency_error(self) -> None:
        """Test CircularDependencyError."""
        from plugginger.core.exceptions import CircularDependencyError, DependencyError

        error = CircularDependencyError("circular dependency")
        assert str(error) == "circular dependency"
        assert isinstance(error, DependencyError)

    def test_background_task_error(self) -> None:
        """Test BackgroundTaskError."""
        from plugginger.core.exceptions import BackgroundTaskError, PluggingerError

        error = BackgroundTaskError("task error")
        assert str(error) == "task error"
        assert isinstance(error, PluggingerError)

    def test_validation_error(self) -> None:
        """Test ValidationError."""
        from plugginger.core.exceptions import PluggingerError, ValidationError

        error = ValidationError("validation error")
        assert str(error) == "validation error"
        assert isinstance(error, PluggingerError)


class TestTypes:
    """Test core types module."""

    def test_type_aliases_exist(self) -> None:
        """Test that type aliases are defined."""
        from plugginger.core.types import (
            EventHandlerType,
            EventPatternInput,
            EventType,
            PluginInstanceId,
            ServiceMethodType,
            ServiceName,
        )

        # These should be importable without error
        assert EventHandlerType is not None
        assert EventPatternInput is not None
        assert EventType is not None
        assert PluginInstanceId is not None
        assert ServiceMethodType is not None
        assert ServiceName is not None

    def test_type_annotations(self) -> None:
        """Test type annotations work correctly."""
        from plugginger.core.types import PluginInstanceId, ServiceName

        # Test that we can use these as type annotations
        plugin_id: PluginInstanceId = "test_plugin_001"
        service_name: ServiceName = "test.service"

        assert plugin_id == "test_plugin_001"
        assert service_name == "test.service"


class TestConfig:
    """Test core config module."""

    def test_log_level_enum(self) -> None:
        """Test LogLevel enum."""
        from plugginger.core.config import LogLevel

        # Test all log levels exist
        assert LogLevel.DEBUG
        assert LogLevel.INFO
        assert LogLevel.WARNING
        assert LogLevel.ERROR
        assert LogLevel.CRITICAL

        # Test values
        assert LogLevel.DEBUG.value == "DEBUG"
        assert LogLevel.INFO.value == "INFO"
        assert LogLevel.WARNING.value == "WARNING"
        assert LogLevel.ERROR.value == "ERROR"
        assert LogLevel.CRITICAL.value == "CRITICAL"

    def test_default_app_name_import(self) -> None:
        """Test DEFAULT_APP_NAME can be imported from config."""
        from plugginger.core.config import DEFAULT_APP_NAME

        assert isinstance(DEFAULT_APP_NAME, str)
        assert len(DEFAULT_APP_NAME) > 0


class TestCoreIntegration:
    """Test integration between core modules."""

    def test_exception_hierarchy(self) -> None:
        """Test that exception hierarchy is correct."""
        from plugginger.core.exceptions import (
            CircularDependencyError,
            ConfigurationError,
            DependencyError,
            MissingDependencyError,
            PluggingerError,
        )

        # Test inheritance chain
        assert issubclass(ConfigurationError, PluggingerError)
        assert issubclass(DependencyError, PluggingerError)
        assert issubclass(MissingDependencyError, DependencyError)
        assert issubclass(CircularDependencyError, DependencyError)

    def test_constants_in_exceptions(self) -> None:
        """Test that constants can be used with exceptions."""
        from plugginger.core.constants import DEFAULT_APP_NAME
        from plugginger.core.exceptions import ConfigurationError

        error = ConfigurationError(f"Invalid app name: {DEFAULT_APP_NAME}")
        assert DEFAULT_APP_NAME in str(error)

    def test_types_with_exceptions(self) -> None:
        """Test that types work with exceptions."""
        from plugginger.core.exceptions import ServiceNotFoundError
        from plugginger.core.types import ServiceName

        service_name: ServiceName = "test.service"
        error = ServiceNotFoundError(f"Service not found: {service_name}")
        assert service_name in str(error)

    def test_config_with_types(self) -> None:
        """Test that config works with types."""
        from plugginger.core.config import LogLevel
        from plugginger.core.types import ServiceName

        # This should work without type errors
        service_name: ServiceName = "logging.service"
        log_level = LogLevel.INFO

        assert isinstance(service_name, str)
        assert isinstance(log_level, LogLevel)


class TestErrorMessages:
    """Test error message formatting."""

    def test_error_message_formatting(self) -> None:
        """Test that error messages are properly formatted."""
        from plugginger.core.exceptions import (
            ConfigurationError,
            PluginRegistrationError,
            ServiceNotFoundError,
        )

        # Test with various message formats
        config_error = ConfigurationError("Configuration is invalid")
        assert "Configuration is invalid" in str(config_error)

        plugin_error = PluginRegistrationError("Plugin 'test' already exists")
        assert "Plugin 'test' already exists" in str(plugin_error)

        service_error = ServiceNotFoundError("Service 'test.service' not found")
        assert "Service 'test.service' not found" in str(service_error)

    def test_error_with_none_message(self) -> None:
        """Test error handling with None message."""
        from plugginger.core.exceptions import PluggingerError

        # Should handle None gracefully
        error = PluggingerError(None)  # type: ignore
        assert str(error) == "None"

    def test_error_with_empty_message(self) -> None:
        """Test error handling with empty message."""
        from plugginger.core.exceptions import PluggingerError

        error = PluggingerError("")
        assert str(error) == ""


class TestModuleImports:
    """Test that all modules can be imported correctly."""

    def test_core_module_imports(self) -> None:
        """Test that core modules can be imported."""
        # These should not raise ImportError
        import plugginger.core.config
        import plugginger.core.constants
        import plugginger.core.exceptions
        import plugginger.core.types

        assert plugginger.core.config is not None
        assert plugginger.core.constants is not None
        assert plugginger.core.exceptions is not None
        assert plugginger.core.types is not None

    def test_selective_imports(self) -> None:
        """Test selective imports from core modules."""
        from plugginger.core.config import DEFAULT_APP_NAME, LogLevel
        from plugginger.core.constants import PLUGIN_METADATA_KEY
        from plugginger.core.exceptions import PluggingerError
        from plugginger.core.types import ServiceName

        # All should be importable
        assert DEFAULT_APP_NAME is not None
        assert LogLevel is not None
        assert PLUGIN_METADATA_KEY is not None
        assert PluggingerError is not None
        assert ServiceName is not None
