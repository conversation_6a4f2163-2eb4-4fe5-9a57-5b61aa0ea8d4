# tests/unit/implementations/test_container.py

"""
Tests for the DI Container implementation.
"""

import os
import sys

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_di_container_basic() -> bool:
    """Test basic DI container functionality."""
    try:
        from typing import Protocol, cast

        from plugginger.implementations.container import DIContainer

        # Define a simple interface
        class TestInterface(Protocol):
            def get_value(self) -> str: ...

        # Define an implementation
        class TestImplementation:
            def get_value(self) -> str:
                return "test_value"

        # Test container
        container = DIContainer()

        # Register service
        container.register(cast(type, TestInterface), TestImplementation)

        # Get instance
        instance: TestInterface = container.get(cast(type[TestInterface], TestInterface))
        assert instance.get_value() == "test_value"

        # Test singleton behavior
        instance2: TestInterface = container.get(cast(type[TestInterface], TestInterface))
        assert instance is instance2  # Same instance

        print("✅ DI Container basic tests passed!")
        return True

    except Exception as e:
        print(f"❌ DI Container test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_di_container_instance_registration() -> bool:
    """Test instance registration."""
    try:
        from typing import Protocol, cast

        from plugginger.implementations.container import DIContainer

        # Define interface
        class TestInterface(Protocol):
            def get_value(self) -> str: ...

        # Create instance
        class TestImplementation:
            def __init__(self, value: str):
                self.value = value
            def get_value(self) -> str:
                return self.value

        instance = TestImplementation("pre_created")

        # Test container
        container = DIContainer()
        container.register_instance(cast(type, TestInterface), instance)

        # Get instance
        retrieved: TestInterface = container.get(cast(type[TestInterface], TestInterface))
        assert retrieved is instance
        assert retrieved.get_value() == "pre_created"

        print("✅ DI Container instance registration tests passed!")
        return True

    except Exception as e:
        print(f"❌ DI Container instance registration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_di_container_error_handling() -> bool:
    """Test error handling."""
    try:
        from typing import Protocol, cast

        from plugginger.implementations.container import DIContainer

        # Define interface
        class TestInterface(Protocol):
            def get_value(self) -> str: ...

        container = DIContainer()

        # Test unregistered interface
        try:
            container.get(cast(type, TestInterface))
            raise AssertionError("Should have raised ValueError")
        except ValueError as e:
            assert "not registered" in str(e)

        # Test has method
        assert not container.has(cast(type, TestInterface))

        print("✅ DI Container error handling tests passed!")
        return True

    except Exception as e:
        print(f"❌ DI Container error handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests() -> bool:
    """Run all DI container tests."""
    from collections.abc import Callable

    tests: list[Callable[[], bool]] = [
        test_di_container_basic,
        test_di_container_instance_registration,
        test_di_container_error_handling,
    ]

    passed = 0
    failed = 0

    for test in tests:
        print(f"\n🧪 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            failed += 1

    print(f"\n📊 Test Results: {passed} passed, {failed} failed")
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    if success:
        print("\n🎉 All DI Container tests passed!")
    else:
        print("\n❌ Some DI Container tests failed!")
    exit(0 if success else 1)
