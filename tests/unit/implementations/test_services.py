# tests/unit/implementations/test_services.py

"""
Tests for the Service implementations.
"""

import asyncio
import os
import sys

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_service_dispatcher() -> bool:
    """Test service dispatcher functionality."""

    async def async_test() -> bool:
        try:
            from plugginger.implementations.services import SimpleServiceDispatcher

            dispatcher = SimpleServiceDispatcher()

            # Define a test service
            async def test_service(name: str) -> str:
                return f"Hello, {name}!"

            # Register service
            dispatcher.add_service("test.greet", test_service)

            # Test service call
            result = await dispatcher.call_service("test.greet", "World")
            assert result == "Hello, World!"

            # Test service listing
            services = dispatcher.list_services()
            assert "test.greet" in services

            # Test has_service
            assert dispatcher.has_service("test.greet")
            assert not dispatcher.has_service("nonexistent.service")

            print("✅ Service dispatcher tests passed!")
            return True

        except Exception as e:
            print(f"❌ Service dispatcher test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_service_registry() -> bool:
    """Test service registry functionality."""

    async def async_test() -> bool:
        try:
            from plugginger.implementations.services import (
                SimpleServiceDispatcher,
                SimpleServiceRegistry,
            )

            dispatcher = SimpleServiceDispatcher()
            registry = SimpleServiceRegistry(dispatcher)

            # Define a test service
            async def test_service(value: int) -> int:
                return value * 2

            # Register service through registry
            service_name = registry.register_service("test_plugin", "double", test_service)
            assert service_name == "test_plugin.double"

            # Test service call through dispatcher
            result = await dispatcher.call_service("test_plugin.double", 21)
            assert result == 42

            # Test plugin service tracking
            plugin_services = registry.get_plugin_services("test_plugin")
            assert "test_plugin.double" in plugin_services

            # Test custom service name
            custom_service_name = registry.register_service(
                "test_plugin", "multiply", test_service, custom_name="custom_double"
            )
            assert custom_service_name == "test_plugin.custom_double"

            # Test unregistering plugin services
            unregistered = registry.unregister_plugin_services("test_plugin")
            assert len(unregistered) == 2
            assert "test_plugin.double" in unregistered
            assert "test_plugin.custom_double" in unregistered

            print("✅ Service registry tests passed!")
            return True

        except Exception as e:
            print(f"❌ Service registry test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_service_error_handling() -> bool:
    """Test service error handling."""

    async def async_test() -> bool:
        try:
            from plugginger.implementations.services import SimpleServiceDispatcher

            dispatcher = SimpleServiceDispatcher()

            # Test calling non-existent service
            try:
                await dispatcher.call_service("nonexistent.service")
                raise AssertionError("Should have raised ServiceNotFoundError")
            except Exception as e:
                assert "not found" in str(e)

            # Test service that raises exception
            async def failing_service() -> str:
                raise ValueError("Service failed")

            dispatcher.add_service("test.failing", failing_service)

            try:
                await dispatcher.call_service("test.failing")
                raise AssertionError("Should have raised ServiceExecutionError")
            except Exception as e:
                assert "Error executing service" in str(e)

            # Test duplicate service registration
            async def duplicate_service() -> str:
                return "duplicate"

            dispatcher.add_service("test.duplicate", duplicate_service)

            try:
                dispatcher.add_service("test.duplicate", duplicate_service)
                raise AssertionError("Should have raised ServiceNameConflictError")
            except Exception as e:
                assert "already registered" in str(e)

            print("✅ Service error handling tests passed!")
            return True

        except Exception as e:
            print(f"❌ Service error handling test failed: {e}")
            import traceback

            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def run_all_tests() -> bool:
    """Run all service tests."""
    tests = [
        test_service_dispatcher,
        test_service_registry,
        test_service_error_handling,
    ]

    passed = 0
    failed = 0

    for test in tests:
        print(f"\n🧪 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            failed += 1

    print(f"\n📊 Test Results: {passed} passed, {failed} failed")
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    if success:
        print("\n🎉 All service tests passed!")
    else:
        print("\n❌ Some service tests failed!")
    exit(0 if success else 1)
