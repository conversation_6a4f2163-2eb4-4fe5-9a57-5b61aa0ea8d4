"""Test advanced DI container functionality with constructor injection."""

from typing import Protocol, cast

import pytest

from plugginger.core.exceptions import (
    DependencyResolutionError,
    MissingTypeAnnotationForDIError,
)
from plugginger.implementations.container import DIContainer


# Test interfaces and implementations
class DatabaseInterface(Protocol):
    """Database interface for testing."""

    def query(self, sql: str) -> str:
        """Execute a query."""
        ...


class LoggerInterface(Protocol):
    """Logger interface for testing."""

    def log(self, message: str) -> None:
        """Log a message."""
        ...


class SimpleDatabase:
    """Simple database implementation."""

    def __init__(self) -> None:
        """Initialize database."""
        pass

    def query(self, sql: str) -> str:
        """Execute a query."""
        return f"Result for: {sql}"


class SimpleLogger:
    """Simple logger implementation."""

    def __init__(self) -> None:
        """Initialize logger."""
        pass

    def log(self, message: str) -> None:
        """Log a message."""
        print(f"LOG: {message}")


class ServiceWithDependencies:
    """Service that requires dependencies."""

    def __init__(self, database: DatabaseInterface, logger: LoggerInterface) -> None:
        """Initialize with dependencies."""
        self.database = database
        self.logger = logger

    def process(self, data: str) -> str:
        """Process data using dependencies."""
        result = self.database.query(f"SELECT * FROM data WHERE value='{data}'")
        self.logger.log(f"Processed: {data}")
        return result


class ServiceWithOptionalDependency:
    """Service with optional dependency."""

    def __init__(self, database: DatabaseInterface, debug: bool = False) -> None:
        """Initialize with required and optional dependencies."""
        self.database = database
        self.debug = debug


class ServiceWithoutDependencies:
    """Service without constructor dependencies."""

    def __init__(self) -> None:
        """Initialize without dependencies."""
        self.value = "initialized"


class ServiceWithMissingAnnotation:
    """Service with missing type annotation."""

    def __init__(self, database) -> None:  # type: ignore[no-untyped-def]
        """Initialize with untyped dependency."""
        self.database = database


class ServiceWithOptionalUntypedParam:
    """Service with optional untyped parameter."""

    def __init__(self, database: DatabaseInterface, config=None) -> None:  # type: ignore[no-untyped-def]
        """Initialize with typed and untyped parameters."""
        self.database = database
        self.config = config


class RepositoryService:
    """Repository service that depends on database."""

    def __init__(self, database: DatabaseInterface) -> None:
        """Initialize with database dependency."""
        self.database = database

    def save(self, data: str) -> str:
        """Save data using database."""
        return self.database.query(f"INSERT INTO table VALUES ('{data}')")


class BusinessService:
    """Business service that depends on repository and logger."""

    def __init__(self, repository: RepositoryService, logger: LoggerInterface) -> None:
        """Initialize with repository and logger dependencies."""
        self.repository = repository
        self.logger = logger

    def process_business_logic(self, data: str) -> str:
        """Process business logic using dependencies."""
        self.logger.log(f"Processing business logic for: {data}")
        result = self.repository.save(data)
        self.logger.log("Business logic completed")
        return result


class TestDIContainerAdvanced:
    """Test advanced DI container functionality."""

    def test_simple_instantiation_without_dependencies(self) -> None:
        """Test instantiation of class without dependencies."""
        container = DIContainer()

        # Register service without dependencies
        container.register(ServiceWithoutDependencies, ServiceWithoutDependencies)

        # Get instance
        instance = container.get(ServiceWithoutDependencies)

        assert isinstance(instance, ServiceWithoutDependencies)
        assert instance.value == "initialized"

    def test_single_dependency_injection(self) -> None:
        """Test injection of single dependency."""
        container = DIContainer()

        # Register dependencies
        container.register(cast(type[DatabaseInterface], DatabaseInterface), SimpleDatabase)

        # Register service with dependency
        container.register(cast(type[SimpleDatabase], SimpleDatabase), SimpleDatabase)

        # Get instance
        instance = container.get(SimpleDatabase)

        assert isinstance(instance, SimpleDatabase)
        assert instance.query("test") == "Result for: test"

    def test_multiple_dependency_injection(self) -> None:
        """Test injection of multiple dependencies."""
        container = DIContainer()

        # Register dependencies
        container.register(cast(type[DatabaseInterface], DatabaseInterface), SimpleDatabase)
        container.register(cast(type[LoggerInterface], LoggerInterface), SimpleLogger)

        # Register service with multiple dependencies
        container.register(ServiceWithDependencies, ServiceWithDependencies)

        # Get instance
        instance = container.get(ServiceWithDependencies)

        assert isinstance(instance, ServiceWithDependencies)
        assert isinstance(instance.database, SimpleDatabase)
        assert isinstance(instance.logger, SimpleLogger)

        # Test functionality
        result = instance.process("test_data")
        assert "test_data" in result

    def test_optional_dependency_with_default(self) -> None:
        """Test handling of optional dependencies with default values."""
        container = DIContainer()

        # Register only required dependency
        container.register(cast(type[DatabaseInterface], DatabaseInterface), SimpleDatabase)

        # Register service with optional dependency
        container.register(ServiceWithOptionalDependency, ServiceWithOptionalDependency)

        # Get instance
        instance = container.get(ServiceWithOptionalDependency)

        assert isinstance(instance, ServiceWithOptionalDependency)
        assert isinstance(instance.database, SimpleDatabase)
        assert instance.debug is False  # Default value used

    def test_singleton_behavior(self) -> None:
        """Test that singleton instances are reused."""
        container = DIContainer()

        # Register as singleton (default)
        container.register(ServiceWithoutDependencies, ServiceWithoutDependencies, singleton=True)

        # Get multiple instances
        instance1 = container.get(ServiceWithoutDependencies)
        instance2 = container.get(ServiceWithoutDependencies)

        # Should be the same instance
        assert instance1 is instance2

    def test_non_singleton_behavior(self) -> None:
        """Test that non-singleton instances are created each time."""
        container = DIContainer()

        # Register as non-singleton
        container.register(ServiceWithoutDependencies, ServiceWithoutDependencies, singleton=False)

        # Get multiple instances
        instance1 = container.get(ServiceWithoutDependencies)
        instance2 = container.get(ServiceWithoutDependencies)

        # Should be different instances
        assert instance1 is not instance2
        assert isinstance(instance1, ServiceWithoutDependencies)
        assert isinstance(instance2, ServiceWithoutDependencies)

    def test_missing_dependency_error(self) -> None:
        """Test error when required dependency is not registered."""
        container = DIContainer()

        # Register service but not its dependencies
        container.register(ServiceWithDependencies, ServiceWithDependencies)

        # Should raise DependencyResolutionError
        with pytest.raises(DependencyResolutionError) as exc_info:
            container.get(ServiceWithDependencies)

        error = exc_info.value
        assert error.target_class == "ServiceWithDependencies"
        assert error.dependency_type == "DatabaseInterface"
        assert error.parameter_name == "database"

    def test_missing_type_annotation_error(self) -> None:
        """Test error when required parameter lacks type annotation."""
        container = DIContainer()

        # Register service with missing type annotation
        container.register(cast(type[ServiceWithMissingAnnotation], ServiceWithMissingAnnotation), ServiceWithMissingAnnotation)

        # Should raise MissingTypeAnnotationForDIError
        with pytest.raises(MissingTypeAnnotationForDIError) as exc_info:
            container.get(ServiceWithMissingAnnotation)

        error = exc_info.value
        assert error.class_name == "ServiceWithMissingAnnotation"
        assert error.parameter_name == "database"

    def test_optional_untyped_parameter_ignored(self) -> None:
        """Test that optional untyped parameters are ignored."""
        container = DIContainer()

        # Register dependencies
        container.register(cast(type[DatabaseInterface], DatabaseInterface), SimpleDatabase)

        # Register service with optional untyped parameter
        container.register(cast(type[ServiceWithOptionalUntypedParam], ServiceWithOptionalUntypedParam), ServiceWithOptionalUntypedParam)

        # Should work - untyped optional parameter is ignored
        instance = container.get(ServiceWithOptionalUntypedParam)

        assert isinstance(instance, ServiceWithOptionalUntypedParam)
        assert isinstance(instance.database, SimpleDatabase)
        assert instance.config is None  # Default value used

    def test_recursive_dependency_resolution(self) -> None:
        """Test recursive dependency resolution (multi-level dependencies)."""
        container = DIContainer()

        # Register all dependencies
        container.register(cast(type[DatabaseInterface], DatabaseInterface), SimpleDatabase)
        container.register(cast(type[LoggerInterface], LoggerInterface), SimpleLogger)
        container.register(cast(type[RepositoryService], RepositoryService), RepositoryService)
        container.register(cast(type[BusinessService], BusinessService), BusinessService)

        # Get instance with multi-level dependencies
        instance = container.get(BusinessService)

        # Verify the entire dependency chain
        assert isinstance(instance, BusinessService)
        assert isinstance(instance.repository, RepositoryService)
        assert isinstance(instance.logger, SimpleLogger)
        assert isinstance(instance.repository.database, SimpleDatabase)

        # Test functionality
        result = instance.process_business_logic("test_data")
        assert "test_data" in result
        assert "INSERT INTO table" in result

    def test_dependency_chain_with_singletons(self) -> None:
        """Test that singletons are reused in dependency chains."""
        container = DIContainer()

        # Register dependencies as singletons
        container.register(cast(type[DatabaseInterface], DatabaseInterface), SimpleDatabase, singleton=True)
        container.register(cast(type[LoggerInterface], LoggerInterface), SimpleLogger, singleton=True)
        container.register(cast(type[RepositoryService], RepositoryService), RepositoryService, singleton=True)
        container.register(cast(type[BusinessService], BusinessService), BusinessService, singleton=True)

        # Get multiple instances
        business1 = container.get(BusinessService)
        business2 = container.get(BusinessService)

        # Should be the same instances throughout the chain
        assert business1 is business2
        assert business1.repository is business2.repository
        assert business1.logger is business2.logger
        assert business1.repository.database is business2.repository.database
