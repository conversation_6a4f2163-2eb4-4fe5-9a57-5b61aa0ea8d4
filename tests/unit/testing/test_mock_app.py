# tests/unit/testing/test_mock_app.py

"""
Tests for the mock application components.
"""

from __future__ import annotations

import asyncio
import os
import sys

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_mock_service_dispatcher() -> bool:
    """Test MockServiceDispatcher functionality."""
    async def async_test() -> bool:
        try:
            from plugginger.testing.mock_app import MockServiceDispatcher

            # Create dispatcher
            dispatcher = MockServiceDispatcher()

            # Test service registration
            async def test_service(value: int) -> int:
                return value * 2

            dispatcher.add_service("test.service", test_service)
            assert dispatcher.has_service("test.service")
            assert "test.service" in dispatcher.list_services()

            # Test service calling
            result = await dispatcher.call_service("test.service", 5)
            assert result == 10

            # Test call history
            assert dispatcher.was_called("test.service")
            assert dispatcher.call_count("test.service") == 1

            history = dispatcher.get_call_history()
            assert len(history) == 1
            assert history[0][0] == "test.service"
            assert history[0][1] == (5,)

            # Test unknown service
            result = await dispatcher.call_service("unknown.service", 42)
            assert result == "mock_result_for_unknown.service"

            print("✅ MockServiceDispatcher tests passed!")
            return True

        except Exception as e:
            print(f"❌ MockServiceDispatcher test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_mock_event_dispatcher() -> bool:
    """Test MockEventDispatcher functionality."""
    async def async_test() -> bool:
        try:
            from plugginger.testing.mock_app import MockEventDispatcher

            # Create dispatcher
            dispatcher = MockEventDispatcher()

            # Test listener registration
            events_received = []

            async def test_listener(event_data: dict, event_type: str) -> None:
                events_received.append((event_type, event_data))

            dispatcher.add_listener("test.*", test_listener)
            assert "test.*" in dispatcher.list_patterns()

            # Test event emission
            await dispatcher.emit_event("test.event", {"data": "value"})

            # Check event history
            assert dispatcher.was_emitted("test.event")
            assert dispatcher.emission_count("test.event") == 1

            history = dispatcher.get_event_history()
            assert len(history) == 1
            assert history[0][0] == "test.event"
            assert history[0][1] == {"data": "value"}

            # Check listener was called
            assert len(events_received) == 1
            assert events_received[0] == ("test.event", {"data": "value"})

            print("✅ MockEventDispatcher tests passed!")
            return True

        except Exception as e:
            print(f"❌ MockEventDispatcher test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_mock_app_instance() -> bool:
    """Test MockPluggingerAppInstance functionality."""
    async def async_test() -> bool:
        try:
            from plugginger.testing.mock_app import MockPluggingerAppInstance

            # Create mock app
            app = MockPluggingerAppInstance("TestApp")
            assert app.app_name == "TestApp"
            assert not app.is_shutdown
            assert app.task_count == 0

            # Test service calling
            app.service_dispatcher.add_service("test.service", lambda x: x * 2)
            result = await app.call_service("test.service", 5)
            assert result == 10

            # Test event emission
            await app.emit_event("test.event", {"data": "value"})
            assert app.event_dispatcher.was_emitted("test.event")

            # Test managed tasks
            async def test_task() -> str:
                await asyncio.sleep(0.01)
                return "completed"

            task = app.create_managed_task(test_task(), name="test")
            assert app.task_count == 1

            result = await task
            assert result == "completed"

            # Wait for cleanup
            await asyncio.sleep(0.01)
            assert app.task_count == 0

            # Test shutdown
            await app.shutdown()
            assert app.is_shutdown

            print("✅ MockPluggingerAppInstance tests passed!")
            return True

        except Exception as e:
            print(f"❌ MockPluggingerAppInstance test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_create_mock_app() -> bool:
    """Test create_mock_app convenience function."""
    try:
        from plugginger.testing.mock_app import create_mock_app

        # Test default name
        app1 = create_mock_app()
        assert app1.app_name == "MockApp"

        # Test custom name
        app2 = create_mock_app("CustomApp")
        assert app2.app_name == "CustomApp"

        print("✅ create_mock_app tests passed!")
        return True

    except Exception as e:
        print(f"❌ create_mock_app test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests() -> bool:
    """Run all mock app tests."""
    tests = [
        test_mock_service_dispatcher,
        test_mock_event_dispatcher,
        test_mock_app_instance,
        test_create_mock_app,
    ]

    passed = 0
    failed = 0

    for test in tests:
        print(f"\n🧪 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            failed += 1

    print(f"\n📊 Test Results: {passed} passed, {failed} failed")
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    if success:
        print("\n🎉 All mock app tests passed!")
    else:
        print("\n❌ Some mock app tests failed!")
    exit(0 if success else 1)
