# tests/unit/api/test_builder_simple.py

"""
Simplified tests for the App Builder API.
"""

from __future__ import annotations

import os
import sys

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_builder_basic() -> bool:
    """Test basic builder functionality."""
    try:
        from plugginger.api.builder import PluggingerAppBuilder

        # Create builder
        builder = PluggingerAppBuilder()

        # Test basic properties
        assert builder.app_name == "PluggingerApp"  # Default name
        assert builder.plugin_count == 0
        assert not builder.is_built

        # Test with_name
        result = builder.with_name("TestApp")
        assert result is builder  # Should return self for chaining
        assert builder.app_name == "TestApp"

        print("✅ Builder basic tests passed!")
        return True

    except Exception as e:
        print(f"❌ Builder basic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_convenience_function() -> bool:
    """Test convenience function for app creation."""
    try:
        from plugginger.api.builder import create_app

        # Test convenience function with default name
        app = create_app()
        assert app.app_name == "PluggingerApp"
        assert not app.is_shutdown

        # Test with custom name
        app_custom = create_app("CustomApp")
        assert app_custom.app_name == "CustomApp"

        print("✅ Convenience function tests passed!")
        return True

    except Exception as e:
        print(f"❌ Convenience function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests() -> bool:
    """Run all simplified builder tests."""
    tests = [
        test_builder_basic,
        test_convenience_function,
    ]

    passed = 0
    failed = 0

    for test in tests:
        print(f"\n🧪 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            failed += 1

    print(f"\n📊 Test Results: {passed} passed, {failed} failed")
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    if success:
        print("\n🎉 All simplified builder tests passed!")
    else:
        print("\n❌ Some simplified builder tests failed!")
    exit(0 if success else 1)
