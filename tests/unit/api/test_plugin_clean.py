# tests/unit/api/test_plugin_clean.py

"""
Clean pytest-compatible tests for the Plugin API (V6.0).
"""

from __future__ import annotations

import os
import sys

import pytest

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_plugin_decorator() -> None:
    """Test @plugin decorator functionality."""
    from plugginger.api.plugin import PluginBase, get_plugin_metadata, is_plugin_class, plugin

    # Test basic plugin decoration
    @plugin(name="test_plugin", version="1.2.3")
    class TestPlugin(PluginBase):
        pass

    # Test metadata extraction
    metadata = get_plugin_metadata(TestPlugin)
    assert metadata["name"] == "test_plugin"
    assert metadata["version"] == "1.2.3"
    assert metadata["class_name"] == "TestPlugin"

    # Test is_plugin_class
    assert is_plugin_class(TestPlugin)


def test_plugin_validation_invalid_base() -> None:
    """Test plugin validation for invalid base class."""
    from plugginger.api.plugin import plugin
    from plugginger.core.exceptions import PluginRegistrationError

    with pytest.raises(PluginRegistrationError, match="must inherit from PluginBase"):

        @plugin(name="invalid", version="1.0.0")
        class InvalidPlugin:
            pass


def test_plugin_validation_invalid_name() -> None:
    """Test plugin validation for invalid names."""
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.core.exceptions import PluginRegistrationError

    # Test invalid identifier
    with pytest.raises(PluginRegistrationError, match="must be a valid Python identifier"):

        @plugin(name="invalid-name", version="1.0.0")
        class InvalidNamePlugin(PluginBase):
            pass


def test_plugin_validation_invalid_version() -> None:
    """Test plugin validation for invalid versions."""
    from plugginger.api.plugin import PluginBase, plugin
    from plugginger.core.exceptions import PluginRegistrationError

    # Test empty version
    with pytest.raises(PluginRegistrationError, match="must be a non-empty string"):

        @plugin(name="test_plugin", version="")
        class EmptyVersionPlugin(PluginBase):
            pass
