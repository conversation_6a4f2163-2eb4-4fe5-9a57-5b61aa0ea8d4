"""Test advanced Depends functionality with dependency resolution."""

from typing import Protocol, cast

import pytest

from plugginger.api.depends import Depends
from plugginger.core.exceptions import DependencyError, MissingDependencyError
from plugginger.implementations.container import DIContainer, reset_container, set_container


# Test interfaces and implementations
class TestServiceInterface(Protocol):
    """Test service interface."""

    def process(self, data: str) -> str:
        """Process data."""
        ...


class TestService:
    """Test service implementation."""

    def __init__(self) -> None:
        """Initialize test service."""
        self.processed_count = 0

    def process(self, data: str) -> str:
        """Process data."""
        self.processed_count += 1
        return f"Processed: {data} (count: {self.processed_count})"


class TestDependsAdvanced:
    """Test advanced Depends functionality."""

    def setup_method(self) -> None:
        """Set up test environment."""
        # Reset container for each test
        reset_container()

    def teardown_method(self) -> None:
        """Clean up test environment."""
        # Reset container after each test
        reset_container()

    def test_type_based_dependency_resolution_success(self) -> None:
        """Test successful type-based dependency resolution."""
        container = DIContainer()
        set_container(container)

        # Register service in container
        container.register(cast(type[TestServiceInterface], TestServiceInterface), TestService)

        # Create Depends marker
        depends = Depends(TestServiceInterface)

        # Resolve dependency
        result = depends.resolve("test_param")

        assert isinstance(result, TestService)
        assert result.process("test") == "Processed: test (count: 1)"

    def test_type_based_dependency_resolution_not_found(self) -> None:
        """Test type-based dependency resolution when type not registered."""
        container = DIContainer()
        set_container(container)

        # Don't register the service
        depends = Depends(TestServiceInterface)

        # Should raise MissingDependencyError
        with pytest.raises(MissingDependencyError) as exc_info:
            depends.resolve("test_param")

        assert "TestServiceInterface" in str(exc_info.value)
        assert "not found in DI container" in str(exc_info.value)

    def test_type_based_optional_dependency_with_default(self) -> None:
        """Test optional type-based dependency with default value."""
        container = DIContainer()
        set_container(container)

        # Don't register the service
        default_value = "default_service"
        depends = Depends(TestServiceInterface, optional=True, default=default_value)

        # Should return default value
        result = depends.resolve("test_param")
        assert result == default_value

    def test_type_based_optional_dependency_without_default(self) -> None:
        """Test optional type-based dependency without default value."""
        container = DIContainer()
        set_container(container)

        # Don't register the service
        depends = Depends(TestServiceInterface, optional=True)

        # Should return None (default default)
        result = depends.resolve("test_param")
        assert result is None

    def test_string_dependency_app_config_not_implemented(self) -> None:
        """Test app.config string dependency (not implemented)."""
        container = DIContainer()
        set_container(container)

        depends = Depends("app.config")

        # Should raise DependencyError for not implemented
        with pytest.raises(DependencyError) as exc_info:
            depends.resolve("test_param")

        assert "app.config" in str(exc_info.value)
        assert "not yet implemented" in str(exc_info.value)

    def test_string_dependency_app_config_optional(self) -> None:
        """Test optional app.config string dependency."""
        container = DIContainer()
        set_container(container)

        default_config = {"test": "config"}
        depends = Depends("app.config", optional=True, default=default_config)

        # Should return default value
        result = depends.resolve("test_param")
        assert result == default_config

    def test_string_dependency_app_instance_not_registered(self) -> None:
        """Test app.instance string dependency when not registered."""
        container = DIContainer()
        set_container(container)

        depends = Depends("app.instance")

        # Should raise MissingDependencyError
        with pytest.raises(MissingDependencyError) as exc_info:
            depends.resolve("test_param")

        assert "app.instance" in str(exc_info.value)
        assert "not registered in DI container" in str(exc_info.value)

    def test_string_dependency_plugin_config_not_implemented(self) -> None:
        """Test plugin.config string dependency (not implemented)."""
        container = DIContainer()
        set_container(container)

        depends = Depends("plugin.config:test_plugin")

        # Should raise DependencyError for not implemented
        with pytest.raises(DependencyError) as exc_info:
            depends.resolve("test_param")

        assert "test_plugin" in str(exc_info.value)
        assert "not yet implemented" in str(exc_info.value)

    def test_string_dependency_service_name_not_implemented(self) -> None:
        """Test service name string dependency (not implemented)."""
        container = DIContainer()
        set_container(container)

        depends = Depends("test_plugin.test_service")

        # Should raise DependencyError for not implemented
        with pytest.raises(DependencyError) as exc_info:
            depends.resolve("test_param")

        assert "test_plugin.test_service" in str(exc_info.value)
        assert "not yet implemented" in str(exc_info.value)

    def test_none_dependency_not_implemented(self) -> None:
        """Test None dependency (not implemented)."""
        container = DIContainer()
        set_container(container)

        depends = Depends(None)

        # Should raise DependencyError for not implemented
        with pytest.raises(DependencyError) as exc_info:
            depends.resolve("test_param")

        assert "dependency=None is not yet implemented" in str(exc_info.value)

    def test_invalid_dependency_type(self) -> None:
        """Test invalid dependency type."""
        container = DIContainer()
        set_container(container)

        # Use invalid dependency type
        depends = Depends(123)  # type: ignore[arg-type]

        # Should raise DependencyError
        with pytest.raises(DependencyError) as exc_info:
            depends.resolve("test_param")

        assert "Invalid dependency type" in str(exc_info.value)
        assert "int" in str(exc_info.value)

    def test_singleton_behavior_in_dependency_resolution(self) -> None:
        """Test that singleton behavior is preserved in dependency resolution."""
        container = DIContainer()
        set_container(container)

        # Register as singleton (default)
        container.register(cast(type[TestServiceInterface], TestServiceInterface), TestService, singleton=True)

        depends = Depends(TestServiceInterface)

        # Resolve multiple times
        result1 = depends.resolve("test_param1")
        result2 = depends.resolve("test_param2")

        # Should be the same instance
        assert result1 is result2

        # Test that state is shared
        result1.process("test1")
        result2.process("test2")
        assert result1.processed_count == 2
        assert result2.processed_count == 2

    def test_depends_repr(self) -> None:
        """Test Depends string representation."""
        depends1 = Depends(TestServiceInterface)
        depends2 = Depends("app.config", optional=True, default="test")
        depends3 = Depends(None, optional=False)

        repr1 = repr(depends1)
        repr2 = repr(depends2)
        repr3 = repr(depends3)

        assert "TestServiceInterface" in repr1
        assert "optional=False" in repr1

        assert "app.config" in repr2
        assert "optional=True" in repr2
        assert "default='test'" in repr2

        assert "None" in repr3
        assert "optional=False" in repr3
