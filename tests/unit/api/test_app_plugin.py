"""Test AppPlugin base class."""

from typing import Any
from unittest.mock import Mock

import pytest

from plugginger.api.app_plugin import AppPluginBase
from plugginger.api.plugin import plugin
from plugginger.core.exceptions import AppPluginError
from plugginger.api.app import PluggingerAppInstance
from plugginger.testing.mock_app import MockPluggingerAppInstance


@plugin(name="test_app_plugin", version="1.0.0")
class TestAppPlugin(AppPluginBase):
    """Test implementation of AppPluginBase."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app=app, **injected_dependencies)
        self.setup_called = False
        self.teardown_called = False
        self.configure_called = False

    def _configure_internal_app(self) -> None:
        """Configure a mock internal app."""
        self.configure_called = True
        self._internal_app = Mock()

        # Make async methods return coroutines
        async def mock_start() -> None:
            self.setup_called = True
            return None

        async def mock_stop() -> None:
            self.teardown_called = True
            return None

        self._internal_app.start_all_plugins = mock_start
        self._internal_app.stop_all_plugins = mock_stop


@plugin(name="failing_app_plugin", version="1.0.0")
class FailingAppPlugin(AppPluginBase):
    """AppPlugin that fails to configure internal app."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app=app, **injected_dependencies)

    def _configure_internal_app(self) -> None:
        """Intentionally don't set _internal_app."""
        pass


@plugin(name="error_app_plugin", version="1.0.0")
class ErrorAppPlugin(AppPluginBase):
    """AppPlugin that has internal app start error."""

    def __init__(self, app: PluggingerAppInstance, **injected_dependencies: Any) -> None:
        super().__init__(app=app, **injected_dependencies)

    def _configure_internal_app(self) -> None:
        """Configure an app that will fail to start."""
        from unittest.mock import Mock

        self._internal_app = Mock()

        async def failing_start() -> None:
            raise RuntimeError("Internal app failed to start")

        self._internal_app.start_all_plugins = failing_start


def test_app_plugin_initialization() -> None:
    """Test AppPlugin initialization."""
    mock_app = MockPluggingerAppInstance()
    plugin_instance = TestAppPlugin(app=mock_app)

    assert plugin_instance._internal_app is None
    assert not plugin_instance._is_internal_app_started
    assert not plugin_instance.setup_called
    assert not plugin_instance.configure_called


@pytest.mark.asyncio
async def test_app_plugin_setup() -> None:
    """Test AppPlugin setup process."""
    mock_app = MockPluggingerAppInstance()
    plugin_instance = TestAppPlugin(app=mock_app)

    await plugin_instance.setup({})

    # Should have configured and started internal app
    assert plugin_instance.configure_called
    assert plugin_instance._internal_app is not None
    assert plugin_instance._is_internal_app_started

    # Should have called start_all_plugins on internal app
    assert plugin_instance.setup_called


@pytest.mark.asyncio
async def test_app_plugin_teardown() -> None:
    """Test AppPlugin teardown process."""
    mock_app = MockPluggingerAppInstance()
    plugin_instance = TestAppPlugin(app=mock_app)

    # Set up first
    await plugin_instance.setup({})

    # Then tear down
    await plugin_instance.teardown()

    # Should have called stop_all_plugins on internal app
    assert plugin_instance.teardown_called
    assert not plugin_instance._is_internal_app_started


@pytest.mark.asyncio
async def test_app_plugin_setup_failure() -> None:
    """Test AppPlugin setup failure when internal app not configured."""
    mock_app = MockPluggingerAppInstance()
    plugin_instance = FailingAppPlugin(app=mock_app)

    with pytest.raises(AppPluginError, match="failed to configure internal app"):
        await plugin_instance.setup({})


def test_app_plugin_internal_app_property() -> None:
    """Test internal_app property access."""
    mock_app = MockPluggingerAppInstance()
    plugin_instance = TestAppPlugin(app=mock_app)

    # Should raise error before setup
    with pytest.raises(AppPluginError, match="Internal app not configured yet"):
        _ = plugin_instance.internal_app


@pytest.mark.asyncio
async def test_app_plugin_internal_app_property_after_setup() -> None:
    """Test internal_app property access after setup."""
    mock_app = MockPluggingerAppInstance()
    plugin_instance = TestAppPlugin(app=mock_app)

    await plugin_instance.setup({})

    # Should return the internal app
    internal_app = plugin_instance.internal_app
    assert internal_app is plugin_instance._internal_app


def test_app_plugin_bridge_events() -> None:
    """Test event bridging (default implementation does nothing)."""
    mock_app = MockPluggingerAppInstance()
    plugin_instance = TestAppPlugin(app=mock_app)

    # Should not raise any errors
    plugin_instance._bridge_events()


def test_app_plugin_forward_methods() -> None:
    """Test event forwarding helper methods."""
    mock_app = MockPluggingerAppInstance()
    plugin_instance = TestAppPlugin(app=mock_app)

    # Mock the app (add attribute dynamically)
    plugin_instance.app = Mock()

    # Should not raise errors (placeholder implementation)
    plugin_instance._forward_to_internal({"test": "data"}, "test.event")
    plugin_instance._forward_to_outer({"test": "data"}, "test.event")


@pytest.mark.asyncio
async def test_app_plugin_teardown_without_setup() -> None:
    """Test teardown without prior setup."""
    mock_app = MockPluggingerAppInstance()
    plugin_instance = TestAppPlugin(app=mock_app)

    # Should not raise errors
    await plugin_instance.teardown()


@pytest.mark.asyncio
async def test_app_plugin_setup_with_internal_app_error() -> None:
    """Test setup when internal app start_all_plugins fails."""
    mock_app = MockPluggingerAppInstance()
    plugin_instance = ErrorAppPlugin(app=mock_app)

    with pytest.raises(AppPluginError, match="Failed to set up AppPlugin"):
        await plugin_instance.setup({})
