# tests/unit/api/test_app_simple.py

"""
Simplified tests for the App API.
"""

from __future__ import annotations

import asyncio
import os
import sys
from unittest.mock import AsyncMock, Mock

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


def test_app_instance_creation() -> bool:
    """Test PluggingerAppInstance creation and basic properties."""
    try:
        from plugginger.api.app import PluggingerAppInstance

        # Create mock dependencies
        mock_service_dispatcher = Mock()
        mock_event_dispatcher = Mock()

        # Create app instance
        app = PluggingerAppInstance(
            service_dispatcher=mock_service_dispatcher,
            event_dispatcher=mock_event_dispatcher,
            app_name="TestApp"
        )

        # Test basic properties
        assert app.app_name == "TestApp"
        assert not app.is_shutdown
        assert app.task_count == 0

        print("✅ App instance creation tests passed!")
        return True

    except Exception as e:
        print(f"❌ App instance creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_service_calling() -> bool:
    """Test service calling functionality."""
    async def async_test() -> bool:
        try:
            from plugginger.api.app import PluggingerAppInstance

            # Create mock service dispatcher
            mock_service_dispatcher = AsyncMock()
            mock_service_dispatcher.call_service.return_value = {"result": "success"}

            mock_event_dispatcher = Mock()

            # Create app instance
            app = PluggingerAppInstance(
                service_dispatcher=mock_service_dispatcher,
                event_dispatcher=mock_event_dispatcher,
                app_name="TestApp"
            )

            # Test service calling
            result = await app.call_service("test.service", "arg1", arg2=42)

            # Verify service was called correctly
            mock_service_dispatcher.call_service.assert_called_once_with(
                "test.service", "arg1", arg2=42
            )
            assert result == {"result": "success"}

            print("✅ Service calling tests passed!")
            return True

        except Exception as e:
            print(f"❌ Service calling test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_event_emission() -> bool:
    """Test event emission functionality."""
    async def async_test() -> bool:
        try:
            from plugginger.api.app import PluggingerAppInstance

            # Create mock event dispatcher
            mock_event_dispatcher = AsyncMock()
            mock_service_dispatcher = Mock()

            # Create app instance
            app = PluggingerAppInstance(
                service_dispatcher=mock_service_dispatcher,
                event_dispatcher=mock_event_dispatcher,
                app_name="TestApp"
            )

            # Test event emission
            event_data = {"user_id": 123, "name": "John"}
            await app.emit_event("user.created", event_data)

            # Verify event was emitted correctly
            mock_event_dispatcher.emit_event.assert_called_once_with(
                "user.created", event_data
            )

            print("✅ Event emission tests passed!")
            return True

        except Exception as e:
            print(f"❌ Event emission test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_managed_tasks() -> bool:
    """Test managed task functionality."""
    async def async_test() -> bool:
        try:
            from plugginger.api.app import PluggingerAppInstance

            mock_service_dispatcher = Mock()
            mock_event_dispatcher = Mock()

            # Create app instance
            app = PluggingerAppInstance(
                service_dispatcher=mock_service_dispatcher,
                event_dispatcher=mock_event_dispatcher,
                app_name="TestApp"
            )

            # Test creating managed tasks
            async def test_coroutine() -> str:
                await asyncio.sleep(0.01)
                return "completed"

            # Create a task
            task1 = app.create_managed_task(test_coroutine(), name="test_task")
            assert app.task_count == 1
            assert isinstance(task1, asyncio.Task)

            # Create another task
            task2 = app.create_managed_task(test_coroutine())
            assert app.task_count == 2

            # Wait for tasks to complete
            await asyncio.gather(task1, task2)

            # Tasks should be cleaned up automatically
            await asyncio.sleep(0.01)  # Give cleanup callback time to run
            assert app.task_count == 0

            print("✅ Managed tasks tests passed!")
            return True

        except Exception as e:
            print(f"❌ Managed tasks test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def test_shutdown() -> bool:
    """Test application shutdown functionality."""
    async def async_test() -> bool:
        try:
            from plugginger.api.app import PluggingerAppInstance

            mock_service_dispatcher = Mock()
            mock_event_dispatcher = Mock()

            # Create app instance
            app = PluggingerAppInstance(
                service_dispatcher=mock_service_dispatcher,
                event_dispatcher=mock_event_dispatcher,
                app_name="TestApp"
            )

            # Create some tasks
            async def test_task() -> None:
                await asyncio.sleep(10)

            app.create_managed_task(test_task())
            app.create_managed_task(test_task())

            assert app.task_count == 2
            assert not app.is_shutdown

            # Shutdown the app
            await app.shutdown()

            # Verify shutdown state
            assert app.is_shutdown
            assert app.task_count == 0

            print("✅ Shutdown tests passed!")
            return True

        except Exception as e:
            print(f"❌ Shutdown test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    return asyncio.run(async_test())


def run_all_tests() -> bool:
    """Run all app API tests."""
    tests = [
        test_app_instance_creation,
        test_service_calling,
        test_event_emission,
        test_managed_tasks,
        test_shutdown,
    ]

    passed = 0
    failed = 0

    for test in tests:
        print(f"\n🧪 Running {test.__name__}...")
        if test():
            passed += 1
        else:
            failed += 1

    print(f"\n📊 Test Results: {passed} passed, {failed} failed")
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    if success:
        print("\n🎉 All app API tests passed!")
    else:
        print("\n❌ Some app API tests failed!")
    exit(0 if success else 1)
