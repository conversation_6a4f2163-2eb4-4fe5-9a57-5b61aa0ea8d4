# tests/unit/interfaces/test_interfaces_comprehensive.py

"""
Comprehensive tests for interface modules to achieve >92% coverage.
"""

from __future__ import annotations

import os
import sys
from typing import Any

# Add src to path for tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))


class TestServiceInterfaces:
    """Test service interfaces module."""

    def test_service_dispatcher_protocol(self) -> None:
        """Test ServiceDispatcher protocol definition."""
        from typing import get_origin

        from plugginger.interfaces.services import ServiceDispatcher

        # Test that it's a Protocol (check if it's a typing construct)
        assert get_origin(ServiceDispatcher) is not None or hasattr(
            ServiceDispatcher, "_is_protocol"
        )

        # Test required methods exist
        assert hasattr(ServiceDispatcher, "add_service")
        assert hasattr(ServiceDispatcher, "call_service")
        assert hasattr(ServiceDispatcher, "has_service")
        assert hasattr(ServiceDispatcher, "list_services")
        assert hasattr(ServiceDispatcher, "remove_service")

    def test_service_dispatcher_implementation(self) -> None:
        """Test that we can implement ServiceDispatcher."""
        from plugginger.core.types import ServiceName

        class MockServiceDispatcher:
            """Mock implementation of ServiceDispatcher."""

            def __init__(self) -> None:
                self._services: dict[ServiceName, Any] = {}

            def add_service(self, service_name: ServiceName, service_method: Any) -> None:
                self._services[service_name] = service_method

            async def call_service(
                self, service_name: ServiceName, *args: Any, **kwargs: Any
            ) -> Any:
                if service_name in self._services:
                    return await self._services[service_name](*args, **kwargs)
                raise ValueError(f"Service not found: {service_name}")

            def has_service(self, service_name: ServiceName) -> bool:
                return service_name in self._services

            def list_services(self) -> list[ServiceName]:
                return list(self._services.keys())

            def remove_service(self, service_name: ServiceName) -> bool:
                if service_name in self._services:
                    del self._services[service_name]
                    return True
                return False

        # Test that our implementation satisfies the protocol
        dispatcher = MockServiceDispatcher()

        # Test basic functionality
        async def test_service() -> str:
            return "test_result"

        dispatcher.add_service("test.service", test_service)
        assert dispatcher.has_service("test.service")
        assert "test.service" in dispatcher.list_services()

        # Test removal
        assert dispatcher.remove_service("test.service")
        assert not dispatcher.has_service("test.service")
        assert not dispatcher.remove_service("nonexistent.service")

    def test_service_dispatcher_typing(self) -> None:
        """Test ServiceDispatcher typing works correctly."""
        from plugginger.interfaces.services import ServiceDispatcher

        # Test that we can use it as a type annotation
        def use_dispatcher(dispatcher: ServiceDispatcher) -> None:
            pass

        # This should not cause type errors
        assert use_dispatcher is not None


class TestEventInterfaces:
    """Test event interfaces module."""

    def test_event_dispatcher_protocol(self) -> None:
        """Test EventDispatcher protocol definition."""
        # Test that it's a Protocol (check if it's a typing construct)
        from typing import get_origin

        from plugginger.interfaces.events import EventDispatcher

        assert get_origin(EventDispatcher) is not None or hasattr(EventDispatcher, "_is_protocol")

        # Test required methods exist
        assert hasattr(EventDispatcher, "add_listener")
        assert hasattr(EventDispatcher, "emit_event")
        assert hasattr(EventDispatcher, "remove_listener")
        assert hasattr(EventDispatcher, "list_patterns")
        assert hasattr(EventDispatcher, "shutdown")

    def test_event_dispatcher_implementation(self) -> None:
        """Test that we can implement EventDispatcher."""
        from plugginger.core.types import EventHandlerType

        class MockEventDispatcher:
            """Mock implementation of EventDispatcher."""

            def __init__(self) -> None:
                self._listeners: dict[str, list[EventHandlerType]] = {}

            def add_listener(self, event_pattern: str, listener: EventHandlerType) -> None:
                if event_pattern not in self._listeners:
                    self._listeners[event_pattern] = []
                self._listeners[event_pattern].append(listener)

            async def emit_event(self, event_type: str, event_data: dict[str, Any]) -> None:
                for pattern, listeners in self._listeners.items():
                    if self._pattern_matches(pattern, event_type):
                        for listener in listeners:
                            await listener(event_data, event_type)

            def _pattern_matches(self, pattern: str, event_type: str) -> bool:
                return pattern == event_type or pattern == "*"

            def remove_listener(self, event_pattern: str, listener: EventHandlerType) -> bool:
                if event_pattern in self._listeners:
                    try:
                        self._listeners[event_pattern].remove(listener)
                        return True
                    except ValueError:
                        pass
                return False

            def list_patterns(self) -> list[str]:
                return list(self._listeners.keys())

            async def shutdown(self) -> None:
                self._listeners.clear()

        # Test that our implementation works
        dispatcher = MockEventDispatcher()

        events_received = []

        async def test_listener(event_data: dict[str, Any], event_type: str) -> None:
            events_received.append((event_type, event_data))

        dispatcher.add_listener("test.*", test_listener)
        assert "test.*" in dispatcher.list_patterns()

    def test_event_dispatcher_typing(self) -> None:
        """Test EventDispatcher typing works correctly."""
        from plugginger.interfaces.events import EventDispatcher

        # Test that we can use it as a type annotation
        def use_dispatcher(dispatcher: EventDispatcher) -> None:
            pass

        # This should not cause type errors
        assert use_dispatcher is not None


class TestInterfaceIntegration:
    """Test integration between interfaces."""

    def test_interfaces_can_be_used_together(self) -> None:
        """Test that both interfaces can be used together."""
        from plugginger.interfaces.events import EventDispatcher
        from plugginger.interfaces.services import ServiceDispatcher

        class MockApp:
            """Mock app using both interfaces."""

            def __init__(
                self, service_dispatcher: ServiceDispatcher, event_dispatcher: EventDispatcher
            ) -> None:
                self.service_dispatcher = service_dispatcher
                self.event_dispatcher = event_dispatcher

        # This should work without type errors
        assert MockApp is not None

    def test_interface_method_signatures(self) -> None:
        """Test that interface method signatures are correct."""
        import inspect

        from plugginger.interfaces.events import EventDispatcher
        from plugginger.interfaces.services import ServiceDispatcher

        # Test ServiceDispatcher signatures
        add_service_sig = inspect.signature(ServiceDispatcher.add_service)
        assert len(add_service_sig.parameters) == 3  # self, service_name, service_method

        call_service_sig = inspect.signature(ServiceDispatcher.call_service)
        assert len(call_service_sig.parameters) >= 2  # self, service_name, *args, **kwargs

        # Test EventDispatcher signatures
        add_listener_sig = inspect.signature(EventDispatcher.add_listener)
        assert len(add_listener_sig.parameters) == 3  # self, event_pattern, listener

        emit_event_sig = inspect.signature(EventDispatcher.emit_event)
        assert len(emit_event_sig.parameters) == 3  # self, event_type, event_data


class TestProtocolCompliance:
    """Test Protocol compliance and typing."""

    def test_service_dispatcher_protocol_compliance(self) -> None:
        """Test ServiceDispatcher protocol compliance."""

        # Test that it's a protocol (check if it's a typing construct)
        from typing import get_origin

        from plugginger.interfaces.services import ServiceDispatcher

        assert get_origin(ServiceDispatcher) is not None or hasattr(
            ServiceDispatcher, "_is_protocol"
        )

        # Test with a compliant implementation
        class CompliantServiceDispatcher:
            def add_service(self, service_name: str, service_method: Any) -> None:
                pass

            async def call_service(self, service_name: str, *args: Any, **kwargs: Any) -> Any:
                return None

            def has_service(self, service_name: str) -> bool:
                return False

            def list_services(self) -> list[str]:
                return []

            def remove_service(self, service_name: str) -> bool:
                return False

        # This should be recognized as implementing the protocol
        impl = CompliantServiceDispatcher()
        assert impl is not None

    def test_event_dispatcher_protocol_compliance(self) -> None:
        """Test EventDispatcher protocol compliance."""

        # Test with a compliant implementation
        class CompliantEventDispatcher:
            def add_listener(self, event_pattern: str, listener: Any) -> None:
                pass

            async def emit_event(self, event_type: str, event_data: dict[str, Any]) -> None:
                pass

            def remove_listener(self, event_pattern: str, listener: Any) -> bool:
                return False

            def list_patterns(self) -> list[str]:
                return []

            async def shutdown(self) -> None:
                pass

        # This should be recognized as implementing the protocol
        impl = CompliantEventDispatcher()
        assert impl is not None


class TestInterfaceDocumentation:
    """Test interface documentation and metadata."""

    def test_service_dispatcher_docstring(self) -> None:
        """Test ServiceDispatcher has proper documentation."""
        from plugginger.interfaces.services import ServiceDispatcher

        assert ServiceDispatcher.__doc__ is not None
        assert len(ServiceDispatcher.__doc__) > 0

    def test_event_dispatcher_docstring(self) -> None:
        """Test EventDispatcher has proper documentation."""
        from plugginger.interfaces.events import EventDispatcher

        assert EventDispatcher.__doc__ is not None
        assert len(EventDispatcher.__doc__) > 0

    def test_method_docstrings(self) -> None:
        """Test that interface methods have documentation."""
        from plugginger.interfaces.events import EventDispatcher
        from plugginger.interfaces.services import ServiceDispatcher

        # Test ServiceDispatcher method docs
        assert ServiceDispatcher.add_service.__doc__ is not None
        assert ServiceDispatcher.call_service.__doc__ is not None

        # Test EventDispatcher method docs
        assert EventDispatcher.add_listener.__doc__ is not None
        assert EventDispatcher.emit_event.__doc__ is not None


class TestModuleStructure:
    """Test interface module structure."""

    def test_services_module_exports(self) -> None:
        """Test services module exports."""
        import plugginger.interfaces.services as services_module

        # Test that ServiceDispatcher is exported
        assert hasattr(services_module, "ServiceDispatcher")

        # Test module docstring
        assert services_module.__doc__ is not None

    def test_events_module_exports(self) -> None:
        """Test events module exports."""
        import plugginger.interfaces.events as events_module

        # Test that EventDispatcher is exported
        assert hasattr(events_module, "EventDispatcher")

        # Test module docstring
        assert events_module.__doc__ is not None

    def test_interface_imports(self) -> None:
        """Test that interfaces can be imported correctly."""
        # These should not raise ImportError
        from plugginger.interfaces.events import EventDispatcher
        from plugginger.interfaces.services import ServiceDispatcher

        assert EventDispatcher is not None
        assert ServiceDispatcher is not None
