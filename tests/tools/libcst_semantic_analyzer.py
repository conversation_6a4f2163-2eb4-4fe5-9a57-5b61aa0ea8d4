import os
import json
from collections import defaultdict
from pathlib import Path

import libcst as cst
from libcst.metadata import PositionProvider, MetadataWrapper

PROJECT_ROOT = Path("/home/<USER>/Python/plugginger/src/plugginger/")
OUTPUT_FILE = Path("/home/<USER>/Python/plugginger/analysis_results/libcst_semantic_extended.json")

# --- Hilfsfunktionen ---
from typing import List, Union

def safe_getattr(node: cst.CSTNode, attr: str, default: object = None) -> object:
    try:
        return getattr(node, attr, default)
    except Exception:
        return default

def extract_import_names(node: Union[cst.Import, cst.ImportFrom]) -> List[str]:
    if isinstance(node, cst.Import):
        return [name.evaluated_name for name in node.names]
    if isinstance(node, cst.ImportFrom):
        module = node.module
        if module:
            return [module.code if hasattr(module, 'code') else str(module)]
    return []

def count_comments(module_text: str) -> int:
    return sum(1 for line in module_text.splitlines() if line.strip().startswith("#"))

from typing import List, Union, DefaultDict, Any, Dict

# --- Visitor-Klasse ---
class PluggingerAnalyzer(cst.CSTVisitor):
    METADATA_DEPENDENCIES = (PositionProvider,)

    def __init__(self, module_text: str) -> None:
        self.module_text: str = module_text
        self.result: DefaultDict[str, List[Any]] = defaultdict(list)

    def visit_Import(self, node: cst.Import) -> None:
        self.result["imports"].extend(extract_import_names(node))

    def visit_ImportFrom(self, node: cst.ImportFrom) -> None:
        self.result["imports"].extend(extract_import_names(node))

    def visit_ClassDef(self, node: cst.ClassDef) -> None:
        self.result["classes"].append(node.name.value)

    def visit_FunctionDef(self, node: cst.FunctionDef) -> None:
        self.result["functions"].append(node.name.value)

    def visit_Call(self, node: cst.Call) -> None:
        func_name = safe_getattr(node.func, "attr", None) or safe_getattr(node.func, "value", None)
        if hasattr(func_name, 'value'):
            func_name = func_name.value
        if func_name == "emit_event":
            self.result["emit_event_calls"].append("found")
        elif func_name == "get" and "container" in self.module_text:
            self.result["container_get_calls"].append("found")
        elif func_name == "depends":
            self.result["depends_calls"].append("found")

    def visit_If(self, node: cst.If) -> None:
        self.result["control_flow"].append("if")

    def visit_For(self, node: cst.For) -> None:
        self.result["control_flow"].append("for")

    def visit_While(self, node: cst.While) -> None:
        self.result["control_flow"].append("while")

    def visit_Try(self, node: cst.Try) -> None:
        self.result["control_flow"].append("try")

# --- Analysefunktion ---
def analyze_file(file_path: Path) -> Dict[str, Any]:
    try:
        module_text = file_path.read_text(encoding="utf-8")
        module = cst.parse_module(module_text)
        wrapper = MetadataWrapper(module)
        visitor = PluggingerAnalyzer(module_text)
        wrapper.visit(visitor)
        visitor.result["comments_density"] = [count_comments(module_text) / max(1, len(module_text.splitlines()))]
        visitor.result["dependencies"] = list(set(visitor.result["imports"]))
        return visitor.result
    except Exception as e:
        return {"error": str(e)}

# --- Hauptprozess ---
results = {}
for file in PROJECT_ROOT.rglob("*.py"):
    results[str(file)] = analyze_file(file)

OUTPUT_FILE.parent.mkdir(parents=True, exist_ok=True)
OUTPUT_FILE.write_text(json.dumps({"files": results, "cyclic_dependencies": []}, indent=2), encoding="utf-8")
print(f"Analyse abgeschlossen. Ergebnisse in {OUTPUT_FILE}")
