#!/usr/bin/env python3
"""
Test script to validate the FaultPolicyHandler and ExecutorRegistry implementation.
"""

import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))


def test_fault_policy_handler() -> bool:
    """Test FaultPolicyHandler implementation."""
    print("Testing FaultPolicyHandler...")

    try:
        from plugginger._internal.runtime.fault_policy import FaultPolicyHandler
        from plugginger.core.config import EventListenerFaultPolicy

        # Create a simple logger
        def simple_logger(msg: str) -> None:
            print(f"LOG: {msg}")

        # Test initialization
        handler = FaultPolicyHandler(EventListenerFaultPolicy.LOG_AND_CONTINUE, simple_logger)
        print("✓ FaultPolicyHandler initialization successful")

        # Test should_invoke method
        async def dummy_listener() -> None:
            pass

        result = handler.should_invoke(dummy_listener)
        assert result is True, "should_invoke should return True for LOG_AND_CONTINUE"
        print("✓ should_invoke method works")

        # Test handle_error method
        try:
            handler.handle_error("test.listener", id(dummy_listener), Exception("Test error"))
            print("✓ handle_error method works for LOG_AND_CONTINUE")
        except Exception as e:
            print(f"✗ handle_error failed: {e}")
            return False

        return True

    except Exception as e:
        print(f"✗ FaultPolicyHandler test failed: {e}")
        return False


def test_executor_registry() -> bool:
    """Test ExecutorRegistry implementation."""
    print("\nTesting ExecutorRegistry...")

    try:
        from plugginger._internal.runtime.executors import ExecutorRegistry
        from plugginger.config.models import ExecutorConfig

        # Create a simple logger
        def simple_logger(msg: str) -> None:
            print(f"LOG: {msg}")

        # Create executor config
        config = ExecutorConfig(name="test", max_workers=2, thread_name_prefix="TestWorker")

        # Test initialization
        registry = ExecutorRegistry(config, simple_logger)
        print("✓ ExecutorRegistry initialization successful")

        # Test get_executor method
        registry.get_executor("default")
        print("✓ get_executor method works")

        # Test shutdown
        registry.shutdown_executors(wait=False)
        print("✓ shutdown_executors method works")

        return True

    except Exception as e:
        print(f"✗ ExecutorRegistry test failed: {e}")
        return False


def main() -> int:
    """Run all tests."""
    print("=== Testing Plugginger Runtime Components ===")

    fault_policy_ok = test_fault_policy_handler()
    executor_registry_ok = test_executor_registry()

    if fault_policy_ok and executor_registry_ok:
        print("\n✓ All tests passed!")
        return 0
    else:
        print("\n✗ Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
