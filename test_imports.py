#!/usr/bin/env python3

import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_import(module_name, item_name=None):
    """Test importing a module or specific item."""
    try:
        if item_name:
            exec(f"from {module_name} import {item_name}")
            print(f"✓ {module_name}.{item_name} import successful")
        else:
            exec(f"import {module_name}")
            print(f"✓ {module_name} import successful")
        return True
    except Exception as e:
        print(f"✗ {module_name}{('.' + item_name) if item_name else ''} import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=== PLUGGINGER IMPORT TEST ===")

    # Test core modules
    print("\n--- Core Modules ---")
    test_import("plugginger.core.types", "LoggerCallable")
    test_import("plugginger.core.config", "EventListenerFaultPolicy")
    test_import("plugginger.core.exceptions", "PluggingerError")

    # Test runtime modules
    print("\n--- Runtime Modules ---")
    test_import("plugginger._internal.runtime.fault_policy", "FaultPolicyHandler")
    test_import("plugginger._internal.runtime.dispatcher", "ServiceDispatcher")
    test_import("plugginger._internal.runtime.dispatcher", "EventDispatcher")

    # Test API modules
    print("\n--- API Modules ---")
    test_import("plugginger.api.plugin", "PluginBase")
    test_import("plugginger.api.plugin", "plugin")
    test_import("plugginger._internal.runtime_facade", "RuntimeFacade")
    test_import("plugginger.api.builder", "PluggingerAppBuilder")
    test_import("plugginger.api.app", "PluggingerAppInstance")

if __name__ == "__main__":
    main()
