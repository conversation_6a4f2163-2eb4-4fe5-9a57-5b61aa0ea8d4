#!/usr/bin/env python3
"""Simple test for the refactored event dispatcher without circular imports."""

import sys
import os
import asyncio
from typing import Any

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_direct_components():
    """Test components directly without full framework imports."""
    print("Testing direct component imports...")
    
    try:
        # Import components directly
        sys.path.insert(0, os.path.join(src_path, 'plugginger', '_internal', 'runtime'))
        import dispatcher
        
        # Test EventPatternMatcher
        async def test_listener(event_data: dict[str, Any]) -> None:
            pass
        
        listeners_map = {"test.*": [test_listener]}
        matcher = dispatcher.EventPatternMatcher(listeners_map)
        
        matches = matcher.find_matching_listeners("test.event")
        assert len(matches) == 1
        assert matches[0][0] == "test.*"
        assert matches[0][1] == test_listener
        print("✅ EventPatternMatcher works")
        
        # Test pattern caching
        cached_matches = matcher.find_matching_listeners("test.event")
        assert len(cached_matches) == 1
        print("✅ Pattern caching works")
        
        # Test cache invalidation
        matcher.invalidate_cache()
        assert len(matcher._pattern_cache) == 0
        print("✅ Cache invalidation works")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_manager_direct():
    """Test ListenerTaskManager directly."""
    print("\nTesting ListenerTaskManager directly...")
    
    try:
        sys.path.insert(0, os.path.join(src_path, 'plugginger', '_internal', 'runtime'))
        import dispatcher
        import fault_policy
        
        # Create mock logger
        def mock_logger(msg: str) -> None:
            pass
        
        # Create fault policy handler with correct signature
        fault_handler = fault_policy.FaultPolicyHandler(mock_logger)
        
        # Create task manager
        task_manager = dispatcher.ListenerTaskManager(fault_handler, mock_logger, 5.0)
        
        # Test initial state
        assert task_manager.get_active_task_count() == 0
        print("✅ Task manager initialization works")
        
        # Test task execution
        async def test_listener(event_data: dict[str, Any]) -> None:
            await asyncio.sleep(0.01)  # Brief work simulation
        
        async def run_test():
            matched_listeners = [("test.*", test_listener)]
            tasks = await task_manager.execute_listeners(
                matched_listeners, "test.event", {"data": "test"}
            )
            
            assert len(tasks) == 1
            assert task_manager.get_active_task_count() == 1
            print("✅ Task creation works")
            
            # Wait for task to complete
            await asyncio.gather(*tasks)
            
            # Clean up completed tasks
            task_manager.cleanup_completed_tasks()
            assert task_manager.get_active_task_count() == 0
            print("✅ Task cleanup works")
        
        # Run the async test
        asyncio.run(run_test())
        
        return True
        
    except Exception as e:
        print(f"❌ Task manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complexity_measurement():
    """Test that complexity has been reduced."""
    print("\nTesting complexity reduction...")
    
    try:
        # Read the dispatcher file
        dispatcher_file = os.path.join(src_path, 'plugginger', '_internal', 'runtime', 'dispatcher.py')
        with open(dispatcher_file, 'r') as f:
            content = f.read()
        
        # Find emit_event method
        emit_start = content.find('async def emit_event(')
        if emit_start == -1:
            print("❌ Could not find emit_event method")
            return False
        
        # Find the end of the method
        next_method = content.find('\n    async def _handle_backpressure', emit_start)
        if next_method == -1:
            next_method = content.find('\n    async def shutdown', emit_start)
        if next_method == -1:
            next_method = len(content)
        
        emit_content = content[emit_start:next_method]
        
        # Count actual decision points (not in comments)
        lines = emit_content.split('\n')
        decision_points = 0
        
        for line in lines:
            stripped = line.strip()
            if stripped.startswith('#'):
                continue  # Skip comments
            if any(stripped.startswith(keyword) for keyword in ['if ', 'elif ', 'for ', 'while ', 'try:']):
                decision_points += 1
        
        print(f"emit_event method complexity analysis:")
        print(f"  - Decision points found: {decision_points}")
        print(f"  - Method length: {len(lines)} lines")
        
        # Check that complexity is reduced from original (12)
        if decision_points <= 8:
            print("✅ emit_event complexity significantly reduced")
            return True
        else:
            print(f"❌ emit_event complexity still high: {decision_points}")
            return False
        
    except Exception as e:
        print(f"❌ Complexity measurement failed: {e}")
        return False

def test_separation_of_concerns():
    """Test that concerns are properly separated."""
    print("\nTesting separation of concerns...")
    
    try:
        dispatcher_file = os.path.join(src_path, 'plugginger', '_internal', 'runtime', 'dispatcher.py')
        with open(dispatcher_file, 'r') as f:
            content = f.read()
        
        # Check that EventPatternMatcher exists
        assert 'class EventPatternMatcher:' in content
        print("✅ EventPatternMatcher class exists")
        
        # Check that ListenerTaskManager exists
        assert 'class ListenerTaskManager:' in content
        print("✅ ListenerTaskManager class exists")
        
        # Check that emit_event delegates to components
        emit_start = content.find('async def emit_event(')
        emit_end = content.find('\n    async def _handle_backpressure', emit_start)
        emit_content = content[emit_start:emit_end]
        
        assert 'self._pattern_matcher.find_matching_listeners' in emit_content
        print("✅ emit_event delegates to pattern matcher")
        
        assert 'self._task_manager.execute_listeners' in emit_content
        print("✅ emit_event delegates to task manager")
        
        assert 'self._task_manager.cleanup_completed_tasks' in emit_content
        print("✅ emit_event delegates cleanup to task manager")
        
        # Check that fnmatch logic is not in emit_event
        assert 'fnmatch.fnmatchcase' not in emit_content
        print("✅ Pattern matching logic moved out of emit_event")
        
        # Check that task creation logic is not in emit_event
        assert 'asyncio.create_task' not in emit_content
        print("✅ Task creation logic moved out of emit_event")
        
        return True
        
    except Exception as e:
        print(f"❌ Separation of concerns test failed: {e}")
        return False

def test_performance_features():
    """Test performance optimization features."""
    print("\nTesting performance features...")
    
    try:
        sys.path.insert(0, os.path.join(src_path, 'plugginger', '_internal', 'runtime'))
        import dispatcher
        
        # Test pattern caching
        async def test_listener(event_data: dict[str, Any]) -> None:
            pass
        
        listeners_map = {"user.*": [test_listener]}
        matcher = dispatcher.EventPatternMatcher(listeners_map)
        
        # First call should populate cache
        matches1 = matcher.find_matching_listeners("user.created")
        assert "user.created" in matcher._pattern_cache
        print("✅ Pattern cache populated on first call")
        
        # Second call should use cache
        matches2 = matcher.find_matching_listeners("user.created")
        assert matches1 == matches2
        print("✅ Pattern cache used on subsequent calls")
        
        # Test cache invalidation on listener changes
        matcher.invalidate_cache_for_pattern("user.*")
        assert len(matcher._pattern_cache) == 0
        print("✅ Cache invalidated when patterns change")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance features test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=== TESTING REFACTORED EVENT DISPATCHER (SIMPLE) ===\n")
    
    tests = [
        test_direct_components,
        test_task_manager_direct,
        test_complexity_measurement,
        test_separation_of_concerns,
        test_performance_features,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        if test():
            passed += 1
        else:
            failed += 1
        print()  # Add spacing
    
    print(f"=== RESULTS ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("🎉 All tests passed! Event dispatcher refactoring successful!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
