#!/usr/bin/env python3
"""Test the refactored method signature validation to ensure it works correctly."""

import sys
import os
import inspect
from typing import Any

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_validation_config_import():
    """Test that ValidationConfig and ValidationProfiles can be imported."""
    print("Testing ValidationConfig imports...")
    
    try:
        from plugginger._internal.validation import (
            ValidationConfig,
            ValidationProfiles,
            validate_method_signature,
            validate_service_method,
            validate_event_handler,
            validate_setup_method,
            validate_teardown_method,
            validate_plugin_constructor,
        )
        print("✅ All validation components imported successfully")
        return True
        
    except Exception as e:
        print(f"❌ Validation imports failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation_config_creation():
    """Test creating ValidationConfig instances."""
    print("\nTesting ValidationConfig creation...")
    
    try:
        from plugginger._internal.validation import ValidationConfig
        
        # Test default config
        config = ValidationConfig()
        assert config.require_async == True
        assert config.require_self == True
        assert config.min_params == 1
        assert config.context_name == "method"
        
        # Test custom config
        custom_config = ValidationConfig(
            require_async=False,
            min_params=2,
            context_name="custom method"
        )
        assert custom_config.require_async == False
        assert custom_config.min_params == 2
        assert custom_config.context_name == "custom method"
        
        print("✅ ValidationConfig creation works correctly")
        return True
        
    except Exception as e:
        print(f"❌ ValidationConfig creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation_profiles():
    """Test predefined validation profiles."""
    print("\nTesting validation profiles...")
    
    try:
        from plugginger._internal.validation import ValidationProfiles
        
        # Test SERVICE_METHOD profile
        service_profile = ValidationProfiles.SERVICE_METHOD
        assert service_profile.require_async == True
        assert service_profile.require_self == True
        assert service_profile.require_return_annotation == True
        assert service_profile.context_name == "service method"
        
        # Test EVENT_HANDLER profile
        event_profile = ValidationProfiles.EVENT_HANDLER
        assert event_profile.require_async == True
        assert event_profile.min_params == 2
        assert event_profile.max_params == 3
        assert event_profile.require_return_annotation == False
        assert event_profile.context_name == "event handler"
        
        # Test SETUP_METHOD profile
        setup_profile = ValidationProfiles.SETUP_METHOD
        assert setup_profile.require_async == True
        assert setup_profile.max_params == 2
        assert setup_profile.require_param_annotations == False
        assert setup_profile.context_name == "setup method"
        
        print("✅ Validation profiles work correctly")
        return True
        
    except Exception as e:
        print(f"❌ Validation profiles test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_service_method_validation():
    """Test service method validation."""
    print("\nTesting service method validation...")
    
    try:
        from plugginger._internal.validation import validate_service_method
        
        # Valid service method
        async def valid_service(self, param: str) -> str:
            return param
        
        # Should not raise
        validate_service_method(valid_service)
        
        # Invalid service method (not async)
        def invalid_service_sync(self, param: str) -> str:
            return param
        
        try:
            validate_service_method(invalid_service_sync)
            assert False, "Should have raised ValidationError"
        except Exception as e:
            assert "must be async" in str(e)
        
        # Invalid service method (no self)
        async def invalid_service_no_self(param: str) -> str:
            return param
        
        try:
            validate_service_method(invalid_service_no_self)
            assert False, "Should have raised ValidationError"
        except Exception as e:
            assert "must be 'self'" in str(e)
        
        print("✅ Service method validation works correctly")
        return True
        
    except Exception as e:
        print(f"❌ Service method validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_event_handler_validation():
    """Test event handler validation."""
    print("\nTesting event handler validation...")
    
    try:
        from plugginger._internal.validation import validate_event_handler
        
        # Valid event handler (2 params)
        async def valid_handler_2(self, event_data: dict[str, Any]) -> None:
            pass
        
        validate_event_handler(valid_handler_2)
        
        # Valid event handler (3 params)
        async def valid_handler_3(self, event_data: dict[str, Any], matched_event_type: str) -> None:
            pass
        
        validate_event_handler(valid_handler_3)
        
        # Invalid event handler (too few params)
        async def invalid_handler_few(self) -> None:
            pass
        
        try:
            validate_event_handler(invalid_handler_few)
            assert False, "Should have raised ValidationError"
        except Exception as e:
            assert "at least 2 parameters" in str(e)
        
        # Invalid event handler (too many params)
        async def invalid_handler_many(self, event_data: dict[str, Any], matched_event_type: str, extra: str) -> None:
            pass
        
        try:
            validate_event_handler(invalid_handler_many)
            assert False, "Should have raised ValidationError"
        except Exception as e:
            assert "at most 3 parameters" in str(e)
        
        print("✅ Event handler validation works correctly")
        return True
        
    except Exception as e:
        print(f"❌ Event handler validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complexity_reduction():
    """Verify that the complexity has been reduced."""
    print("\nAnalyzing complexity reduction...")
    
    try:
        # Read the refactored implementation
        with open('src/plugginger/_internal/validation.py', 'r') as f:
            content = f.read()
        
        # Find the validate_method_signature function
        func_start = content.find('def validate_method_signature(')
        if func_start == -1:
            print("❌ Could not find validate_method_signature function")
            return False
        
        # Find the end of the function (next function or end of file)
        next_func = content.find('\ndef ', func_start + 1)
        if next_func == -1:
            next_func = len(content)
        
        func_content = content[func_start:next_func]
        
        # Count decision points (rough complexity estimate)
        if_count = func_content.count('if ')
        elif_count = func_content.count('elif ')
        for_count = func_content.count('for ')
        while_count = func_content.count('while ')
        try_count = func_content.count('try:')
        
        estimated_complexity = 1 + if_count + elif_count + for_count + while_count + try_count
        
        print(f"validate_method_signature complexity analysis:")
        print(f"  - if statements: {if_count}")
        print(f"  - elif statements: {elif_count}")
        print(f"  - for loops: {for_count}")
        print(f"  - while loops: {while_count}")
        print(f"  - try blocks: {try_count}")
        print(f"  - Estimated complexity: {estimated_complexity}")
        
        # Check that complexity is significantly reduced from original (14)
        if estimated_complexity <= 8:
            print("✅ Function complexity significantly reduced")
            return True
        else:
            print(f"❌ Function complexity still high: {estimated_complexity}")
            return False
        
    except Exception as e:
        print(f"❌ Complexity analysis failed: {e}")
        return False

def test_backward_compatibility():
    """Test that the API changes maintain backward compatibility."""
    print("\nTesting backward compatibility...")
    
    try:
        from plugginger.api.service import validate_service_method_signature
        from plugginger.api.events import validate_event_listener_signature
        
        # Test that the old API functions still work
        async def test_service(self, param: str) -> str:
            return param
        
        async def test_event_handler(self, event_data: dict[str, Any]) -> None:
            pass
        
        # Should not raise
        validate_service_method_signature(test_service)
        validate_event_listener_signature(test_event_handler)
        
        print("✅ Backward compatibility maintained")
        return True
        
    except Exception as e:
        print(f"❌ Backward compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=== TESTING REFACTORED METHOD SIGNATURE VALIDATION ===\n")
    
    tests = [
        test_validation_config_import,
        test_validation_config_creation,
        test_validation_profiles,
        test_service_method_validation,
        test_event_handler_validation,
        test_complexity_reduction,
        test_backward_compatibility,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        if test():
            passed += 1
        else:
            failed += 1
        print()  # Add spacing
    
    print(f"=== RESULTS ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("🎉 All tests passed! Method signature validation refactoring successful!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
