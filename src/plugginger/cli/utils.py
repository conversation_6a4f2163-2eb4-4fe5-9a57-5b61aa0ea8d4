# src/plugginger/cli/utils.py

"""
Utility functions for CLI commands.

This module provides common functionality used across different CLI commands,
such as loading app factories and resolving module paths.
"""

import importlib
import logging
from typing import Any, Callable

from plugginger.api.builder import PluggingerAppBuilder

logger = logging.getLogger(__name__)


def resolve_app_factory(factory_path: str) -> PluggingerAppBuilder:
    """
    Resolve and load an app factory function.

    Args:
        factory_path: Path in format "module:function" or "module.submodule:function"

    Returns:
        Configured PluggingerAppBuilder instance

    Raises:
        ImportError: If module cannot be imported
        AttributeError: If function cannot be found
        TypeError: If function doesn't return a PluggingerAppBuilder

    Example:
        ```python
        builder = resolve_app_factory("myapp.main:create_app")
        ```
    """
    logger.debug(f"Resolving app factory: {factory_path}")
    
    if ':' not in factory_path:
        raise ValueError(
            f"Invalid factory path '{factory_path}'. "
            "Expected format: 'module:function'"
        )
    
    module_path, function_name = factory_path.rsplit(':', 1)
    
    try:
        # Import the module
        module = importlib.import_module(module_path)
        logger.debug(f"Imported module: {module_path}")
        
        # Get the function
        if not hasattr(module, function_name):
            raise AttributeError(
                f"Module '{module_path}' has no function '{function_name}'"
            )
        
        factory_function = getattr(module, function_name)
        logger.debug(f"Found factory function: {function_name}")
        
        # Call the factory function
        if not callable(factory_function):
            raise TypeError(
                f"'{function_name}' in module '{module_path}' is not callable"
            )
        
        result = factory_function()
        
        # Validate the result
        if not isinstance(result, PluggingerAppBuilder):
            raise TypeError(
                f"Factory function '{factory_path}' must return a PluggingerAppBuilder, "
                f"got {type(result)}"
            )
        
        logger.info(f"Successfully loaded app factory: {factory_path}")
        return result
        
    except ImportError as e:
        logger.error(f"Could not import module '{module_path}': {e}")
        raise ImportError(f"Could not import module '{module_path}': {e}") from e
    
    except Exception as e:
        logger.error(f"Error loading factory '{factory_path}': {e}")
        raise


def validate_factory_path(factory_path: str) -> bool:
    """
    Validate that a factory path has the correct format.

    Args:
        factory_path: Path to validate

    Returns:
        True if valid, False otherwise
    """
    if not factory_path or not isinstance(factory_path, str):
        return False
    
    if ':' not in factory_path:
        return False
    
    module_path, function_name = factory_path.rsplit(':', 1)
    
    # Basic validation
    if not module_path or not function_name:
        return False
    
    # Check that module path looks reasonable
    if not all(part.isidentifier() for part in module_path.split('.')):
        return False
    
    # Check that function name is a valid identifier
    if not function_name.isidentifier():
        return False
    
    return True


def setup_logging(verbose: bool = False) -> None:
    """
    Set up logging for CLI commands.

    Args:
        verbose: If True, enable debug logging
    """
    level = logging.DEBUG if verbose else logging.INFO
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # Reduce noise from external libraries
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
