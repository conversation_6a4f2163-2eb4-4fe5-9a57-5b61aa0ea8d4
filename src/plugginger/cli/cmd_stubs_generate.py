# src/plugginger/cli/cmd_stubs_generate.py

"""
Implementation of the 'plugginger stubs' command.

This module provides functionality to generate .pyi stub files for plugin proxies,
enabling better type checking and IDE support.
"""

import logging
import time
from pathlib import Path
from typing import Dict, Optional, Type

from plugginger.api.plugin import PluginBase
from plugginger.cli.utils import resolve_app_factory
from plugginger.stubgen import generate_stubs_for_app_structure

logger = logging.getLogger(__name__)


def cmd_stubs_generate(factory_path: str, output_dir_option: Optional[Path], watch: bool) -> None:
    """
    Execute the stubs generate command.

    Args:
        factory_path: Path to app factory function (module:function)
        output_dir_option: Optional output directory for stubs
        watch: Whether to watch for changes and regenerate
    """
    logger.info(f"Generating stubs for factory: {factory_path}")
    
    if watch:
        logger.info("Watch mode enabled - will regenerate stubs on changes")
        _watch_and_regenerate_stubs(factory_path, output_dir_option)
    else:
        _generate_stubs_for_app(factory_path, output_dir_option)


def _generate_stubs_for_app(factory_path: str, output_dir_option: Optional[Path]) -> None:
    """
    Generate stubs for a single app.

    Args:
        factory_path: Path to app factory function
        output_dir_option: Optional output directory
    """
    try:
        # Load the app builder
        app_builder = resolve_app_factory(factory_path)
        logger.info(f"Loaded app builder for: {app_builder._app_name}")
        
        # Get plugin items from builder
        plugin_items = _extract_plugin_items_from_builder(app_builder)
        logger.info(f"Found {len(plugin_items)} plugins to generate stubs for")
        
        # Determine output directory
        if output_dir_option:
            stubs_output_dir = output_dir_option
        else:
            stubs_output_dir = Path("src") / "plugginger_stubs"
        
        logger.info(f"Output directory: {stubs_output_dir}")
        
        # Ensure output directory exists
        stubs_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate stubs
        generate_stubs_for_app_structure(plugin_items, stubs_output_dir)
        
        print(f"✓ Generated stubs for {len(plugin_items)} plugins in {stubs_output_dir}")
        
    except Exception as e:
        logger.error(f"Failed to generate stubs: {e}")
        raise


def _extract_plugin_items_from_builder(app_builder) -> Dict[str, Type[PluginBase]]:
    """
    Extract plugin items from the app builder.

    Args:
        app_builder: The configured app builder

    Returns:
        Dictionary mapping registration_name -> plugin_class
    """
    plugin_items = {}
    
    # Access the builder's registered items
    # Note: This accesses internal state - in a real implementation,
    # the builder would expose this through a public API
    for registration_name, plugin_class in app_builder._registered_item_classes.items():
        plugin_items[registration_name] = plugin_class
        logger.debug(f"Found plugin: {registration_name} -> {plugin_class.__name__}")
    
    return plugin_items


def _watch_and_regenerate_stubs(factory_path: str, output_dir_option: Optional[Path]) -> None:
    """
    Watch for changes and regenerate stubs periodically.

    Args:
        factory_path: Path to app factory function
        output_dir_option: Optional output directory

    Note:
        This is a simplified implementation using polling.
        A production version would use file system events (e.g., watchdog library).
    """
    logger.info("Starting watch mode (polling every 5 seconds)")
    print("Watching for changes... Press Ctrl+C to stop")
    
    last_generation_time = 0
    
    try:
        while True:
            try:
                # Check if we should regenerate
                current_time = time.time()
                if current_time - last_generation_time >= 5:  # Every 5 seconds
                    logger.debug("Checking for changes...")
                    _generate_stubs_for_app(factory_path, output_dir_option)
                    last_generation_time = current_time
                    print(f"✓ Stubs regenerated at {time.strftime('%H:%M:%S')}")
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Error during watch regeneration: {e}")
                print(f"✗ Error: {e}")
                time.sleep(5)  # Wait before retrying
                
    except KeyboardInterrupt:
        print("\nWatch mode stopped")


def _should_regenerate_stubs(factory_path: str, output_dir: Path) -> bool:
    """
    Check if stubs should be regenerated based on file modification times.

    Args:
        factory_path: Path to app factory function
        output_dir: Output directory for stubs

    Returns:
        True if stubs should be regenerated

    Note:
        This is a simplified implementation. A production version would
        track source file modification times more accurately.
    """
    # For MVP, always regenerate
    return True


def _get_source_files_for_factory(factory_path: str) -> list[Path]:
    """
    Get list of source files that affect the factory.

    Args:
        factory_path: Path to app factory function

    Returns:
        List of source file paths

    Note:
        This is a placeholder for a more sophisticated implementation
        that would track all files imported by the factory.
    """
    # For MVP, return empty list
    return []
