# src/plugginger/cli/cmd_project_run.py

"""
Implementation of the 'plugginger run' command.

This module provides functionality to run Plugginger applications with
proper configuration loading and lifecycle management.
"""

import asyncio
import json
import logging
import signal
import sys
from pathlib import Path
from typing import Any, Optional

# import tomli  # Optional dependency for TOML support
from pydantic import ValidationError

from plugginger.cli.utils import resolve_app_factory
from plugginger.config.models import GlobalAppConfig

logger = logging.getLogger(__name__)


def cmd_project_run(factory_path: str, config_path: Optional[Path]) -> None:
    """
    Execute the project run command.

    Args:
        factory_path: Path to app factory function (module:function)
        config_path: Optional path to configuration file
    """
    logger.info(f"Running application from factory: {factory_path}")

    if config_path:
        logger.info(f"Using configuration file: {config_path}")

    try:
        _load_and_run_app(factory_path, config_path)
    except KeyboardInterrupt:
        logger.info("Application stopped by user")
        print("\nApplication stopped")
    except Exception as e:
        logger.error(f"Application failed: {e}")
        print(f"Error: {e}")
        sys.exit(1)


def _load_and_run_app(factory_path: str, config_path: Optional[Path]) -> None:
    """
    Load and run the application.

    Args:
        factory_path: Path to app factory function
        config_path: Optional path to configuration file
    """
    # Load configuration if provided
    app_config = None
    if config_path:
        app_config = _load_app_config(config_path)
        logger.info("Configuration loaded successfully")

    # Load the app builder
    app_builder = resolve_app_factory(factory_path)
    logger.info(f"App builder loaded: {app_builder._app_name}")

    # Build the application
    app_instance = app_builder.build(app_config)
    logger.info("Application built successfully")

    # Set up signal handlers for graceful shutdown
    _setup_signal_handlers()

    # Run the application
    print(f"Starting application: {app_instance.app_name}")
    asyncio.run(app_instance.run())


def _load_app_config(config_path: Path) -> GlobalAppConfig:
    """
    Load application configuration from file.

    Args:
        config_path: Path to configuration file

    Returns:
        Loaded and validated configuration

    Raises:
        FileNotFoundError: If config file doesn't exist
        ValidationError: If config is invalid
    """
    if not config_path.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")

    logger.debug(f"Loading configuration from: {config_path}")

    try:
        if config_path.suffix.lower() == '.json':
            return _load_json_config(config_path)
        elif config_path.suffix.lower() == '.toml':
            return _load_toml_config(config_path)
        else:
            # Try to detect format by content
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content.startswith('{'):
                    return _load_json_config(config_path)
                else:
                    return _load_toml_config(config_path)

    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        raise


def _load_json_config(config_path: Path) -> GlobalAppConfig:
    """Load configuration from JSON file."""
    with open(config_path, 'r', encoding='utf-8') as f:
        config_data = json.load(f)

    try:
        return GlobalAppConfig(**config_data)
    except ValidationError as e:
        raise ValidationError(f"Invalid configuration in {config_path}: {e}") from e


def _load_toml_config(config_path: Path) -> GlobalAppConfig:
    """Load configuration from TOML file."""
    # For MVP, skip TOML parsing to avoid external dependencies
    raise NotImplementedError(
        f"TOML configuration loading not implemented for {config_path}. "
        "Please use JSON format or install tomli dependency."
    )
    # In a full implementation, would use tomli:
    # with open(config_path, 'rb') as f:
    #     config_data = tomli.load(f)
    # plugginger_config = config_data.get('tool', {}).get('plugginger', {}).get('app_config', config_data)
    # return GlobalAppConfig(**plugginger_config)


def _setup_signal_handlers() -> None:
    """Set up signal handlers for graceful shutdown."""
    def signal_handler(signum: int, frame: Any) -> None:
        logger.info(f"Received signal {signum}, initiating shutdown...")
        # The asyncio.run() will handle the KeyboardInterrupt
        raise KeyboardInterrupt()

    # Set up signal handlers for Unix systems
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)
    if hasattr(signal, 'SIGINT'):
        signal.signal(signal.SIGINT, signal_handler)


def _create_example_config_file(output_path: Path) -> None:
    """
    Create an example configuration file.

    Args:
        output_path: Where to write the example config

    Note:
        This is a utility function that could be exposed as a separate command.
    """
    example_config = {
        "app_name": "MyPluggingerApp",
        "log_level": "INFO",
        "max_fractal_depth": 10,
        "default_event_listener_timeout_seconds": 5.0,
        "executors": [
            {
                "name": "default",
                "max_workers": 4,
                "thread_name_prefix": "PluggingerWorker"
            }
        ],
        "plugin_configs": {
            "example_plugin_instance_id": {
                "setting1": "value1",
                "setting2": 42
            }
        }
    }

    if output_path.suffix.lower() == '.toml':
        # For TOML, wrap in tool.plugginger.app_config
        toml_config = {
            "tool": {
                "plugginger": {
                    "app_config": example_config
                }
            }
        }
        # Note: Would need toml library for writing TOML
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(toml_config, f, indent=2)
    else:
        # JSON format
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(example_config, f, indent=2)

    print(f"Example configuration written to: {output_path}")
