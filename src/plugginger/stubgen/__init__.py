# src/plugginger/stubgen/__init__.py

"""
Stub generator for Plugginger plugin proxies.

This module provides functionality to generate .pyi stub files for plugin proxies,
enabling better type checking and IDE support for Plugginger applications.
"""

import inspect
import logging
from pathlib import Path
from typing import Any, Dict, Type, get_args, get_origin

from plugginger.api.plugin import PluginBase
from plugginger.core.constants import SERVICE_METADATA_KEY

logger = logging.getLogger(__name__)


def generate_stubs_for_app_structure(
    plugin_items: dict[str, type[PluginBase]],
    output_directory: Path
) -> None:
    """
    Generate .pyi stub files for plugin proxies based on app structure.

    Args:
        plugin_items: Mapping of registration_name -> plugin_class
        output_directory: Directory where stub files will be generated

    Example:
        ```python
        plugin_items = {
            "user_service": UserServicePlugin,
            "auth_module": AuthAppPlugin
        }
        generate_stubs_for_app_structure(plugin_items, Path("stubs/"))
        ```
    """
    logger.info(f"Generating stubs for {len(plugin_items)} plugins in {output_directory}")

    # Ensure output directory exists
    output_directory.mkdir(parents=True, exist_ok=True)

    # Create py.typed marker file
    py_typed_file = output_directory / "py.typed"
    py_typed_file.write_text("# Marker file for PEP 561\n")

    # Generate stub for each plugin
    for registration_name, plugin_class in plugin_items.items():
        stub_content = _render_proxy_stub_code(registration_name, plugin_class)

        # Create stub filename: user_service -> user_service_proxy.pyi
        stub_filename = f"{registration_name}_proxy.pyi"
        stub_file_path = output_directory / stub_filename

        stub_file_path.write_text(stub_content)
        logger.debug(f"Generated stub file: {stub_file_path}")

    logger.info(f"Stub generation completed. Files written to {output_directory}")


def _render_proxy_stub_code(registration_name: str, plugin_class: type[PluginBase]) -> str:
    """
    Render the .pyi stub code for a plugin proxy class.

    Args:
        registration_name: Name under which plugin is registered
        plugin_class: The plugin class to generate stubs for

    Returns:
        Complete .pyi file content as string
    """
    # Generate proxy class name: user_service -> UserServiceProxy
    proxy_class_name = _generate_proxy_class_name(registration_name)

    # Collect service methods
    service_methods = _collect_service_methods(plugin_class)

    # Generate imports
    imports = _generate_imports(service_methods)

    # Generate class definition
    class_def = _generate_class_definition(proxy_class_name, service_methods)

    return f"""# Generated stub file for {plugin_class.__name__} proxy
# This file provides type hints for the {registration_name} plugin proxy

{imports}

{class_def}
"""


def _generate_proxy_class_name(registration_name: str) -> str:
    """Generate proxy class name from registration name."""
    # Convert snake_case to PascalCase and add Proxy suffix
    words = registration_name.split('_')
    pascal_case = ''.join(word.capitalize() for word in words)
    return f"{pascal_case}Proxy"


def _collect_service_methods(plugin_class: type[PluginBase]) -> list[tuple[str, Any]]:
    """Collect all service methods from a plugin class."""
    service_methods = []

    for member_name in dir(plugin_class):
        if member_name.startswith('_'):
            continue

        member = getattr(plugin_class, member_name)
        if not callable(member):
            continue

        # Check if it's a service method
        service_meta = getattr(member, SERVICE_METADATA_KEY, None)
        if service_meta:
            service_methods.append((member_name, member))

    return service_methods


def _generate_imports(service_methods: list[tuple[str, Any]]) -> str:
    """Generate import statements for the stub file."""
    imports = {
        "from typing import Any, Awaitable",
        "from collections.abc import Callable"
    }

    # Analyze method signatures to determine needed imports
    for _method_name, method in service_methods:
        try:
            sig = inspect.signature(method)

            # Check return type
            if sig.return_annotation != inspect.Signature.empty:
                _add_type_imports(sig.return_annotation, imports)

            # Check parameter types
            for param in sig.parameters.values():
                if param.annotation != inspect.Parameter.empty:
                    _add_type_imports(param.annotation, imports)

        except (ValueError, TypeError):
            # Skip methods with problematic signatures
            continue

    return '\n'.join(sorted(imports))


def _add_type_imports(annotation: Any, imports: set[str]) -> None:
    """Add necessary imports for a type annotation."""
    # Handle common typing constructs
    origin = get_origin(annotation)
    if origin is not None:
        if origin in (list, dict, tuple, set):
            imports.add("from typing import List, Dict, Tuple, Set")
        elif str(origin).startswith('typing.Union'):
            imports.add("from typing import Union")
        elif str(origin).startswith('typing.Optional'):
            imports.add("from typing import Optional")

    # Handle args of generic types
    args = get_args(annotation)
    for arg in args:
        _add_type_imports(arg, imports)


def _generate_class_definition(proxy_class_name: str, service_methods: list[tuple[str, Any]]) -> str:
    """Generate the proxy class definition."""
    lines = [f"class {proxy_class_name}:"]
    lines.append('    """Generated proxy class for plugin services."""')
    lines.append("")

    if not service_methods:
        lines.append("    pass")
        return '\n'.join(lines)

    # Generate method stubs
    for method_name, method in service_methods:
        method_stub = _generate_method_stub(method_name, method)
        lines.extend(f"    {line}" for line in method_stub.split('\n'))
        lines.append("")

    return '\n'.join(lines)


def _generate_method_stub(method_name: str, method: Any) -> str:
    """Generate stub for a single service method."""
    try:
        sig = inspect.signature(method)

        # Format parameters
        params = []
        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue
            param_str = _format_parameter(param)
            params.append(param_str)

        params_str = ', '.join(params)

        # Format return type
        return_type = _format_type_hint(sig.return_annotation)

        # Generate method signature
        method_sig = f"def {method_name}({params_str}) -> {return_type}:"

        # Add docstring
        docstring = f'    """Service method: {method_name}."""'

        # Add ellipsis
        body = "    ..."

        return f"{method_sig}\n{docstring}\n{body}"

    except (ValueError, TypeError) as e:
        logger.warning(f"Could not generate stub for method {method_name}: {e}")
        return f"def {method_name}(*args: Any, **kwargs: Any) -> Any:\n    # FIXME: Complex signature, please annotate manually\n    ..."


def _format_parameter(param: inspect.Parameter) -> str:
    """Format a parameter for the stub file."""
    param_str = param.name

    # Add type annotation
    if param.annotation != inspect.Parameter.empty:
        type_hint = _format_type_hint(param.annotation)
        param_str += f": {type_hint}"
    else:
        param_str += ": Any"

    # Add default value
    if param.default != inspect.Parameter.empty:
        if param.default is None:
            param_str += " = None"
        elif isinstance(param.default, str | int | float | bool):
            param_str += f" = {repr(param.default)}"
        else:
            param_str += " = ..."

    return param_str


def _format_type_hint(annotation: Any) -> str:
    """Format a type annotation for the stub file."""
    if annotation == inspect.Signature.empty:
        return "Any"

    try:
        # Handle None type
        if annotation is type(None):
            return "None"

        # Handle basic types
        if annotation in (int, str, float, bool, bytes):
            return annotation.__name__

        # Handle typing constructs
        origin = get_origin(annotation)
        args = get_args(annotation)

        if origin is not None:
            if origin == list:
                if args:
                    return f"list[{_format_type_hint(args[0])}]"
                return "list[Any]"
            elif origin == dict:
                if len(args) >= 2:
                    return f"dict[{_format_type_hint(args[0])}, {_format_type_hint(args[1])}]"
                return "dict[Any, Any]"
            elif origin == tuple:
                if args:
                    arg_strs = [_format_type_hint(arg) for arg in args]
                    return f"tuple[{', '.join(arg_strs)}]"
                return "tuple[Any, ...]"
            elif str(origin).startswith('typing.Union'):
                if args:
                    arg_strs = [_format_type_hint(arg) for arg in args]
                    return f"Union[{', '.join(arg_strs)}]"

        # Handle coroutines/awaitables
        if hasattr(annotation, '__name__'):
            if 'Coroutine' in annotation.__name__ or 'Awaitable' in annotation.__name__:
                if args:
                    return f"Awaitable[{_format_type_hint(args[-1])}]"
                return "Awaitable[Any]"

        # Fallback to string representation
        return str(annotation).replace('typing.', '')

    except Exception:
        # Ultimate fallback
        return "Any"


# Backward compatibility alias
generate_stubs_for_plugins = generate_stubs_for_app_structure
