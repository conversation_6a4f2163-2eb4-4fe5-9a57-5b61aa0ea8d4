# src/plugginger/__init__.py

"""
Plugginger: A Python framework for highly modular applications.

This `__init__.py` file serves as the main public API surface for the Plugginger
framework. It re-exports the most commonly used classes, decorators, and
exceptions from their respective modules within the `plugginger.api` subpackage.
This allows users of the framework to import these core components directly
from the top-level `plugginger` package, simplifying import statements and
making the framework easier to use.

Example Usage:
```python
from plugginger import (
    PluginBase,
    plugin,
    service,
    on_event,
    Depends,
    EventEnumBase,
    PluggingerAppBuilder,
    PluggingerAppInstance,
    PluggingerError
)

# Define a plugin
@plugin(name="my_service", version="1.0.0")
class MyServicePlugin(PluginBase):
    @service
    async def greet(self, name: str) -> str:
        return f"Hello, {name}!"

# Build and run an application
builder = PluggingerAppBuilder(app_name="my_simple_app")
builder.include(MyServicePlugin)
app = builder.build()

async def main_logic():
    result = await app.call_service("my_service.greet", name="World")
    print(result) # Output: Hello, World!

if __name__ == "__main__":
    import asyncio
    asyncio.run(app.run(main_logic))
"""

# Version information
__version__ = "0.1.0"

# Import for type annotations
from typing import Any

# Clean DI-based architecture - no lazy imports needed
# All imports are handled through the DI container

__all__ = [
    # Core components
    "DIContainer",
    "get_container",
    "reset_container",
    # Configuration
    "EventListenerFaultPolicy",
    # Exceptions
    "PluggingerError",
    "ServiceNotFoundError",
    "EventListenerError",
]


def __getattr__(name: str) -> Any:
    """Provide access to core components through clean imports."""
    if name == "DIContainer":
        from plugginger.implementations.container import DIContainer

        return DIContainer
    elif name == "get_container":
        from plugginger.implementations.container import get_container

        return get_container
    elif name == "reset_container":
        from plugginger.implementations.container import reset_container

        return reset_container
    elif name == "EventListenerFaultPolicy":
        from plugginger.core.config import EventListenerFaultPolicy

        return EventListenerFaultPolicy
    elif name == "PluggingerError":
        from plugginger.core.exceptions import PluggingerError

        return PluggingerError
    elif name == "ServiceNotFoundError":
        from plugginger.core.exceptions import ServiceNotFoundError

        return ServiceNotFoundError
    elif name == "EventListenerError":
        from plugginger.core.exceptions import EventListenerError

        return EventListenerError
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


# Note: __all__ is defined above with DI components
