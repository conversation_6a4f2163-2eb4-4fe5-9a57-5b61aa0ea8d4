# src/plugginger/__init__.py

"""
Plugginger: A Python framework for highly modular applications.

This `__init__.py` file serves as the main public API surface for the Plugginger
framework. It re-exports the most commonly used classes, decorators, and
exceptions from their respective modules within the `plugginger.api` subpackage.
This allows users of the framework to import these core components directly
from the top-level `plugginger` package, simplifying import statements and
making the framework easier to use.

Example Usage:
```python
from plugginger import (
    PluginBase,
    plugin,
    service,
    on_event,
    Depends,
    EventEnumBase,
    PluggingerAppBuilder,
    PluggingerAppInstance,
    PluggingerError
)

# Define a plugin
@plugin(name="my_service", version="1.0.0")
class MyServicePlugin(PluginBase):
    @service
    async def greet(self, name: str) -> str:
        return f"Hello, {name}!"

# Build and run an application
builder = PluggingerAppBuilder(app_name="my_simple_app")
builder.include(MyServicePlugin)
app = builder.build()

async def main_logic():
    result = await app.call_service("my_service.greet", name="World")
    print(result) # Output: Hello, World!

if __name__ == "__main__":
    import asyncio
    asyncio.run(app.run(main_logic))
"""

# Version information
__version__ = "0.1.0"

# Import for type annotations
from typing import Any

# Public API exports
__all__ = [
    # Core plugin components
    "PluginBase",
    "AppPluginBase",
    "plugin",
    "service",
    "on_event",
    "background_task",

    # Dependency injection
    "Depends",

    # Application building
    "PluggingerAppBuilder",
    "PluggingerAppInstance",

    # Configuration
    "GlobalAppConfig",

    # Core exceptions
    "PluggingerError",
    "PluginRegistrationError",
    "ServiceDefinitionError",
    "EventDefinitionError",
    "DependencyError",
    "MissingDependencyError",
    "CircularDependencyError",
    "ConfigurationError",
]


def __getattr__(name: str) -> Any:
    """Provide access to core framework components through lazy imports."""
    # Core plugin components
    if name == "PluginBase":
        from plugginger.api.plugin import PluginBase
        return PluginBase
    elif name == "AppPluginBase":
        from plugginger.api.app_plugin import AppPluginBase
        return AppPluginBase
    elif name == "plugin":
        from plugginger.api.plugin import plugin
        return plugin
    elif name == "service":
        from plugginger.api.service import service
        return service
    elif name == "on_event":
        from plugginger.api.events import on_event
        return on_event
    elif name == "background_task":
        from plugginger.api.background import background_task
        return background_task

    # Dependency injection
    elif name == "Depends":
        from plugginger.api.depends import Depends
        return Depends

    # Application building
    elif name == "PluggingerAppBuilder":
        from plugginger.api.builder import PluggingerAppBuilder
        return PluggingerAppBuilder
    elif name == "PluggingerAppInstance":
        from plugginger.api.app import PluggingerAppInstance
        return PluggingerAppInstance

    # Configuration
    elif name == "GlobalAppConfig":
        from plugginger.config.models import GlobalAppConfig
        return GlobalAppConfig

    # Core exceptions
    elif name == "PluggingerError":
        from plugginger.core.exceptions import PluggingerError
        return PluggingerError
    elif name == "PluginRegistrationError":
        from plugginger.core.exceptions import PluginRegistrationError
        return PluginRegistrationError
    elif name == "ServiceDefinitionError":
        from plugginger.core.exceptions import ServiceDefinitionError
        return ServiceDefinitionError
    elif name == "EventDefinitionError":
        from plugginger.core.exceptions import EventDefinitionError
        return EventDefinitionError
    elif name == "DependencyError":
        from plugginger.core.exceptions import DependencyError
        return DependencyError
    elif name == "MissingDependencyError":
        from plugginger.core.exceptions import MissingDependencyError
        return MissingDependencyError
    elif name == "CircularDependencyError":
        from plugginger.core.exceptions import CircularDependencyError
        return CircularDependencyError
    elif name == "ConfigurationError":
        from plugginger.core.exceptions import ConfigurationError
        return ConfigurationError

    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
