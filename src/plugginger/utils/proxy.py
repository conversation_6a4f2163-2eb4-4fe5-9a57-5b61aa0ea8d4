# src/plugginger/utils/proxy.py

"""
Proxy utilities for the Plugginger framework.

This module provides proxy classes for lazy loading and dependency injection,
enabling flexible service resolution and plugin interaction.
"""

from __future__ import annotations

from collections.abc import Callable
from typing import Any, TypeVar

from plugginger.core.exceptions import MissingDependencyError
from plugginger.core.types import ServiceName

T = TypeVar("T")


class LazyProxy:
    """
    Base class for lazy-loading proxies.

    This proxy delays resolution of the target object until it's actually accessed,
    enabling flexible dependency injection and circular dependency resolution.
    """

    def __init__(self, resolver: Callable[[], Any], name: str = "unknown") -> None:
        """
        Initialize the lazy proxy.

        Args:
            resolver: Function that resolves the target object
            name: Name for debugging purposes
        """
        self._resolver = resolver
        self._name = name
        self._resolved: Any = None
        self._is_resolved = False

    def _resolve(self) -> Any:
        """Resolve the target object if not already resolved."""
        if not self._is_resolved:
            try:
                self._resolved = self._resolver()
                self._is_resolved = True
            except Exception as e:
                raise MissingDependencyError(f"Failed to resolve proxy '{self._name}': {e}") from e

        return self._resolved

    def __getattr__(self, name: str) -> Any:
        """Proxy attribute access to the resolved object."""
        target = self._resolve()
        return getattr(target, name)

    def __call__(self, *args: Any, **kwargs: Any) -> Any:
        """Proxy method calls to the resolved object."""
        target = self._resolve()
        return target(*args, **kwargs)

    def __repr__(self) -> str:
        """String representation for debugging."""
        status = "resolved" if self._is_resolved else "unresolved"
        return f"LazyProxy({self._name!r}, {status})"


class ServiceProxy:
    """
    Proxy for service method calls.

    This proxy enables calling services by name without direct references,
    supporting dynamic service resolution and dependency injection.
    """

    def __init__(self, service_dispatcher_resolver: Callable[[], Any]) -> None:
        """
        Initialize the service proxy.

        Args:
            service_dispatcher_resolver: Function that resolves the service dispatcher
        """
        self._dispatcher_resolver = service_dispatcher_resolver
        self._dispatcher: Any = None
        self._is_resolved = False

    def _get_dispatcher(self) -> Any:
        """Get the service dispatcher, resolving if necessary."""
        if not self._is_resolved:
            try:
                self._dispatcher = self._dispatcher_resolver()
                self._is_resolved = True
            except Exception as e:
                raise MissingDependencyError(f"Failed to resolve service dispatcher: {e}") from e

        return self._dispatcher

    async def call(self, service_name: ServiceName, *args: Any, **kwargs: Any) -> Any:
        """
        Call a service by name.

        Args:
            service_name: Name of the service to call
            *args: Positional arguments to pass to the service
            **kwargs: Keyword arguments to pass to the service

        Returns:
            Result from the service call

        Raises:
            ServiceNotFoundError: If service is not found
        """
        dispatcher = self._get_dispatcher()
        return await dispatcher.call_service(service_name, *args, **kwargs)

    def __getattr__(self, service_name: str) -> ServiceMethodProxy:
        """
        Get a proxy for a specific service.

        Args:
            service_name: Name of the service

        Returns:
            Proxy for the service method
        """
        return ServiceMethodProxy(self, service_name)

    def __repr__(self) -> str:
        """String representation for debugging."""
        status = "resolved" if self._is_resolved else "unresolved"
        return f"ServiceProxy({status})"


class ServiceMethodProxy:
    """
    Proxy for a specific service method.

    This proxy represents a single service and enables calling it like a regular method.
    """

    def __init__(self, service_proxy: ServiceProxy, service_name: str) -> None:
        """
        Initialize the service method proxy.

        Args:
            service_proxy: Parent service proxy
            service_name: Name of the service
        """
        self._service_proxy = service_proxy
        self._service_name = service_name

    async def __call__(self, *args: Any, **kwargs: Any) -> Any:
        """
        Call the service method.

        Args:
            *args: Positional arguments to pass to the service
            **kwargs: Keyword arguments to pass to the service

        Returns:
            Result from the service call
        """
        return await self._service_proxy.call(self._service_name, *args, **kwargs)

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"ServiceMethodProxy({self._service_name!r})"


class EventProxy:
    """
    Proxy for event emission.

    This proxy enables emitting events without direct references to the event dispatcher,
    supporting dynamic event resolution and dependency injection.
    """

    def __init__(self, event_dispatcher_resolver: Callable[[], Any]) -> None:
        """
        Initialize the event proxy.

        Args:
            event_dispatcher_resolver: Function that resolves the event dispatcher
        """
        self._dispatcher_resolver = event_dispatcher_resolver
        self._dispatcher: Any = None
        self._is_resolved = False

    def _get_dispatcher(self) -> Any:
        """Get the event dispatcher, resolving if necessary."""
        if not self._is_resolved:
            try:
                self._dispatcher = self._dispatcher_resolver()
                self._is_resolved = True
            except Exception as e:
                raise MissingDependencyError(f"Failed to resolve event dispatcher: {e}") from e

        return self._dispatcher

    async def emit(self, event_type: str, event_data: dict[str, Any]) -> None:
        """
        Emit an event.

        Args:
            event_type: Type of the event
            event_data: Data payload for the event
        """
        dispatcher = self._get_dispatcher()
        await dispatcher.emit_event(event_type, event_data)

    def __repr__(self) -> str:
        """String representation for debugging."""
        status = "resolved" if self._is_resolved else "unresolved"
        return f"EventProxy({status})"


class ConfigProxy:
    """
    Proxy for configuration access.

    This proxy enables accessing configuration values without direct references,
    supporting dynamic configuration resolution and dependency injection.
    """

    def __init__(self, config_resolver: Callable[[], dict[str, Any]]) -> None:
        """
        Initialize the config proxy.

        Args:
            config_resolver: Function that resolves the configuration
        """
        self._config_resolver = config_resolver
        self._config: dict[str, Any] | None = None
        self._is_resolved = False

    def _get_config(self) -> dict[str, Any]:
        """Get the configuration, resolving if necessary."""
        if not self._is_resolved:
            try:
                self._config = self._config_resolver()
                self._is_resolved = True
            except Exception as e:
                raise MissingDependencyError(f"Failed to resolve configuration: {e}") from e

        return self._config or {}

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value.

        Args:
            key: Configuration key
            default: Default value if key is not found

        Returns:
            Configuration value or default
        """
        config = self._get_config()
        return config.get(key, default)

    def __getitem__(self, key: str) -> Any:
        """Get a configuration value using dict-like access."""
        config = self._get_config()
        return config[key]

    def __contains__(self, key: str) -> bool:
        """Check if a configuration key exists."""
        config = self._get_config()
        return key in config

    def __repr__(self) -> str:
        """String representation for debugging."""
        status = "resolved" if self._is_resolved else "unresolved"
        return f"ConfigProxy({status})"


def create_service_proxy(service_dispatcher_resolver: Callable[[], Any]) -> ServiceProxy:
    """
    Create a service proxy.

    Args:
        service_dispatcher_resolver: Function that resolves the service dispatcher

    Returns:
        Service proxy instance
    """
    return ServiceProxy(service_dispatcher_resolver)


def create_event_proxy(event_dispatcher_resolver: Callable[[], Any]) -> EventProxy:
    """
    Create an event proxy.

    Args:
        event_dispatcher_resolver: Function that resolves the event dispatcher

    Returns:
        Event proxy instance
    """
    return EventProxy(event_dispatcher_resolver)


def create_config_proxy(config_resolver: Callable[[], dict[str, Any]]) -> ConfigProxy:
    """
    Create a config proxy.

    Args:
        config_resolver: Function that resolves the configuration

    Returns:
        Config proxy instance
    """
    return ConfigProxy(config_resolver)


def create_lazy_proxy(resolver: Callable[[], T], name: str = "unknown") -> LazyProxy:
    """
    Create a lazy proxy.

    Args:
        resolver: Function that resolves the target object
        name: Name for debugging purposes

    Returns:
        Lazy proxy instance
    """
    return LazyProxy(resolver, name)
