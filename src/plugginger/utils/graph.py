# src/plugginger/utils/graph.py

"""
Dependency graph utilities for the Plugginger framework.

This module provides utilities for analyzing plugin dependencies, detecting
circular dependencies, and determining optimal loading order.
"""

from __future__ import annotations

from typing import Any, TypeVar

from plugginger.core.exceptions import CircularDependencyError, DependencyError

T = TypeVar("T")


class DependencyNode:
    """
    Represents a node in the dependency graph.

    Each node represents a plugin with its dependencies and dependents.
    """

    def __init__(self, name: str, data: Any = None) -> None:
        """
        Initialize a dependency node.

        Args:
            name: Unique name for the node
            data: Optional data associated with the node
        """
        self.name = name
        self.data = data
        self.dependencies: set[str] = set()
        self.dependents: set[str] = set()
        self.optional_dependencies: set[str] = set()

    def add_dependency(self, dependency_name: str, optional: bool = False) -> None:
        """
        Add a dependency to this node.

        Args:
            dependency_name: Name of the dependency
            optional: Whether the dependency is optional
        """
        if optional:
            self.optional_dependencies.add(dependency_name)
        else:
            self.dependencies.add(dependency_name)

    def remove_dependency(self, dependency_name: str) -> None:
        """
        Remove a dependency from this node.

        Args:
            dependency_name: Name of the dependency to remove
        """
        self.dependencies.discard(dependency_name)
        self.optional_dependencies.discard(dependency_name)

    def add_dependent(self, dependent_name: str) -> None:
        """
        Add a dependent to this node.

        Args:
            dependent_name: Name of the dependent
        """
        self.dependents.add(dependent_name)

    def remove_dependent(self, dependent_name: str) -> None:
        """
        Remove a dependent from this node.

        Args:
            dependent_name: Name of the dependent to remove
        """
        self.dependents.discard(dependent_name)

    def get_all_dependencies(self) -> set[str]:
        """Get all dependencies (required + optional)."""
        return self.dependencies | self.optional_dependencies

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"DependencyNode({self.name!r}, deps={len(self.dependencies)}, opts={len(self.optional_dependencies)})"


class DependencyGraph:
    """
    Manages a dependency graph for plugins.

    Provides methods for adding nodes, analyzing dependencies, and determining
    load order while detecting circular dependencies.
    """

    def __init__(self) -> None:
        """Initialize an empty dependency graph."""
        self._nodes: dict[str, DependencyNode] = {}

    def add_node(self, name: str, data: Any = None) -> DependencyNode:
        """
        Add a node to the graph.

        Args:
            name: Unique name for the node
            data: Optional data to associate with the node

        Returns:
            The created dependency node

        Raises:
            DependencyError: If node already exists
        """
        if name in self._nodes:
            raise DependencyError(f"Node '{name}' already exists in dependency graph")

        node = DependencyNode(name, data)
        self._nodes[name] = node
        return node

    def remove_node(self, name: str) -> None:
        """
        Remove a node from the graph.

        Args:
            name: Name of the node to remove

        Raises:
            DependencyError: If node doesn't exist
        """
        if name not in self._nodes:
            raise DependencyError(f"Node '{name}' not found in dependency graph")

        node = self._nodes[name]

        # Remove this node from all its dependencies' dependents
        for dep_name in node.get_all_dependencies():
            if dep_name in self._nodes:
                self._nodes[dep_name].remove_dependent(name)

        # Remove this node from all its dependents' dependencies
        for dep_name in node.dependents:
            if dep_name in self._nodes:
                self._nodes[dep_name].remove_dependency(name)

        del self._nodes[name]

    def add_dependency(self, node_name: str, dependency_name: str, optional: bool = False) -> None:
        """
        Add a dependency relationship between nodes.

        Args:
            node_name: Name of the node that depends on something
            dependency_name: Name of the dependency
            optional: Whether the dependency is optional

        Raises:
            DependencyError: If nodes don't exist
        """
        if node_name not in self._nodes:
            raise DependencyError(f"Node '{node_name}' not found in dependency graph")

        if dependency_name not in self._nodes:
            raise DependencyError(f"Dependency '{dependency_name}' not found in dependency graph")

        # Add dependency
        self._nodes[node_name].add_dependency(dependency_name, optional)

        # Add reverse relationship
        self._nodes[dependency_name].add_dependent(node_name)

    def get_node(self, name: str) -> DependencyNode:
        """
        Get a node by name.

        Args:
            name: Name of the node

        Returns:
            The dependency node

        Raises:
            DependencyError: If node doesn't exist
        """
        if name not in self._nodes:
            raise DependencyError(f"Node '{name}' not found in dependency graph")

        return self._nodes[name]

    def has_node(self, name: str) -> bool:
        """
        Check if a node exists in the graph.

        Args:
            name: Name of the node

        Returns:
            True if node exists, False otherwise
        """
        return name in self._nodes

    def get_all_nodes(self) -> list[DependencyNode]:
        """Get all nodes in the graph."""
        return list(self._nodes.values())

    def detect_circular_dependencies(self) -> list[list[str]]:
        """
        Detect circular dependencies in the graph.

        Returns:
            List of cycles, where each cycle is a list of node names

        Raises:
            CircularDependencyError: If circular dependencies are found
        """
        cycles = []
        visited = set()
        rec_stack = set()

        def dfs(node_name: str, path: list[str]) -> None:
            if node_name in rec_stack:
                # Found a cycle
                cycle_start = path.index(node_name)
                cycle = path[cycle_start:] + [node_name]
                cycles.append(cycle)
                return

            if node_name in visited:
                return

            visited.add(node_name)
            rec_stack.add(node_name)

            if node_name in self._nodes:
                # Only follow required dependencies for cycle detection
                for dep_name in self._nodes[node_name].dependencies:
                    dfs(dep_name, path + [node_name])

            rec_stack.remove(node_name)

        for node_name in self._nodes:
            if node_name not in visited:
                dfs(node_name, [])

        if cycles:
            cycle_descriptions = []
            for cycle in cycles:
                cycle_descriptions.append(" -> ".join(cycle))

            raise CircularDependencyError(
                f"Circular dependencies detected: {'; '.join(cycle_descriptions)}"
            )

        return cycles

    def topological_sort(self) -> list[str]:
        """
        Perform topological sort to determine load order.

        Returns:
            List of node names in dependency order (dependencies first)

        Raises:
            CircularDependencyError: If circular dependencies exist
        """
        # First check for circular dependencies
        self.detect_circular_dependencies()

        # Kahn's algorithm for topological sorting
        in_degree = {}
        for node_name in self._nodes:
            in_degree[node_name] = len(self._nodes[node_name].dependencies)

        queue = [name for name, degree in in_degree.items() if degree == 0]
        result = []

        while queue:
            # Sort queue for deterministic results
            queue.sort()
            node_name = queue.pop(0)
            result.append(node_name)

            # Reduce in-degree for dependents
            if node_name in self._nodes:
                for dependent_name in self._nodes[node_name].dependents:
                    in_degree[dependent_name] -= 1
                    if in_degree[dependent_name] == 0:
                        queue.append(dependent_name)

        if len(result) != len(self._nodes):
            # This shouldn't happen if circular dependency detection works correctly
            raise CircularDependencyError("Failed to resolve all dependencies")

        return result

    def get_load_order(self) -> list[str]:
        """
        Get the optimal load order for all nodes.

        Returns:
            List of node names in load order (dependencies loaded first)
        """
        return self.topological_sort()

    def get_dependencies(self, node_name: str, recursive: bool = False) -> set[str]:
        """
        Get dependencies for a node.

        Args:
            node_name: Name of the node
            recursive: Whether to include transitive dependencies

        Returns:
            Set of dependency names
        """
        if node_name not in self._nodes:
            return set()

        if not recursive:
            return self._nodes[node_name].get_all_dependencies()

        # Get transitive dependencies
        visited = set()
        dependencies = set()

        def collect_deps(name: str) -> None:
            if name in visited or name not in self._nodes:
                return

            visited.add(name)
            node_deps = self._nodes[name].get_all_dependencies()
            dependencies.update(node_deps)

            for dep_name in node_deps:
                collect_deps(dep_name)

        collect_deps(node_name)
        return dependencies

    def validate_graph(self) -> None:
        """
        Validate the entire dependency graph.

        Raises:
            DependencyError: If graph is invalid
            CircularDependencyError: If circular dependencies exist
        """
        # Check for circular dependencies
        self.detect_circular_dependencies()

        # Check that all dependencies exist
        for node_name, node in self._nodes.items():
            for dep_name in node.get_all_dependencies():
                if dep_name not in self._nodes:
                    raise DependencyError(
                        f"Node '{node_name}' depends on '{dep_name}' which doesn't exist"
                    )

    def __len__(self) -> int:
        """Get the number of nodes in the graph."""
        return len(self._nodes)

    def __contains__(self, name: str) -> bool:
        """Check if a node exists in the graph."""
        return name in self._nodes

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"DependencyGraph(nodes={len(self._nodes)})"
