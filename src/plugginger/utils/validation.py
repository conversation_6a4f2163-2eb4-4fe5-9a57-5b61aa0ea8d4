# src/plugginger/utils/validation.py

"""
Input validation utilities for the Plugginger framework.

This module provides validation functions for plugin metadata, service definitions,
event patterns, and other framework inputs to ensure type safety and correctness.
"""

from __future__ import annotations

import inspect
import re
from collections.abc import Callable
from typing import Any

from plugginger.core.exceptions import (
    EventDefinitionError,
    PluginRegistrationError,
    ServiceDefinitionError,
    ValidationError,
)


def validate_plugin_name(name: str) -> None:
    """
    Validate a plugin name.

    Args:
        name: Plugin name to validate

    Raises:
        PluginRegistrationError: If name is invalid
    """
    if not name or not isinstance(name, str):
        raise PluginRegistrationError(f"Plugin name must be a non-empty string, got: {name!r}")

    if not re.match(r"^[a-zA-Z][a-zA-Z0-9_.-]*$", name):
        raise PluginRegistrationError(
            f"Plugin name '{name}' is invalid. Must start with a letter and contain only "
            f"letters, numbers, underscores, dots, and hyphens."
        )

    if len(name) > 100:
        raise PluginRegistrationError(f"Plugin name '{name}' is too long (max 100 characters)")


def validate_service_name(name: str) -> None:
    """
    Validate a service name.

    Args:
        name: Service name to validate

    Raises:
        ServiceDefinitionError: If name is invalid
    """
    if not name or not isinstance(name, str):
        raise ServiceDefinitionError(f"Service name must be a non-empty string, got: {name!r}")

    if not re.match(r"^[a-zA-Z][a-zA-Z0-9_.-]*$", name):
        raise ServiceDefinitionError(
            f"Service name '{name}' is invalid. Must start with a letter and contain only "
            f"letters, numbers, underscores, dots, and hyphens."
        )

    if len(name) > 100:
        raise ServiceDefinitionError(f"Service name '{name}' is too long (max 100 characters)")


def validate_event_pattern(pattern: str) -> None:
    """
    Validate an event pattern.

    Args:
        pattern: Event pattern to validate

    Raises:
        EventDefinitionError: If pattern is invalid
    """
    if not pattern or not isinstance(pattern, str):
        raise EventDefinitionError(f"Event pattern must be a non-empty string, got: {pattern!r}")

    # Allow wildcards and basic event naming
    if not re.match(r"^[a-zA-Z*][a-zA-Z0-9_.*-]*$", pattern):
        raise EventDefinitionError(
            f"Event pattern '{pattern}' is invalid. Must start with a letter or '*' and contain only "
            f"letters, numbers, underscores, dots, asterisks, and hyphens."
        )

    if len(pattern) > 200:
        raise EventDefinitionError(f"Event pattern '{pattern}' is too long (max 200 characters)")


def validate_version_string(version: str) -> None:
    """
    Validate a version string.

    Args:
        version: Version string to validate

    Raises:
        ValidationError: If version is invalid
    """
    if not version or not isinstance(version, str):
        raise ValidationError(f"Version must be a non-empty string, got: {version!r}")

    # Basic semantic versioning pattern
    if not re.match(r"^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$", version):
        raise ValidationError(
            f"Version '{version}' is invalid. Must follow semantic versioning (e.g., '1.0.0' or '1.0.0-alpha.1')"
        )


def validate_timeout_value(timeout: float | None) -> None:
    """
    Validate a timeout value.

    Args:
        timeout: Timeout value to validate

    Raises:
        ValidationError: If timeout is invalid
    """
    if timeout is None:
        return

    if not isinstance(timeout, int | float):
        raise ValidationError(f"Timeout must be a number or None, got: {type(timeout)}")

    if timeout <= 0:
        raise ValidationError(f"Timeout must be positive, got: {timeout}")

    if timeout > 3600:  # 1 hour max
        raise ValidationError(f"Timeout too large (max 3600 seconds), got: {timeout}")


def validate_priority_value(priority: int) -> None:
    """
    Validate a priority value.

    Args:
        priority: Priority value to validate

    Raises:
        ValidationError: If priority is invalid
    """
    if not isinstance(priority, int):
        raise ValidationError(f"Priority must be an integer, got: {type(priority)}")

    if priority < -1000 or priority > 1000:
        raise ValidationError(f"Priority must be between -1000 and 1000, got: {priority}")


def validate_method_signature(
    method: Callable[..., Any],
    *,
    require_async: bool = True,
    require_self: bool = True,
    min_params: int = 1,
    max_params: int | None = None,
    allow_varargs: bool = False,
    allow_kwargs: bool = False
) -> None:
    """
    Validate a method signature against requirements.

    Args:
        method: Method to validate
        require_async: Whether the method must be async
        require_self: Whether the first parameter must be 'self'
        min_params: Minimum number of parameters (including self)
        max_params: Maximum number of parameters (None for unlimited)
        allow_varargs: Whether *args is allowed
        allow_kwargs: Whether **kwargs is allowed

    Raises:
        ValidationError: If signature is invalid
    """
    if require_async and not inspect.iscoroutinefunction(method):
        raise ValidationError(f"Method '{method.__name__}' must be async")

    sig = inspect.signature(method)
    params = list(sig.parameters.values())

    if len(params) < min_params:
        raise ValidationError(
            f"Method '{method.__name__}' must have at least {min_params} parameters, got {len(params)}"
        )

    if max_params is not None and len(params) > max_params:
        raise ValidationError(
            f"Method '{method.__name__}' must have at most {max_params} parameters, got {len(params)}"
        )

    if require_self and (not params or params[0].name != 'self'):
        raise ValidationError(f"Method '{method.__name__}' first parameter must be 'self'")

    # Check for unsupported parameter types
    for param in params:
        if param.kind == inspect.Parameter.VAR_POSITIONAL and not allow_varargs:
            raise ValidationError(f"Method '{method.__name__}' cannot use *args parameters")

        if param.kind == inspect.Parameter.VAR_KEYWORD and not allow_kwargs:
            raise ValidationError(f"Method '{method.__name__}' cannot use **kwargs parameters")


def validate_dependency_list(dependencies: list[str] | None) -> None:
    """
    Validate a list of dependency names.

    Args:
        dependencies: List of dependency names to validate

    Raises:
        ValidationError: If dependencies are invalid
    """
    if dependencies is None:
        return

    if not isinstance(dependencies, list):
        raise ValidationError(f"Dependencies must be a list or None, got: {type(dependencies)}")

    for dep in dependencies:
        if not isinstance(dep, str):
            raise ValidationError(f"Dependency name must be a string, got: {type(dep)}")

        validate_plugin_name(dep)  # Reuse plugin name validation


def validate_event_data(event_data: dict[str, Any]) -> None:
    """
    Validate event data payload.

    Args:
        event_data: Event data to validate

    Raises:
        EventDefinitionError: If event data is invalid
    """
    if not isinstance(event_data, dict):
        raise EventDefinitionError(f"Event data must be a dictionary, got: {type(event_data)}")

    # Check for reserved keys
    reserved_keys = {"__type__", "__timestamp__", "__source__"}
    for key in event_data:
        if key in reserved_keys:
            raise EventDefinitionError(f"Event data cannot use reserved key: {key}")

        if not isinstance(key, str):
            raise EventDefinitionError(f"Event data keys must be strings, got: {type(key)}")


def validate_app_name(name: str) -> None:
    """
    Validate an application name.

    Args:
        name: Application name to validate

    Raises:
        ValidationError: If name is invalid
    """
    if not name or not isinstance(name, str):
        raise ValidationError(f"App name must be a non-empty string, got: {name!r}")

    if not re.match(r"^[a-zA-Z][a-zA-Z0-9_-]*$", name):
        raise ValidationError(
            f"App name '{name}' is invalid. Must start with a letter and contain only "
            f"letters, numbers, underscores, and hyphens."
        )

    if len(name) > 50:
        raise ValidationError(f"App name '{name}' is too long (max 50 characters)")


def is_valid_identifier(name: str) -> bool:
    """
    Check if a string is a valid Python identifier.

    Args:
        name: String to check

    Returns:
        True if valid identifier, False otherwise
    """
    return isinstance(name, str) and name.isidentifier() and not name.startswith("_")


def sanitize_name(name: str, max_length: int = 50) -> str:
    """
    Sanitize a name to make it a valid identifier.

    Args:
        name: Name to sanitize
        max_length: Maximum length for the result

    Returns:
        Sanitized name

    Raises:
        ValidationError: If name cannot be sanitized
    """
    if not isinstance(name, str):
        raise ValidationError(f"Name must be a string, got: {type(name)}")

    # Remove invalid characters and replace with underscores
    sanitized = re.sub(r"[^a-zA-Z0-9_]", "_", name)

    # Ensure it starts with a letter
    if not sanitized or not sanitized[0].isalpha():
        sanitized = "item_" + sanitized

    # Truncate if too long
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]

    # Ensure it's still valid
    if not is_valid_identifier(sanitized):
        raise ValidationError(f"Cannot sanitize name '{name}' to valid identifier")

    return sanitized
