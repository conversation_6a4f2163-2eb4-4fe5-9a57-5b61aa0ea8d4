# src/plugginger/utils/generic_proxy.py

"""
Generic plugin proxy for dependency injection.

This module provides the GenericPluginProxy class that enables lazy resolution
of plugin dependencies during the build process.
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any

from plugginger.core.exceptions import MissingDependencyError

if TYPE_CHECKING:
    from typing import Any as PluggingerAppInstance  # Use Any to avoid circular import issues
    from plugginger.api.plugin import PluginBase


class GenericPluginProxy:
    """
    Generic proxy for plugin dependencies in dependency injection.

    This proxy enables lazy resolution of plugin dependencies, allowing plugins
    to be injected before they are fully instantiated. The proxy resolves the
    actual plugin instance when attributes or methods are accessed.

    Attributes:
        _app_instance: Reference to the application instance
        _target_instance_id: Instance ID of the target plugin
        _target_registration_name: Registration name of the target plugin
        _resolved_plugin: Cached resolved plugin instance
        _is_resolved: Flag indicating if the plugin has been resolved
    """

    def __init__(
        self,
        app_instance: Any,
        target_instance_id: str,
        target_registration_name: str,
    ) -> None:
        """
        Initialize the generic plugin proxy.

        Args:
            app_instance: Application instance that contains the target plugin
            target_instance_id: Unique instance ID of the target plugin
            target_registration_name: Registration name of the target plugin
        """
        self._app_instance = app_instance
        self._target_instance_id = target_instance_id
        self._target_registration_name = target_registration_name
        self._resolved_plugin: Any = None
        self._is_resolved = False

    def _resolve_plugin(self) -> Any:
        """
        Resolve the target plugin instance.

        Returns:
            The resolved plugin instance

        Raises:
            MissingDependencyError: If the target plugin cannot be resolved
        """
        if not self._is_resolved:
            try:
                # Try to get the plugin instance from the app
                plugin_instance = self._app_instance.get_plugin_instance(self._target_instance_id)

                if plugin_instance is None:
                    raise MissingDependencyError(
                        f"Plugin '{self._target_registration_name}' (instance ID: '{self._target_instance_id}') "
                        f"not found or not yet available"
                    )

                self._resolved_plugin = plugin_instance
                self._is_resolved = True

            except Exception as e:
                if isinstance(e, MissingDependencyError):
                    raise
                raise MissingDependencyError(
                    f"Failed to resolve plugin '{self._target_registration_name}' "
                    f"(instance ID: '{self._target_instance_id}'): {e}"
                ) from e

        return self._resolved_plugin

    def __getattr__(self, name: str) -> Any:
        """
        Proxy attribute access to the resolved plugin.

        Args:
            name: Name of the attribute to access

        Returns:
            The attribute value from the resolved plugin

        Raises:
            MissingDependencyError: If the plugin cannot be resolved
            AttributeError: If the attribute doesn't exist on the resolved plugin
        """
        # Avoid infinite recursion for internal attributes
        if name.startswith('_'):
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

        plugin = self._resolve_plugin()
        return getattr(plugin, name)

    def __setattr__(self, name: str, value: Any) -> None:
        """
        Handle attribute setting.

        For internal attributes (starting with _), set them directly on the proxy.
        For other attributes, set them on the resolved plugin.

        Args:
            name: Name of the attribute to set
            value: Value to set
        """
        if name.startswith('_'):
            # Set internal proxy attributes directly
            super().__setattr__(name, value)
        else:
            # Set attributes on the resolved plugin
            plugin = self._resolve_plugin()
            setattr(plugin, name, value)

    def __call__(self, *args: Any, **kwargs: Any) -> Any:
        """
        Proxy method calls to the resolved plugin.

        Args:
            *args: Positional arguments to pass to the plugin
            **kwargs: Keyword arguments to pass to the plugin

        Returns:
            Result of calling the resolved plugin

        Raises:
            MissingDependencyError: If the plugin cannot be resolved
            TypeError: If the resolved plugin is not callable
        """
        plugin = self._resolve_plugin()
        return plugin(*args, **kwargs)

    def __repr__(self) -> str:
        """
        String representation for debugging.

        Returns:
            String representation of the proxy
        """
        status = "resolved" if self._is_resolved else "unresolved"
        return (
            f"GenericPluginProxy("
            f"target='{self._target_registration_name}', "
            f"instance_id='{self._target_instance_id}', "
            f"status={status})"
        )

    def __str__(self) -> str:
        """
        String representation for user display.

        Returns:
            String representation of the proxy or resolved plugin
        """
        if self._is_resolved and self._resolved_plugin is not None:
            return str(self._resolved_plugin)
        return f"<Proxy for {self._target_registration_name}>"
