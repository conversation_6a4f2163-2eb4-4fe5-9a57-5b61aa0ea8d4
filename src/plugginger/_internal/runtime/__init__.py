# src/plugginger/_internal/runtime/__init__.py

"""
Runtime components for the Plugginger framework.

This module provides internal runtime components that handle fault policies,
executor management, and other core runtime functionality.
"""

from plugginger._internal.runtime.executors import ExecutorRegistry
from plugginger._internal.runtime.fault_policy import FaultPolicyHandler

__all__ = [
    "FaultPolicyHandler",
    "ExecutorRegistry",
]
