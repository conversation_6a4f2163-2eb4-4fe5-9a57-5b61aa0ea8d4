# src/plugginger/_internal/runtime/fault_policy.py

"""
Fault policy handler for event listener error management.

This module provides the FaultPolicyHandler class that implements different
strategies for handling errors that occur within event listeners during
event dispatch according to the configured EventListenerFaultPolicy.
"""

from __future__ import annotations

import asyncio

from plugginger.core.config import EventListenerFaultPolicy
from plugginger.core.types import EventHandlerType, LoggerCallable


class FaultPolicyHandler:
    """
    Handles errors in event listeners according to a configured fault policy.
    
    This class implements different strategies for dealing with exceptions that
    occur during event listener execution, including logging, isolation, and
    fail-fast behavior.
    
    Attributes:
        _policy: The configured fault policy strategy
        _logger: Logger function for error reporting
        _dead_listeners: Set of isolated listener IDs that should not be invoked
    """

    def __init__(self, policy: EventListenerFaultPolicy, logger: LoggerCallable) -> None:
        """
        Initialize the fault policy handler.
        
        Args:
            policy: The fault policy strategy to use for error handling
            logger: Logger function for error reporting and debugging
        """
        self._policy = policy
        self._logger = logger
        self._dead_listeners: set[int] = set()

    def should_invoke(self, listener: EventHandlerType) -> bool:
        """
        Determine whether a listener should be invoked based on fault policy.
        
        This method checks if a listener has been isolated due to previous errors
        when using the ISOLATE_AND_LOG policy.
        
        Args:
            listener: The event listener function to check
            
        Returns:
            True if the listener should be invoked, False if it should be skipped
        """
        if self._policy == EventListenerFaultPolicy.ISOLATE_AND_LOG:
            listener_id = id(listener)
            if listener_id in self._dead_listeners:
                return False
        return True

    def handle_error(self, listener_qualname: str, listener_id: int, exc: Exception) -> None:
        """
        Handle an error that occurred during event listener execution.
        
        This method implements the configured fault policy strategy:
        - LOG_AND_CONTINUE: Log the error and continue processing
        - FAIL_FAST: Log the error and raise appropriate exception
        - ISOLATE_AND_LOG: Log the error and isolate the failing listener
        
        Args:
            listener_qualname: Qualified name of the failing listener for logging
            listener_id: Unique ID of the listener (from id() function)
            exc: The exception that occurred during listener execution
            
        Raises:
            EventListenerTimeoutError: When exc is TimeoutError and policy is FAIL_FAST
            EventListenerUnhandledError: When exc is other exception and policy is FAIL_FAST
        """
        # Always log the error regardless of policy
        self._logger(
            f"[Plugginger] Error in event listener '{listener_qualname}': {exc}"
        )

        if self._policy == EventListenerFaultPolicy.FAIL_FAST:
            # Import exceptions only when needed to avoid circular imports
            from plugginger.core.exceptions import (
                EventListenerTimeoutError,
                EventListenerUnhandledError,
            )

            if isinstance(exc, asyncio.TimeoutError):
                raise EventListenerTimeoutError(
                    f"Event listener '{listener_qualname}' timed out (FAIL_FAST policy active)."
                ) from exc
            else:
                raise EventListenerUnhandledError(
                    f"Unhandled exception in event listener '{listener_qualname}' (FAIL_FAST policy active)."
                ) from exc

        elif self._policy == EventListenerFaultPolicy.ISOLATE_AND_LOG:
            # Add the listener to the dead listeners set to prevent future invocation
            self._dead_listeners.add(listener_id)
            self._logger(
                f"[Plugginger] Listener '{listener_qualname}' isolated due to error"
            )

        # For LOG_AND_CONTINUE, no additional action is needed after logging
