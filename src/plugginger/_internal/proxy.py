# src/plugginger/_internal/proxy.py

"""
Generic plugin proxy for dependency injection.

This module provides the GenericPluginProxy class that enables lazy resolution
of plugin dependencies during the build process with service call support.
"""

from __future__ import annotations

import inspect
from typing import TYPE_CHECKING, Any, Awaitable

from plugginger.core.exceptions import MissingDependencyError

if TYPE_CHECKING:
    from plugginger.api.app import PluggingerAppInstance


class GenericPluginProxy:
    """
    Generic proxy for plugin dependencies in dependency injection.

    This proxy enables lazy resolution of plugin dependencies, allowing plugins
    to be injected before they are fully instantiated. The proxy resolves the
    actual plugin instance when attributes or methods are accessed, and provides
    special handling for service method calls.

    Attributes:
        _app_instance: Reference to the application instance
        _target_instance_id: Instance ID of the target plugin
        _target_plugin_name: Registration name of the target plugin (for service calls)
        _resolved_plugin: Cached resolved plugin instance
        _is_resolved: Flag indicating if the plugin has been resolved
    """

    def __init__(
        self,
        app_instance: PluggingerAppInstance,
        target_instance_id: str,
        target_plugin_name: str,
    ) -> None:
        """
        Initialize the generic plugin proxy.

        Args:
            app_instance: Application instance that contains the target plugin
            target_instance_id: Unique instance ID of the target plugin
            target_plugin_name: Registration name of the target plugin (for service calls)
        """
        self._app_instance = app_instance
        self._target_instance_id = target_instance_id
        self._target_plugin_name = target_plugin_name
        self._resolved_plugin: Any = None
        self._is_resolved = False

    def _resolve_plugin(self) -> Any:
        """
        Resolve the target plugin instance.

        Returns:
            The resolved plugin instance

        Raises:
            MissingDependencyError: If the target plugin cannot be resolved
        """
        if not self._is_resolved:
            try:
                # Try to get the plugin instance from the app
                plugin_instance = self._app_instance.get_plugin_instance(self._target_instance_id)

                if plugin_instance is None:
                    raise MissingDependencyError(
                        f"Plugin '{self._target_plugin_name}' (instance ID: '{self._target_instance_id}') "
                        f"not found or not yet available"
                    )

                self._resolved_plugin = plugin_instance
                self._is_resolved = True

            except Exception as e:
                if isinstance(e, MissingDependencyError):
                    raise
                raise MissingDependencyError(
                    f"Failed to resolve plugin '{self._target_plugin_name}' "
                    f"(instance ID: '{self._target_instance_id}'): {e}"
                ) from e

        return self._resolved_plugin

    def _create_service_wrapper(self, service_method_name: str) -> Any:
        """
        Create a wrapper for service method calls.

        This wrapper checks if the method is a service and routes the call
        through the app's service dispatcher if so, otherwise calls the method directly.

        Args:
            service_method_name: Name of the service method

        Returns:
            Wrapper function for the service method
        """
        def service_wrapper(*args: Any, **kwargs: Any) -> Any:
            # Construct the fully qualified service name
            fully_qualified_service_name = f"{self._target_plugin_name}.{service_method_name}"
            
            # Check if this is a registered service
            if self._app_instance.has_service(fully_qualified_service_name):
                # Route through service dispatcher
                return self._app_instance.call_service(fully_qualified_service_name, *args, **kwargs)
            else:
                # Fall back to direct method call on resolved plugin
                plugin = self._resolve_plugin()
                method = getattr(plugin, service_method_name)
                return method(*args, **kwargs)

        return service_wrapper

    def __getattr__(self, name: str) -> Any:
        """
        Proxy attribute access to the resolved plugin.

        For methods that might be services, returns a wrapper that routes
        through the service dispatcher. For other attributes, returns the
        attribute directly from the resolved plugin.

        Args:
            name: Name of the attribute to access

        Returns:
            The attribute value from the resolved plugin or a service wrapper

        Raises:
            MissingDependencyError: If the plugin cannot be resolved
            AttributeError: If the attribute doesn't exist on the resolved plugin
        """
        # Avoid infinite recursion for internal attributes
        if name.startswith('_'):
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

        plugin = self._resolve_plugin()
        attr = getattr(plugin, name)
        
        # If it's a callable (method), check if it might be a service
        if callable(attr):
            # Check if this method has service metadata
            service_metadata = getattr(attr, '_plugginger_service_metadata', None)
            if service_metadata:
                # Return a service wrapper for service methods
                return self._create_service_wrapper(name)
        
        # Return the attribute directly for non-service attributes
        return attr

    def __setattr__(self, name: str, value: Any) -> None:
        """
        Handle attribute setting.

        For internal attributes (starting with _), set them directly on the proxy.
        For other attributes, set them on the resolved plugin.

        Args:
            name: Name of the attribute to set
            value: Value to set
        """
        if name.startswith('_'):
            # Set internal proxy attributes directly
            super().__setattr__(name, value)
        else:
            # Set attributes on the resolved plugin
            plugin = self._resolve_plugin()
            setattr(plugin, name, value)

    def __call__(self, *args: Any, **kwargs: Any) -> Any:
        """
        Proxy method calls to the resolved plugin.

        Args:
            *args: Positional arguments to pass to the plugin
            **kwargs: Keyword arguments to pass to the plugin

        Returns:
            Result of calling the resolved plugin

        Raises:
            MissingDependencyError: If the plugin cannot be resolved
            TypeError: If the resolved plugin is not callable
        """
        plugin = self._resolve_plugin()
        return plugin(*args, **kwargs)

    def __repr__(self) -> str:
        """
        String representation for debugging.

        Returns:
            String representation of the proxy
        """
        status = "resolved" if self._is_resolved else "unresolved"
        return (
            f"GenericPluginProxy("
            f"target='{self._target_plugin_name}', "
            f"instance_id='{self._target_instance_id}', "
            f"status={status})"
        )

    def __str__(self) -> str:
        """
        String representation for user display.

        Returns:
            String representation of the proxy or resolved plugin
        """
        if self._is_resolved and self._resolved_plugin is not None:
            return str(self._resolved_plugin)
        return f"<Proxy for {self._target_plugin_name}>"
