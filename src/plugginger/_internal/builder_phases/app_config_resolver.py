# src/plugginger/_internal/builder_phases/app_config_resolver.py

"""
Configuration resolution and validation for the PluggingerAppBuilder.

This module contains the AppConfigResolver class which handles the resolution
and validation of GlobalAppConfig instances during the build process.
"""

from __future__ import annotations

import logging
from typing import Any

from pydantic import ValidationError

from plugginger.config.models import GlobalAppConfig
from plugginger.core.exceptions import ConfigurationError


class AppConfigResolver:
    """
    Handles resolution and validation of global application configuration.
    
    This class is responsible for taking various forms of configuration input
    (GlobalAppConfig instances, dictionaries, or None) and producing a validated
    GlobalAppConfig instance with appropriate defaults and consistency checks.
    """
    
    def __init__(self, logger: logging.Logger | None = None) -> None:
        """
        Initialize the configuration resolver.
        
        Args:
            logger: Optional logger for configuration resolution messages
        """
        self._logger = logger or logging.getLogger(__name__)
    
    def resolve_and_validate(
        self,
        app_name: str,
        current_max_depth: int,
        app_config_input: GlobalAppConfig | dict[str, Any] | None
    ) -> GlobalAppConfig:
        """
        Resolve and validate global application configuration.
        
        Takes various forms of configuration input and produces a validated
        GlobalAppConfig instance with proper defaults and consistency checks.
        
        Args:
            app_name: The name of the application being built
            current_max_depth: The current maximum fractal depth from the builder
            app_config_input: Configuration input in various forms
            
        Returns:
            A validated GlobalAppConfig instance
            
        Raises:
            ConfigurationError: If configuration validation fails
        """
        if app_config_input is None:
            cfg = GlobalAppConfig(app_name=app_name, max_fractal_depth=current_max_depth)
            self._logger.debug(f"No app_config_input provided for '{app_name}', using default GlobalAppConfig.")
            return cfg
            
        if isinstance(app_config_input, GlobalAppConfig):
            # Ensure app_name consistency
            if app_config_input.app_name != app_name and app_name:
                self._logger.warning(
                    f"Provided GlobalAppConfig.app_name ('{app_config_input.app_name}') differs from "
                    f"builder's app_name ('{app_name}'). Builder's name ('{app_name}') will be used for this instance."
                )
            app_config_input.app_name = app_name  # Builder's name is authoritative
            return app_config_input
            
        if isinstance(app_config_input, dict):
            try:
                # Ensure app_name from builder is used, and max_fractal_depth is respected
                config_dict_with_defaults = {
                    "app_name": app_name,
                    "max_fractal_depth": current_max_depth,
                    **app_config_input
                }
                return GlobalAppConfig(**config_dict_with_defaults)
            except ValidationError as e:
                self._logger.error(f"Invalid app_config dictionary for '{app_name}': {e.errors()}")
                raise ConfigurationError(
                    f"Invalid app_config dictionary for '{app_name}'.",
                    validation_errors=e.errors()
                ) from e
        
        raise ConfigurationError(
            f"app_config_input for '{app_name}' must be a GlobalAppConfig instance, "
            f"a compatible dict, or None. Got {type(app_config_input)}."
        )
