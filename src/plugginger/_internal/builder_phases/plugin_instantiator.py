# src/plugginger/_internal/builder_phases/plugin_instantiator.py

"""
Plugin instantiation and dependency injection for the PluggingerAppBuilder.

This module contains the PluginInstantiator class which handles the instantiation
of plugins with proper dependency injection and configuration preparation.
"""

from __future__ import annotations

import logging
from typing import TYPE_CHECKING, Any, Callable

from pydantic import BaseModel, ValidationError
from typing import cast

from plugginger._internal.proxy import GenericPluginProxy
from plugginger.api.depends import Depends
from plugginger.config.models import GlobalAppConfig
from plugginger.core.exceptions import ConfigurationError, PluginRegistrationError

if TYPE_CHECKING:
    from plugginger.api.app import PluggingerAppInstance
    from plugginger.api.plugin import PluginBase

# Type alias for instance ID generator function
InstanceIdGeneratorFunc = Callable[[str], str]


class PluginInstantiator:
    """
    Handles plugin instantiation with dependency injection and configuration.
    
    This class is responsible for instantiating plugins in the correct order,
    injecting their dependencies via GenericPluginProxy instances, and preparing
    their configuration objects for the setup phase.
    """
    
    def __init__(self, logger: logging.Logger | None = None) -> None:
        """
        Initialize the plugin instantiator.
        
        Args:
            logger: Optional logger for instantiation messages
        """
        self._logger = logger or logging.getLogger(__name__)
    
    def instantiate_all(
        self,
        sorted_registration_names: list[str],
        registered_item_classes: dict[str, type[PluginBase]],
        provisional_app_instance: PluggingerAppInstance,
        global_config: GlobalAppConfig,
        dependency_declarations: dict[str, list[Depends]],
        instance_id_generator_func: InstanceIdGeneratorFunc,
        metadata_getter: Callable[[type[Any], str, str], Any]
    ) -> tuple[dict[str, PluginBase], dict[str, BaseModel]]:
        """
        Instantiate all plugins with dependency injection and prepare configurations.
        
        Args:
            sorted_registration_names: Plugin names in instantiation order
            registered_item_classes: Map of registration names to plugin classes
            provisional_app_instance: App instance for plugin constructors
            global_config: Global configuration containing plugin configs
            dependency_declarations: Map of registration names to their dependencies
            instance_id_generator_func: Function to generate unique instance IDs
            metadata_getter: Function to get plugin metadata attributes
            
        Returns:
            Tuple of (plugin_instances_map, plugin_configs_for_setup)
            
        Raises:
            PluginRegistrationError: If plugin instantiation fails
            ConfigurationError: If plugin configuration validation fails
        """
        plugin_instances_map: dict[str, PluginBase] = {}  # registration_name -> instance
        plugin_configs_for_setup: dict[str, BaseModel] = {}  # instance_id -> validated_config
        
        for registration_name in sorted_registration_names:
            plugin_class = registered_item_classes[registration_name]
            instance_id = instance_id_generator_func(registration_name)
            
            # Prepare dependency injection kwargs
            injected_deps_kwargs = self._prepare_dependency_injection(
                registration_name,
                dependency_declarations,
                provisional_app_instance,
                instance_id_generator_func,
                metadata_getter
            )
            
            # Instantiate the plugin
            plugin_instance = self._instantiate_plugin(
                plugin_class,
                registration_name,
                provisional_app_instance,
                injected_deps_kwargs
            )
            
            # Set instance ID and store
            plugin_instance._plugginger_instance_id = instance_id
            plugin_instances_map[registration_name] = plugin_instance
            
            # Prepare configuration for setup
            config = self._prepare_plugin_config(
                plugin_class,
                instance_id,
                registration_name,
                global_config,
                metadata_getter
            )
            plugin_configs_for_setup[instance_id] = config
            
            self._logger.debug(f"Instantiated plugin '{registration_name}' with instance ID '{instance_id}'")
        
        return plugin_instances_map, plugin_configs_for_setup
    
    def _prepare_dependency_injection(
        self,
        registration_name: str,
        dependency_declarations: dict[str, list[Depends]],
        provisional_app_instance: PluggingerAppInstance,
        instance_id_generator_func: InstanceIdGeneratorFunc,
        metadata_getter: Callable[[type[Any], str, str], Any]
    ) -> dict[str, GenericPluginProxy]:
        """Prepare dependency injection kwargs for a plugin."""
        injected_deps_kwargs: dict[str, GenericPluginProxy] = {}
        
        for dep_obj in dependency_declarations.get(registration_name, []):
            dep_target_reg_name: str
            if isinstance(dep_obj.plugin_identifier, str):
                dep_target_reg_name = dep_obj.plugin_identifier
            else:
                dep_target_reg_name = metadata_getter(
                    dep_obj.plugin_identifier, "_plugginger_plugin_name", "di_target_resolution"
                )
            
            # The key for kwargs must match the __init__ parameter name
            target_plugin_instance_id = instance_id_generator_func(dep_target_reg_name)
            injected_deps_kwargs[dep_target_reg_name] = GenericPluginProxy(
                provisional_app_instance,  # App context for proxied calls
                target_plugin_instance_id,  # Instance ID of the dependency
                dep_target_reg_name  # Registration name of the dependency (for service FQN)
            )
        
        return injected_deps_kwargs
    
    def _instantiate_plugin(
        self,
        plugin_class: type[PluginBase],
        registration_name: str,
        provisional_app_instance: PluggingerAppInstance,
        injected_deps_kwargs: dict[str, GenericPluginProxy]
    ) -> PluginBase:
        """Instantiate a single plugin with error handling."""
        try:
            # Pass provisional_app_instance as 'app' to constructor
            plugin_instance = plugin_class(app=provisional_app_instance, **injected_deps_kwargs)
            return plugin_instance
        except Exception as e:
            self._logger.error(
                f"Failed to instantiate plugin '{registration_name}' "
                f"(class: {plugin_class.__name__}): {e!r}",
                exc_info=True
            )
            raise PluginRegistrationError(f"Instantiation failed for '{registration_name}'.") from e
    
    def _prepare_plugin_config(
        self,
        plugin_class: type[PluginBase],
        instance_id: str,
        registration_name: str,
        global_config: GlobalAppConfig,
        metadata_getter: Callable[[type[Any], str, str], Any]
    ) -> BaseModel:
        """Prepare and validate configuration for a plugin."""
        plugin_class_config_schema = metadata_getter(
            plugin_class, "_plugginger_config_schema", "config_schema_lookup"
        )
        raw_config_for_plugin = global_config.plugin_configs.get(instance_id, {})
        
        if plugin_class_config_schema:
            try:
                validated_config = plugin_class_config_schema(**raw_config_for_plugin)
                return validated_config
            except ValidationError as e:
                err_msg = (
                    f"Invalid configuration for plugin instance '{instance_id}' "
                    f"(class {plugin_class.__name__}, registration name '{registration_name}'). "
                    f"Pydantic errors: {e.errors()}"
                )
                self._logger.error(err_msg)
                raise ConfigurationError(err_msg, validation_errors=e.errors()) from e
        elif raw_config_for_plugin:
            self._logger.warning(
                f"Plugin instance '{instance_id}' received configuration but has no "
                "`config_schema` defined. Config passed as raw dict to `setup`."
            )
            return cast(BaseModel, raw_config_for_plugin)  # Pass as dict
        else:  # No config in GlobalAppConfig and no schema, or schema allows all defaults
            if plugin_class_config_schema:  # Schema exists, use its defaults
                return plugin_class_config_schema()
            else:  # No schema, no config: empty dict
                return cast(BaseModel, {})
