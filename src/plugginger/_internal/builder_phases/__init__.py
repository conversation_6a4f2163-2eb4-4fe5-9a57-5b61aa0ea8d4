# src/plugginger/_internal/builder_phases/__init__.py

"""
Builder phases for the PluggingerAppBuilder.

This package contains specialized classes that handle different phases of the
application building process, extracted from the monolithic build() method
to improve maintainability and reduce complexity.
"""

from __future__ import annotations

from .app_config_resolver import AppConfigResolver
from .dependency_orchestrator import DependencyOrchestrator
from .interface_registrar import InterfaceRegistrar
from .plugin_instantiator import PluginInstantiator

__all__ = [
    "AppConfigResolver",
    "DependencyOrchestrator", 
    "InterfaceRegistrar",
    "PluginInstantiator",
]
