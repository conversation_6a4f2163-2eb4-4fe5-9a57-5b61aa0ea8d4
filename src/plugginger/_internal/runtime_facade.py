# src/plugginger/_internal/runtime_facade.py

"""
Runtime facade for the Plugginger framework.

This module provides the RuntimeFacade class that orchestrates all runtime
components including service dispatch, event handling, lifecycle management,
and executor management.
"""

from __future__ import annotations

from collections.abc import Iterable
from typing import Any

from pydantic import BaseModel

from plugginger._internal.runtime import (
    EventDispatcher,
    ExecutorRegistry,
    FaultPolicyHandler,
    LifecycleManager,
    ServiceDispatcher,
)
from plugginger.api.plugin import PluginBase
from plugginger.config.models import ExecutorConfig, GlobalAppConfig
from plugginger.core.config import EventListenerFaultPolicy
from plugginger.core.types import EventHandlerType, LoggerCallable, ServiceMethodType


class RuntimeFacade:
    """
    Facade that orchestrates all runtime components of the Plugginger framework.

    This class provides a unified interface to all runtime functionality including
    service dispatch, event handling, plugin lifecycle management, and background
    task execution. It acts as the central coordinator for the application runtime.

    Attributes:
        _logger: Logger function for debugging and error reporting
        _fault_handler: Handler for event listener fault policies
        _service_dispatcher: Dispatcher for service registration and execution
        _event_dispatcher: Dispatcher for event listener registration and dispatch
        _executor_registry: Registry for managing thread pool executors
        _lifecycle_manager: Manager for plugin lifecycle operations (set during finalize_setup)
        _plugin_map_by_id: Mapping of plugin instance IDs to plugin instances
    """

    def __init__(
        self,
        global_config: GlobalAppConfig,
        logger: LoggerCallable,
    ) -> None:
        """
        Initialize the runtime facade with core components.

        Args:
            global_config: Global application configuration
            logger: Logger function for debugging and error reporting
        """
        self._logger = logger
        self._global_config = global_config

        # Initialize components using factory methods
        self._fault_handler = self._create_fault_handler(global_config, logger)
        self._service_dispatcher = self._create_service_dispatcher(logger)
        self._event_dispatcher = self._create_event_dispatcher(global_config, logger)
        self._executor_registry = self._create_executor_registry(global_config, logger)

        # Lifecycle manager and plugin map will be set during finalize_setup
        self._lifecycle_manager: LifecycleManager | None = None
        self._plugin_map_by_id: dict[str, PluginBase] = {}

    def _create_fault_handler(self, global_config: GlobalAppConfig, logger: LoggerCallable) -> FaultPolicyHandler:
        """Create fault policy handler from global configuration."""
        return FaultPolicyHandler(global_config.event_listener_fault_policy, logger)

    def _create_service_dispatcher(self, logger: LoggerCallable) -> ServiceDispatcher:
        """Create service dispatcher."""
        return ServiceDispatcher(logger)

    def _create_event_dispatcher(self, global_config: GlobalAppConfig, logger: LoggerCallable) -> EventDispatcher:
        """Create event dispatcher with configuration from global config."""
        return EventDispatcher(
            fault_handler=self._fault_handler,
            logger=logger,
            default_listener_timeout=global_config.default_event_listener_timeout_seconds,
            max_concurrent_listener_tasks=getattr(global_config, 'max_concurrent_listener_tasks', 50),
        )

    def _create_executor_registry(self, global_config: GlobalAppConfig, logger: LoggerCallable) -> ExecutorRegistry:
        """Create executor registry with default executor and all configured executors."""
        # Use first executor as default, or create a default one
        default_executor_config = global_config.executors[0] if global_config.executors else ExecutorConfig(name="default")
        registry = ExecutorRegistry(default_executor_config, logger)

        # Register all additional executors from config
        for executor_config in global_config.executors[1:]:
            registry.register_executor(executor_config.name, executor_config)

        return registry

    def finalize_setup(
        self,
        plugins_in_order: Iterable[PluginBase],
        plugin_configs_for_setup: dict[str, BaseModel],
    ) -> None:
        """
        Finalize the runtime setup with plugin information.

        This method creates the lifecycle manager with the final plugin list
        and configurations prepared by the application builder.

        Args:
            plugins_in_order: Plugin instances in dependency-resolved setup order
            plugin_configs_for_setup: Mapping of plugin instance IDs to config models
        """
        # Convert to list to allow multiple iterations
        plugins_list = list(plugins_in_order)

        # Build plugin map by instance ID
        for plugin in plugins_list:
            instance_id = getattr(plugin, '_plugginger_instance_id', None)
            if instance_id:
                self._plugin_map_by_id[instance_id] = plugin

        self._lifecycle_manager = LifecycleManager(
            plugins_in_setup_order=plugins_list,
            plugin_configs_for_setup=plugin_configs_for_setup,
            logger=self._logger,
        )
        self._logger("[Plugginger] Runtime facade setup finalized")

    # Service dispatcher delegation methods
    def add_service(self, fully_qualified_name: str, service_coroutine: ServiceMethodType[..., Any]) -> None:
        """Register a service with the service dispatcher."""
        self._service_dispatcher.add_service(fully_qualified_name, service_coroutine)

    async def call_service(self, fully_qualified_name: str, *args: Any, **kwargs: Any) -> Any:
        """Execute a registered service."""
        return await self._service_dispatcher.call_service(fully_qualified_name, *args, **kwargs)

    def has_service(self, name: str) -> bool:
        """Check if a service is registered."""
        return self._service_dispatcher.has_service(name)

    def list_services(self) -> list[str]:
        """Get a list of all registered service names."""
        return self._service_dispatcher.list_services()

    def remove_service(self, name: str) -> bool:
        """Remove a registered service."""
        return self._service_dispatcher.remove_service(name)

    # Event dispatcher delegation methods
    def add_event_listener(self, event_pattern: str, listener_coroutine: EventHandlerType) -> None:
        """Register an event listener."""
        self._event_dispatcher.add_listener(event_pattern, listener_coroutine)

    async def emit_event(self, event_type: str, event_data: dict[str, Any]) -> None:
        """Emit an event to all matching listeners."""
        await self._event_dispatcher.emit_event(event_type, event_data)

    def remove_event_listener(self, event_pattern: str, listener_coroutine: EventHandlerType) -> bool:
        """Remove an event listener."""
        return self._event_dispatcher.remove_listener(event_pattern, listener_coroutine)

    def list_event_patterns(self) -> list[str]:
        """Get a list of all registered event patterns."""
        return self._event_dispatcher.list_patterns()

    # Executor registry delegation methods
    def register_executor(self, name: str, config_or_executor: ExecutorConfig | Any) -> None:
        """Register a new executor."""
        self._executor_registry.register_executor(name, config_or_executor)

    def get_executor(self, name: str | None = None) -> Any:
        """Get an executor by name."""
        if name is None:
            return self._executor_registry.get_executor()
        return self._executor_registry.get_executor(name)

    # Lifecycle management delegation methods
    async def setup_all_plugins(self) -> None:
        """Set up all plugins in dependency order."""
        if self._lifecycle_manager is None:
            raise RuntimeError("Runtime facade not finalized - call finalize_setup() first")
        await self._lifecycle_manager.setup_all_plugins()

    async def teardown_all_plugins(self) -> None:
        """Tear down all plugins in reverse order."""
        if self._lifecycle_manager is None:
            raise RuntimeError("Runtime facade not finalized - call finalize_setup() first")
        await self._lifecycle_manager.teardown_all_plugins()

    def get_plugins_were_setup_flag(self) -> bool:
        """Get the current plugin setup status."""
        if self._lifecycle_manager is None:
            return False
        return self._lifecycle_manager.get_plugins_were_setup_flag()

    def get_plugin_by_id(self, plugin_instance_id: str) -> PluginBase | None:
        """
        Get a plugin instance by its unique instance ID.

        Args:
            plugin_instance_id: The unique instance ID of the plugin

        Returns:
            Plugin instance if found, None otherwise
        """
        return self._plugin_map_by_id.get(plugin_instance_id)

    # Shutdown methods
    async def shutdown(self) -> None:
        """
        Shutdown all runtime components gracefully.

        This method shuts down the event dispatcher and executor registry,
        ensuring all background tasks and threads are properly cleaned up.
        """
        self._logger("[Plugginger] Runtime facade shutdown initiated")

        # Shutdown event dispatcher (cancels all active listener tasks)
        await self._event_dispatcher.shutdown()

        # Shutdown executor registry (stops all thread pools)
        self._executor_registry.shutdown_executors(wait=True)

        self._logger("[Plugginger] Runtime facade shutdown completed")
