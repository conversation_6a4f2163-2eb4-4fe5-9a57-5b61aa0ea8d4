# src/plugginger/_internal/validation.py

"""
Dependency validation for the Plugginger framework.

This module provides the DependencyValidator class for validating plugin
dependencies, versions, and dependency injection signatures.
"""

from __future__ import annotations

import inspect
import re
from collections.abc import Callable
from dataclasses import dataclass
from typing import Any

from packaging.specifiers import InvalidSpecifier, SpecifierSet
from packaging.version import InvalidVersion, Version

from plugginger._internal.graph import DependencyGraph
from plugginger.api.depends import Depends
from plugginger.api.plugin import PluginBase
from plugginger.core.exceptions import (
    ConfigurationError,
    DependencyError,
    DependencyVersionConflictError,
    EventDefinitionError,
    MissingDependencyError,
    PluginRegistrationError,
    ServiceDefinitionError,
    ValidationError,
)

# Type aliases for resolver functions
VersionResolverFunc = Callable[[str], str]
ClassResolverFunc = Callable[[str], type[PluginBase]]


@dataclass
class ValidationConfig:
    """
    Configuration for method signature validation.

    This class encapsulates all validation parameters for method signature checking,
    enabling different validation profiles for different types of plugin methods.

    Attributes:
        require_async: Whether the method must be async
        require_self: Whether the first parameter must be 'self'
        min_params: Minimum number of parameters (including self)
        max_params: Maximum number of parameters (None for unlimited)
        allow_varargs: Whether *args parameters are allowed
        allow_kwargs: Whether **kwargs parameters are allowed
        require_return_annotation: Whether return type annotation is required
        allowed_return_types: Set of allowed return types (None for any)
        require_param_annotations: Whether parameter type annotations are required
        context_name: Descriptive name for error messages
    """
    require_async: bool = True
    require_self: bool = True
    min_params: int = 1
    max_params: int | None = None
    allow_varargs: bool = False
    allow_kwargs: bool = False
    require_return_annotation: bool = True
    allowed_return_types: set[type] | None = None
    require_param_annotations: bool = True
    context_name: str = "method"


class ValidationProfiles:
    """
    Predefined validation profiles for common plugin method types.

    This class provides standard validation configurations for different
    types of plugin methods, ensuring consistent validation across the framework.
    """

    SERVICE_METHOD = ValidationConfig(
        require_async=True,
        require_self=True,
        min_params=1,
        require_return_annotation=True,
        require_param_annotations=True,
        context_name="service method"
    )

    EVENT_HANDLER = ValidationConfig(
        require_async=True,
        require_self=True,
        min_params=2,  # self + event_data
        max_params=3,  # self + event_data + matched_event_type
        require_return_annotation=False,
        require_param_annotations=True,
        context_name="event handler"
    )

    SETUP_METHOD = ValidationConfig(
        require_async=True,
        require_self=True,
        min_params=1,
        max_params=2,  # self + optional config
        require_return_annotation=False,
        require_param_annotations=False,  # Config can be untyped dict
        context_name="setup method"
    )

    TEARDOWN_METHOD = ValidationConfig(
        require_async=True,
        require_self=True,
        min_params=1,
        max_params=1,  # Only self
        require_return_annotation=False,
        require_param_annotations=False,
        context_name="teardown method"
    )

    PLUGIN_CONSTRUCTOR = ValidationConfig(
        require_async=False,
        require_self=True,
        min_params=1,
        allow_kwargs=True,  # For **injected_dependencies
        require_return_annotation=False,
        require_param_annotations=True,
        context_name="plugin constructor"
    )


class DependencyValidator:
    """
    Validator for plugin dependencies, versions, and injection signatures.

    This class provides comprehensive validation of plugin dependency graphs,
    including structure validation, version constraint checking, and dependency
    injection signature validation.

    Attributes:
        _version_resolver: Function to resolve plugin versions by name
        _class_resolver: Function to resolve plugin classes by name
    """

    def __init__(
        self,
        version_resolver_func: VersionResolverFunc,
        class_resolver_func: ClassResolverFunc,
    ) -> None:
        """
        Initialize the dependency validator.

        Args:
            version_resolver_func: Function that takes a plugin name and returns its version
            class_resolver_func: Function that takes a plugin name and returns its class
        """
        self._version_resolver = version_resolver_func
        self._class_resolver = class_resolver_func

    def validate_graph_structure(
        self,
        graph: DependencyGraph[str],
        all_registered_plugin_names: set[str],
    ) -> None:
        """
        Validate that all dependencies in the graph reference existing plugins.

        This method checks that every prerequisite node referenced in dependency
        edges actually exists in the set of registered plugins.

        Args:
            graph: Dependency graph to validate
            all_registered_plugin_names: Set of all registered plugin names

        Raises:
            MissingDependencyError: If any dependency references a non-existent plugin
        """
        for node in graph.get_all_nodes():
            prerequisites = graph.get_prerequisites(node)
            for prerequisite in prerequisites:
                if prerequisite not in all_registered_plugin_names:
                    raise MissingDependencyError(
                        f"Plugin '{node}' depends on '{prerequisite}' which is not registered"
                    )

    def validate_dependency_versions_and_signatures(
        self,
        plugin_dependency_declarations: dict[str, list[Depends]],
    ) -> None:
        """
        Validate dependency versions and injection signatures.

        This method performs comprehensive validation of plugin dependencies:
        1. Version constraint validation against actual plugin versions
        2. Dependency injection signature validation
        3. Completeness check for required constructor parameters

        Args:
            plugin_dependency_declarations: Mapping of plugin names to their dependency lists

        Raises:
            DependencyVersionConflictError: If version constraints are not satisfied
            DependencyError: If injection signatures are invalid or incomplete
            PluginRegistrationError: If plugin versions are invalid
            ConfigurationError: If version constraints are malformed
        """
        for dependent_plugin_name, depends_list in plugin_dependency_declarations.items():
            # Get the dependent plugin class for signature validation
            try:
                dependent_plugin_class = self._class_resolver(dependent_plugin_name)
            except Exception as e:
                raise DependencyError(
                    f"Cannot resolve class for plugin '{dependent_plugin_name}'"
                ) from e

            # Validate each dependency
            for dep_obj in depends_list:
                dependency_target_name = dep_obj.plugin_identifier

                # Version constraint validation
                if dep_obj.version_constraint is not None:
                    self._validate_version_constraint(
                        dependent_plugin_name, dependency_target_name, dep_obj.version_constraint
                    )

            # Validate dependency injection signatures
            self._validate_injection_signature(
                dependent_plugin_name, dependent_plugin_class, depends_list
            )

    def _validate_version_constraint(
        self,
        dependent_plugin_name: str,
        dependency_target_name: str,
        version_constraint: str,
    ) -> None:
        """
        Validate a version constraint against the actual plugin version.

        Args:
            dependent_plugin_name: Name of the plugin declaring the dependency
            dependency_target_name: Name of the dependency plugin
            version_constraint: Version constraint string (e.g., ">=1.0,<2.0")

        Raises:
            DependencyVersionConflictError: If version constraint is not satisfied
            PluginRegistrationError: If plugin version is invalid
            ConfigurationError: If version constraint is malformed
        """
        # Get actual version of the dependency
        try:
            actual_version_str = self._version_resolver(dependency_target_name)
        except Exception as e:
            raise DependencyError(
                f"Cannot resolve version for plugin '{dependency_target_name}'"
            ) from e

        # Parse actual version
        try:
            actual_version = Version(actual_version_str)
        except InvalidVersion as e:
            raise PluginRegistrationError(
                f"Plugin '{dependency_target_name}' has invalid version '{actual_version_str}'"
            ) from e

        # Parse version constraint
        try:
            specifier_set = SpecifierSet(version_constraint)
        except InvalidSpecifier as e:
            raise ConfigurationError(
                f"Plugin '{dependent_plugin_name}' has invalid version constraint "
                f"'{version_constraint}' for dependency '{dependency_target_name}'"
            ) from e

        # Check if actual version satisfies constraint
        if actual_version not in specifier_set:
            raise DependencyVersionConflictError(
                f"Plugin '{dependent_plugin_name}' requires '{dependency_target_name}' "
                f"version '{version_constraint}', but version '{actual_version_str}' is available"
            )

    def _validate_injection_signature(
        self,
        dependent_plugin_name: str,
        dependent_plugin_class: type[PluginBase],
        depends_list: list[Depends],
    ) -> None:
        """
        Validate dependency injection signature for a plugin.

        This method checks that:
        1. All declared dependencies have corresponding __init__ parameters
        2. All required __init__ parameters are covered by dependencies

        Args:
            dependent_plugin_name: Name of the plugin being validated
            dependent_plugin_class: Class of the plugin being validated
            depends_list: List of declared dependencies

        Raises:
            DependencyError: If injection signature is invalid or incomplete
        """
        # Inspect __init__ method signature
        try:
            init_signature = inspect.signature(dependent_plugin_class.__init__)
        except Exception as e:
            raise DependencyError(
                f"Cannot inspect __init__ signature for plugin '{dependent_plugin_name}'"
            ) from e

        # Get parameter information (excluding 'self' and 'app')
        init_params = {}
        for param_name, param in init_signature.parameters.items():
            if param_name not in ('self', 'app'):
                init_params[param_name] = param

        # Create mapping of dependency names to their targets
        dependency_names = {dep.plugin_identifier for dep in depends_list}

        # Check that all declared dependencies have corresponding parameters
        for dep in depends_list:
            dependency_name = dep.plugin_identifier
            if dependency_name not in init_params:
                raise DependencyError(
                    f"Plugin '{dependent_plugin_name}' declares dependency '{dependency_name}' "
                    f"but __init__ method has no parameter '{dependency_name}'"
                )

        # Check that all required parameters are covered by dependencies
        for param_name, param in init_params.items():
            # Skip **kwargs parameters (like **injected_dependencies)
            if param.kind == inspect.Parameter.VAR_KEYWORD:
                continue

            if param.default is inspect.Parameter.empty:  # Required parameter
                if param_name not in dependency_names:
                    raise DependencyError(
                        f"Plugin '{dependent_plugin_name}' __init__ requires parameter '{param_name}' "
                        f"but no dependency is declared for it"
                    )


# Additional validation utilities (migrated from utils/validation.py)

def validate_plugin_name(name: str) -> None:
    """
    Validate a plugin name.

    Args:
        name: Plugin name to validate

    Raises:
        PluginRegistrationError: If name is invalid
    """
    if not name or not isinstance(name, str):
        raise PluginRegistrationError(f"Plugin name must be a non-empty string, got: {name!r}")

    if not re.match(r"^[a-zA-Z][a-zA-Z0-9_.-]*$", name):
        raise PluginRegistrationError(
            f"Plugin name '{name}' is invalid. Must start with a letter and contain only "
            f"letters, numbers, underscores, dots, and hyphens."
        )

    if len(name) > 100:
        raise PluginRegistrationError(f"Plugin name '{name}' is too long (max 100 characters)")


def validate_service_name(name: str) -> None:
    """
    Validate a service name.

    Args:
        name: Service name to validate

    Raises:
        ServiceDefinitionError: If name is invalid
    """
    if not name or not isinstance(name, str):
        raise ServiceDefinitionError(f"Service name must be a non-empty string, got: {name!r}")

    if not re.match(r"^[a-zA-Z][a-zA-Z0-9_.-]*$", name):
        raise ServiceDefinitionError(
            f"Service name '{name}' is invalid. Must start with a letter and contain only "
            f"letters, numbers, underscores, dots, and hyphens."
        )

    if len(name) > 100:
        raise ServiceDefinitionError(f"Service name '{name}' is too long (max 100 characters)")


def validate_event_pattern(pattern: str) -> None:
    """
    Validate an event pattern.

    Args:
        pattern: Event pattern to validate

    Raises:
        EventDefinitionError: If pattern is invalid
    """
    if not pattern or not isinstance(pattern, str):
        raise EventDefinitionError(f"Event pattern must be a non-empty string, got: {pattern!r}")

    # Allow wildcards and basic event naming
    if not re.match(r"^[a-zA-Z*][a-zA-Z0-9_.*-]*$", pattern):
        raise EventDefinitionError(
            f"Event pattern '{pattern}' is invalid. Must start with a letter or '*' and contain only "
            f"letters, numbers, underscores, dots, asterisks, and hyphens."
        )

    if len(pattern) > 200:
        raise EventDefinitionError(f"Event pattern '{pattern}' is too long (max 200 characters)")


def validate_version_string(version: str) -> None:
    """
    Validate a version string.

    Args:
        version: Version string to validate

    Raises:
        ValidationError: If version is invalid
    """
    if not version or not isinstance(version, str):
        raise ValidationError(f"Version must be a non-empty string, got: {version!r}")

    # Basic semantic versioning pattern
    if not re.match(r"^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$", version):
        raise ValidationError(
            f"Version '{version}' is invalid. Must follow semantic versioning (e.g., '1.0.0' or '1.0.0-alpha.1')"
        )


def validate_timeout_value(timeout: float | None) -> None:
    """
    Validate a timeout value.

    Args:
        timeout: Timeout value to validate

    Raises:
        ValidationError: If timeout is invalid
    """
    if timeout is None:
        return

    if not isinstance(timeout, (int, float)):
        raise ValidationError(f"Timeout must be a number or None, got: {type(timeout)}")

    if timeout <= 0:
        raise ValidationError(f"Timeout must be positive, got: {timeout}")

    if timeout > 3600:  # 1 hour max
        raise ValidationError(f"Timeout too large (max 3600 seconds), got: {timeout}")


def validate_method_signature(method: Callable[..., Any], config: ValidationConfig) -> None:
    """
    Validate a method signature against configuration requirements.

    This function uses a ValidationConfig object to determine validation rules,
    making it more flexible and maintainable than the previous parameter-heavy approach.

    Args:
        method: Method to validate
        config: Validation configuration specifying requirements

    Raises:
        ValidationError: If signature is invalid according to the configuration
    """
    method_name = getattr(method, '__name__', str(method))

    # Validate async requirement
    if config.require_async and not inspect.iscoroutinefunction(method):
        raise ValidationError(f"{config.context_name.capitalize()} '{method_name}' must be async")

    # Get method signature
    try:
        sig = inspect.signature(method)
    except (ValueError, TypeError) as e:
        raise ValidationError(f"Cannot inspect signature of {config.context_name} '{method_name}': {e}") from e

    params = list(sig.parameters.values())

    # Validate parameter count using helper
    _validate_parameter_count(method_name, params, config)

    # Validate self parameter
    if config.require_self and (not params or params[0].name != 'self'):
        raise ValidationError(
            f"{config.context_name.capitalize()} '{method_name}' first parameter must be 'self'"
        )

    # Validate parameter types using helper
    _validate_parameter_types(method_name, params, config)

    # Validate return annotation using helper
    _validate_return_annotation(method_name, sig, config)


def _validate_parameter_count(method_name: str, params: list[inspect.Parameter], config: ValidationConfig) -> None:
    """Helper function to validate parameter count."""
    if len(params) < config.min_params:
        raise ValidationError(
            f"{config.context_name.capitalize()} '{method_name}' must have at least "
            f"{config.min_params} parameters, got {len(params)}"
        )

    if config.max_params is not None and len(params) > config.max_params:
        raise ValidationError(
            f"{config.context_name.capitalize()} '{method_name}' must have at most "
            f"{config.max_params} parameters, got {len(params)}"
        )


def _validate_parameter_types(method_name: str, params: list[inspect.Parameter], config: ValidationConfig) -> None:
    """Helper function to validate parameter types and annotations."""
    for param in params:
        # Check for unsupported parameter kinds
        if param.kind == inspect.Parameter.VAR_POSITIONAL and not config.allow_varargs:
            raise ValidationError(
                f"{config.context_name.capitalize()} '{method_name}' cannot use *args parameters"
            )

        if param.kind == inspect.Parameter.VAR_KEYWORD and not config.allow_kwargs:
            raise ValidationError(
                f"{config.context_name.capitalize()} '{method_name}' cannot use **kwargs parameters"
            )

        # Check parameter annotations if required
        if config.require_param_annotations and param.name != 'self':
            if param.annotation == inspect.Parameter.empty:
                raise ValidationError(
                    f"{config.context_name.capitalize()} '{method_name}' parameter '{param.name}' "
                    f"must have a type annotation"
                )


def _validate_return_annotation(method_name: str, sig: inspect.Signature, config: ValidationConfig) -> None:
    """Helper function to validate return annotation."""
    if not config.require_return_annotation:
        return

    if sig.return_annotation == inspect.Signature.empty:
        raise ValidationError(
            f"{config.context_name.capitalize()} '{method_name}' must have a return type annotation"
        )

    # Check allowed return types if specified
    if config.allowed_return_types is not None:
        if sig.return_annotation not in config.allowed_return_types:
            allowed_names = [t.__name__ for t in config.allowed_return_types]
            raise ValidationError(
                f"{config.context_name.capitalize()} '{method_name}' return type must be one of "
                f"{allowed_names}, got {sig.return_annotation}"
            )


# Convenience functions for common validation scenarios

def validate_service_method(method: Callable[..., Any]) -> None:
    """
    Validate a service method signature.

    Service methods must be async, have 'self' as first parameter,
    and have proper type annotations.

    Args:
        method: Service method to validate

    Raises:
        ValidationError: If method signature is invalid for a service method
    """
    validate_method_signature(method, ValidationProfiles.SERVICE_METHOD)


def validate_event_handler(method: Callable[..., Any]) -> None:
    """
    Validate an event handler method signature.

    Event handlers must be async, have 'self' as first parameter,
    and accept event_data and optionally matched_event_type parameters.

    Args:
        method: Event handler method to validate

    Raises:
        ValidationError: If method signature is invalid for an event handler
    """
    validate_method_signature(method, ValidationProfiles.EVENT_HANDLER)


def validate_setup_method(method: Callable[..., Any]) -> None:
    """
    Validate a plugin setup method signature.

    Setup methods must be async, have 'self' as first parameter,
    and optionally accept a configuration parameter.

    Args:
        method: Setup method to validate

    Raises:
        ValidationError: If method signature is invalid for a setup method
    """
    validate_method_signature(method, ValidationProfiles.SETUP_METHOD)


def validate_teardown_method(method: Callable[..., Any]) -> None:
    """
    Validate a plugin teardown method signature.

    Teardown methods must be async and only have 'self' as parameter.

    Args:
        method: Teardown method to validate

    Raises:
        ValidationError: If method signature is invalid for a teardown method
    """
    validate_method_signature(method, ValidationProfiles.TEARDOWN_METHOD)


def validate_plugin_constructor(method: Callable[..., Any]) -> None:
    """
    Validate a plugin constructor (__init__) signature.

    Plugin constructors must have 'self' as first parameter and can
    accept **kwargs for dependency injection.

    Args:
        method: Constructor method to validate

    Raises:
        ValidationError: If method signature is invalid for a plugin constructor
    """
    validate_method_signature(method, ValidationProfiles.PLUGIN_CONSTRUCTOR)


def is_valid_identifier(name: str) -> bool:
    """
    Check if a string is a valid Python identifier.

    Args:
        name: String to check

    Returns:
        True if valid identifier, False otherwise
    """
    return isinstance(name, str) and name.isidentifier() and not name.startswith("_")


def sanitize_name(name: str, max_length: int = 50) -> str:
    """
    Sanitize a name to make it a valid identifier.

    Args:
        name: Name to sanitize
        max_length: Maximum length for the result

    Returns:
        Sanitized name

    Raises:
        ValidationError: If name cannot be sanitized
    """
    if not isinstance(name, str):
        raise ValidationError(f"Name must be a string, got: {type(name)}")

    # Remove invalid characters and replace with underscores
    sanitized = re.sub(r"[^a-zA-Z0-9_]", "_", name)

    # Ensure it starts with a letter
    if not sanitized or not sanitized[0].isalpha():
        sanitized = "item_" + sanitized

    # Truncate if too long
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]

    # Ensure it's still valid
    if not is_valid_identifier(sanitized):
        raise ValidationError(f"Cannot sanitize name '{name}' to valid identifier")

    return sanitized
