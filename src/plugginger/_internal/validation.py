# src/plugginger/_internal/validation.py

"""
Dependency validation for the Plugginger framework.

This module provides the DependencyValidator class for validating plugin
dependencies, versions, and dependency injection signatures.
"""

from __future__ import annotations

import inspect
import re
from collections.abc import Callable
from typing import Any

from packaging.specifiers import InvalidSpecifier, SpecifierSet
from packaging.version import InvalidVersion, Version

from plugginger._internal.graph import DependencyGraph
from plugginger.api.depends import Depends
from plugginger.api.plugin import PluginBase
from plugginger.core.exceptions import (
    ConfigurationError,
    DependencyError,
    DependencyVersionConflictError,
    EventDefinitionError,
    MissingDependencyError,
    PluginRegistrationError,
    ServiceDefinitionError,
    ValidationError,
)

# Type aliases for resolver functions
VersionResolverFunc = Callable[[str], str]
ClassResolverFunc = Callable[[str], type[PluginBase]]


class DependencyValidator:
    """
    Validator for plugin dependencies, versions, and injection signatures.

    This class provides comprehensive validation of plugin dependency graphs,
    including structure validation, version constraint checking, and dependency
    injection signature validation.

    Attributes:
        _version_resolver: Function to resolve plugin versions by name
        _class_resolver: Function to resolve plugin classes by name
    """

    def __init__(
        self,
        version_resolver_func: VersionResolverFunc,
        class_resolver_func: ClassResolverFunc,
    ) -> None:
        """
        Initialize the dependency validator.

        Args:
            version_resolver_func: Function that takes a plugin name and returns its version
            class_resolver_func: Function that takes a plugin name and returns its class
        """
        self._version_resolver = version_resolver_func
        self._class_resolver = class_resolver_func

    def validate_graph_structure(
        self,
        graph: DependencyGraph[str],
        all_registered_plugin_names: set[str],
    ) -> None:
        """
        Validate that all dependencies in the graph reference existing plugins.

        This method checks that every prerequisite node referenced in dependency
        edges actually exists in the set of registered plugins.

        Args:
            graph: Dependency graph to validate
            all_registered_plugin_names: Set of all registered plugin names

        Raises:
            MissingDependencyError: If any dependency references a non-existent plugin
        """
        for node in graph.get_all_nodes():
            prerequisites = graph.get_prerequisites(node)
            for prerequisite in prerequisites:
                if prerequisite not in all_registered_plugin_names:
                    raise MissingDependencyError(
                        f"Plugin '{node}' depends on '{prerequisite}' which is not registered"
                    )

    def validate_dependency_versions_and_signatures(
        self,
        plugin_dependency_declarations: dict[str, list[Depends]],
    ) -> None:
        """
        Validate dependency versions and injection signatures.

        This method performs comprehensive validation of plugin dependencies:
        1. Version constraint validation against actual plugin versions
        2. Dependency injection signature validation
        3. Completeness check for required constructor parameters

        Args:
            plugin_dependency_declarations: Mapping of plugin names to their dependency lists

        Raises:
            DependencyVersionConflictError: If version constraints are not satisfied
            DependencyError: If injection signatures are invalid or incomplete
            PluginRegistrationError: If plugin versions are invalid
            ConfigurationError: If version constraints are malformed
        """
        for dependent_plugin_name, depends_list in plugin_dependency_declarations.items():
            # Get the dependent plugin class for signature validation
            try:
                dependent_plugin_class = self._class_resolver(dependent_plugin_name)
            except Exception as e:
                raise DependencyError(
                    f"Cannot resolve class for plugin '{dependent_plugin_name}'"
                ) from e

            # Validate each dependency
            for dep_obj in depends_list:
                dependency_target_name = dep_obj.plugin_identifier

                # Version constraint validation
                if dep_obj.version_constraint is not None:
                    self._validate_version_constraint(
                        dependent_plugin_name, dependency_target_name, dep_obj.version_constraint
                    )

            # Validate dependency injection signatures
            self._validate_injection_signature(
                dependent_plugin_name, dependent_plugin_class, depends_list
            )

    def _validate_version_constraint(
        self,
        dependent_plugin_name: str,
        dependency_target_name: str,
        version_constraint: str,
    ) -> None:
        """
        Validate a version constraint against the actual plugin version.

        Args:
            dependent_plugin_name: Name of the plugin declaring the dependency
            dependency_target_name: Name of the dependency plugin
            version_constraint: Version constraint string (e.g., ">=1.0,<2.0")

        Raises:
            DependencyVersionConflictError: If version constraint is not satisfied
            PluginRegistrationError: If plugin version is invalid
            ConfigurationError: If version constraint is malformed
        """
        # Get actual version of the dependency
        try:
            actual_version_str = self._version_resolver(dependency_target_name)
        except Exception as e:
            raise DependencyError(
                f"Cannot resolve version for plugin '{dependency_target_name}'"
            ) from e

        # Parse actual version
        try:
            actual_version = Version(actual_version_str)
        except InvalidVersion as e:
            raise PluginRegistrationError(
                f"Plugin '{dependency_target_name}' has invalid version '{actual_version_str}'"
            ) from e

        # Parse version constraint
        try:
            specifier_set = SpecifierSet(version_constraint)
        except InvalidSpecifier as e:
            raise ConfigurationError(
                f"Plugin '{dependent_plugin_name}' has invalid version constraint "
                f"'{version_constraint}' for dependency '{dependency_target_name}'"
            ) from e

        # Check if actual version satisfies constraint
        if actual_version not in specifier_set:
            raise DependencyVersionConflictError(
                f"Plugin '{dependent_plugin_name}' requires '{dependency_target_name}' "
                f"version '{version_constraint}', but version '{actual_version_str}' is available"
            )

    def _validate_injection_signature(
        self,
        dependent_plugin_name: str,
        dependent_plugin_class: type[PluginBase],
        depends_list: list[Depends],
    ) -> None:
        """
        Validate dependency injection signature for a plugin.

        This method checks that:
        1. All declared dependencies have corresponding __init__ parameters
        2. All required __init__ parameters are covered by dependencies

        Args:
            dependent_plugin_name: Name of the plugin being validated
            dependent_plugin_class: Class of the plugin being validated
            depends_list: List of declared dependencies

        Raises:
            DependencyError: If injection signature is invalid or incomplete
        """
        # Inspect __init__ method signature
        try:
            init_signature = inspect.signature(dependent_plugin_class.__init__)
        except Exception as e:
            raise DependencyError(
                f"Cannot inspect __init__ signature for plugin '{dependent_plugin_name}'"
            ) from e

        # Get parameter information (excluding 'self' and 'app')
        init_params = {}
        for param_name, param in init_signature.parameters.items():
            if param_name not in ('self', 'app'):
                init_params[param_name] = param

        # Create mapping of dependency names to their targets
        dependency_names = {dep.plugin_identifier for dep in depends_list}

        # Check that all declared dependencies have corresponding parameters
        for dep in depends_list:
            dependency_name = dep.plugin_identifier
            if dependency_name not in init_params:
                raise DependencyError(
                    f"Plugin '{dependent_plugin_name}' declares dependency '{dependency_name}' "
                    f"but __init__ method has no parameter '{dependency_name}'"
                )

        # Check that all required parameters are covered by dependencies
        for param_name, param in init_params.items():
            # Skip **kwargs parameters (like **injected_dependencies)
            if param.kind == inspect.Parameter.VAR_KEYWORD:
                continue

            if param.default is inspect.Parameter.empty:  # Required parameter
                if param_name not in dependency_names:
                    raise DependencyError(
                        f"Plugin '{dependent_plugin_name}' __init__ requires parameter '{param_name}' "
                        f"but no dependency is declared for it"
                    )


# Additional validation utilities (migrated from utils/validation.py)

def validate_plugin_name(name: str) -> None:
    """
    Validate a plugin name.

    Args:
        name: Plugin name to validate

    Raises:
        PluginRegistrationError: If name is invalid
    """
    if not name or not isinstance(name, str):
        raise PluginRegistrationError(f"Plugin name must be a non-empty string, got: {name!r}")

    if not re.match(r"^[a-zA-Z][a-zA-Z0-9_.-]*$", name):
        raise PluginRegistrationError(
            f"Plugin name '{name}' is invalid. Must start with a letter and contain only "
            f"letters, numbers, underscores, dots, and hyphens."
        )

    if len(name) > 100:
        raise PluginRegistrationError(f"Plugin name '{name}' is too long (max 100 characters)")


def validate_service_name(name: str) -> None:
    """
    Validate a service name.

    Args:
        name: Service name to validate

    Raises:
        ServiceDefinitionError: If name is invalid
    """
    if not name or not isinstance(name, str):
        raise ServiceDefinitionError(f"Service name must be a non-empty string, got: {name!r}")

    if not re.match(r"^[a-zA-Z][a-zA-Z0-9_.-]*$", name):
        raise ServiceDefinitionError(
            f"Service name '{name}' is invalid. Must start with a letter and contain only "
            f"letters, numbers, underscores, dots, and hyphens."
        )

    if len(name) > 100:
        raise ServiceDefinitionError(f"Service name '{name}' is too long (max 100 characters)")


def validate_event_pattern(pattern: str) -> None:
    """
    Validate an event pattern.

    Args:
        pattern: Event pattern to validate

    Raises:
        EventDefinitionError: If pattern is invalid
    """
    if not pattern or not isinstance(pattern, str):
        raise EventDefinitionError(f"Event pattern must be a non-empty string, got: {pattern!r}")

    # Allow wildcards and basic event naming
    if not re.match(r"^[a-zA-Z*][a-zA-Z0-9_.*-]*$", pattern):
        raise EventDefinitionError(
            f"Event pattern '{pattern}' is invalid. Must start with a letter or '*' and contain only "
            f"letters, numbers, underscores, dots, asterisks, and hyphens."
        )

    if len(pattern) > 200:
        raise EventDefinitionError(f"Event pattern '{pattern}' is too long (max 200 characters)")


def validate_version_string(version: str) -> None:
    """
    Validate a version string.

    Args:
        version: Version string to validate

    Raises:
        ValidationError: If version is invalid
    """
    if not version or not isinstance(version, str):
        raise ValidationError(f"Version must be a non-empty string, got: {version!r}")

    # Basic semantic versioning pattern
    if not re.match(r"^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$", version):
        raise ValidationError(
            f"Version '{version}' is invalid. Must follow semantic versioning (e.g., '1.0.0' or '1.0.0-alpha.1')"
        )


def validate_timeout_value(timeout: float | None) -> None:
    """
    Validate a timeout value.

    Args:
        timeout: Timeout value to validate

    Raises:
        ValidationError: If timeout is invalid
    """
    if timeout is None:
        return

    if not isinstance(timeout, int | float):
        raise ValidationError(f"Timeout must be a number or None, got: {type(timeout)}")

    if timeout <= 0:
        raise ValidationError(f"Timeout must be positive, got: {timeout}")

    if timeout > 3600:  # 1 hour max
        raise ValidationError(f"Timeout too large (max 3600 seconds), got: {timeout}")


def validate_method_signature(
    method: Callable[..., Any],
    *,
    require_async: bool = True,
    require_self: bool = True,
    min_params: int = 1,
    max_params: int | None = None,
    allow_varargs: bool = False,
    allow_kwargs: bool = False
) -> None:
    """
    Validate a method signature against requirements.

    Args:
        method: Method to validate
        require_async: Whether the method must be async
        require_self: Whether the first parameter must be 'self'
        min_params: Minimum number of parameters (including self)
        max_params: Maximum number of parameters (None for unlimited)
        allow_varargs: Whether *args is allowed
        allow_kwargs: Whether **kwargs is allowed

    Raises:
        ValidationError: If signature is invalid
    """
    if require_async and not inspect.iscoroutinefunction(method):
        raise ValidationError(f"Method '{method.__name__}' must be async")

    sig = inspect.signature(method)
    params = list(sig.parameters.values())

    if len(params) < min_params:
        raise ValidationError(
            f"Method '{method.__name__}' must have at least {min_params} parameters, got {len(params)}"
        )

    if max_params is not None and len(params) > max_params:
        raise ValidationError(
            f"Method '{method.__name__}' must have at most {max_params} parameters, got {len(params)}"
        )

    if require_self and (not params or params[0].name != 'self'):
        raise ValidationError(f"Method '{method.__name__}' first parameter must be 'self'")

    # Check for unsupported parameter types
    for param in params:
        if param.kind == inspect.Parameter.VAR_POSITIONAL and not allow_varargs:
            raise ValidationError(f"Method '{method.__name__}' cannot use *args parameters")

        if param.kind == inspect.Parameter.VAR_KEYWORD and not allow_kwargs:
            raise ValidationError(f"Method '{method.__name__}' cannot use **kwargs parameters")


def is_valid_identifier(name: str) -> bool:
    """
    Check if a string is a valid Python identifier.

    Args:
        name: String to check

    Returns:
        True if valid identifier, False otherwise
    """
    return isinstance(name, str) and name.isidentifier() and not name.startswith("_")


def sanitize_name(name: str, max_length: int = 50) -> str:
    """
    Sanitize a name to make it a valid identifier.

    Args:
        name: Name to sanitize
        max_length: Maximum length for the result

    Returns:
        Sanitized name

    Raises:
        ValidationError: If name cannot be sanitized
    """
    if not isinstance(name, str):
        raise ValidationError(f"Name must be a string, got: {type(name)}")

    # Remove invalid characters and replace with underscores
    sanitized = re.sub(r"[^a-zA-Z0-9_]", "_", name)

    # Ensure it starts with a letter
    if not sanitized or not sanitized[0].isalpha():
        sanitized = "item_" + sanitized

    # Truncate if too long
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]

    # Ensure it's still valid
    if not is_valid_identifier(sanitized):
        raise ValidationError(f"Cannot sanitize name '{name}' to valid identifier")

    return sanitized
