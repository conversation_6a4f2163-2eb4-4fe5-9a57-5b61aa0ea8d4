# src/plugginger/testing/collectors.py

"""
Event and service call collectors for testing.

This module provides utilities for collecting and analyzing events and service
calls during testing, enabling comprehensive verification of plugin behavior.
"""

import asyncio
from typing import Any

from plugginger.core.types import ServiceName


class EventCollector:
    """
    Collects events for testing and verification.

    This collector can be used as an event listener to capture events
    emitted during testing for later analysis.
    """

    def __init__(self, pattern_filter: str | None = None) -> None:
        """
        Initialize the event collector.

        Args:
            pattern_filter: Optional pattern to filter events (None = collect all)
        """
        self._pattern_filter = pattern_filter
        self._collected_events: list[tuple[str, dict[str, Any], float]] = []
        self._start_time = asyncio.get_event_loop().time()

    async def collect_event(self, event_data: dict[str, Any], event_type: str) -> None:
        """
        Collect an event (used as event listener).

        Args:
            event_data: Event data payload
            event_type: Type of the event
        """
        if self._should_collect(event_type):
            timestamp = asyncio.get_event_loop().time() - self._start_time
            self._collected_events.append((event_type, event_data.copy(), timestamp))

    def _should_collect(self, event_type: str) -> bool:
        """Check if an event should be collected based on the filter."""
        if self._pattern_filter is None:
            return True

        # Simple pattern matching
        if self._pattern_filter == event_type:
            return True

        if "*" in self._pattern_filter:
            pattern_parts = self._pattern_filter.split("*")
            if len(pattern_parts) == 2:
                prefix, suffix = pattern_parts
                return event_type.startswith(prefix) and event_type.endswith(suffix)

        return False

    def get_events(self) -> list[tuple[str, dict[str, Any], float]]:
        """
        Get all collected events.

        Returns:
            List of (event_type, event_data, timestamp) tuples
        """
        return self._collected_events.copy()

    def get_events_by_type(self, event_type: str) -> list[tuple[dict[str, Any], float]]:
        """
        Get events of a specific type.

        Args:
            event_type: Type of events to retrieve

        Returns:
            List of (event_data, timestamp) tuples
        """
        return [
            (data, timestamp)
            for etype, data, timestamp in self._collected_events
            if etype == event_type
        ]

    def count_events(self, event_type: str | None = None) -> int:
        """
        Count collected events.

        Args:
            event_type: Optional event type to count (None = count all)

        Returns:
            Number of events
        """
        if event_type is None:
            return len(self._collected_events)

        return sum(1 for etype, _, _ in self._collected_events if etype == event_type)

    def has_event(self, event_type: str, event_data: dict[str, Any] | None = None) -> bool:
        """
        Check if a specific event was collected.

        Args:
            event_type: Type of event to check
            event_data: Optional event data to match

        Returns:
            True if event was collected, False otherwise
        """
        for etype, data, _ in self._collected_events:
            if etype == event_type:
                if event_data is None or data == event_data:
                    return True
        return False

    def clear(self) -> None:
        """Clear all collected events."""
        self._collected_events.clear()
        self._start_time = asyncio.get_event_loop().time()

    def get_latest_event(self, event_type: str | None = None) -> tuple[str, dict[str, Any], float] | None:
        """
        Get the latest collected event.

        Args:
            event_type: Optional event type to filter

        Returns:
            Latest event tuple or None if no events
        """
        filtered_events = [
            event for event in self._collected_events
            if event_type is None or event[0] == event_type
        ]

        return filtered_events[-1] if filtered_events else None

    def wait_for_event(
        self, event_type: str, timeout: float = 1.0, event_data: dict[str, Any] | None = None
    ) -> bool:
        """
        Wait for a specific event to be collected.

        Args:
            event_type: Type of event to wait for
            timeout: Maximum time to wait in seconds
            event_data: Optional event data to match

        Returns:
            True if event was collected within timeout, False otherwise
        """
        # This is a simplified implementation for testing
        # In a real implementation, this would use asyncio.Event or similar
        return self.has_event(event_type, event_data)

    def __len__(self) -> int:
        """Get the number of collected events."""
        return len(self._collected_events)

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"EventCollector(events={len(self._collected_events)}, filter={self._pattern_filter!r})"


class ServiceCallCollector:
    """
    Collects service calls for testing and verification.

    This collector can be used to wrap service methods and capture
    calls made during testing for later analysis.
    """

    def __init__(self) -> None:
        """Initialize the service call collector."""
        self._collected_calls: list[tuple[ServiceName, tuple[Any, ...], dict[str, Any], float, Any]] = []
        self._start_time = asyncio.get_event_loop().time()

    async def collect_call(
        self,
        service_name: ServiceName,
        args: tuple[Any, ...],
        kwargs: dict[str, Any],
        result: Any
    ) -> None:
        """
        Collect a service call.

        Args:
            service_name: Name of the service that was called
            args: Positional arguments passed to the service
            kwargs: Keyword arguments passed to the service
            result: Result returned by the service
        """
        timestamp = asyncio.get_event_loop().time() - self._start_time
        self._collected_calls.append((service_name, args, kwargs, timestamp, result))

    def get_calls(self) -> list[tuple[ServiceName, tuple[Any, ...], dict[str, Any], float, Any]]:
        """
        Get all collected service calls.

        Returns:
            List of (service_name, args, kwargs, timestamp, result) tuples
        """
        return self._collected_calls.copy()

    def get_calls_for_service(self, service_name: ServiceName) -> list[tuple[tuple[Any, ...], dict[str, Any], float, Any]]:
        """
        Get calls for a specific service.

        Args:
            service_name: Name of the service

        Returns:
            List of (args, kwargs, timestamp, result) tuples
        """
        return [
            (args, kwargs, timestamp, result)
            for sname, args, kwargs, timestamp, result in self._collected_calls
            if sname == service_name
        ]

    def count_calls(self, service_name: ServiceName | None = None) -> int:
        """
        Count collected service calls.

        Args:
            service_name: Optional service name to count (None = count all)

        Returns:
            Number of calls
        """
        if service_name is None:
            return len(self._collected_calls)

        return sum(1 for sname, _, _, _, _ in self._collected_calls if sname == service_name)

    def was_called(self, service_name: ServiceName, args: tuple[Any, ...] | None = None) -> bool:
        """
        Check if a service was called.

        Args:
            service_name: Name of the service
            args: Optional arguments to match

        Returns:
            True if service was called, False otherwise
        """
        for sname, call_args, _, _, _ in self._collected_calls:
            if sname == service_name:
                if args is None or call_args == args:
                    return True
        return False

    def get_latest_call(self, service_name: ServiceName | None = None) -> tuple[ServiceName, tuple[Any, ...], dict[str, Any], float, Any] | None:
        """
        Get the latest service call.

        Args:
            service_name: Optional service name to filter

        Returns:
            Latest call tuple or None if no calls
        """
        filtered_calls = [
            call for call in self._collected_calls
            if service_name is None or call[0] == service_name
        ]

        return filtered_calls[-1] if filtered_calls else None

    def clear(self) -> None:
        """Clear all collected service calls."""
        self._collected_calls.clear()
        self._start_time = asyncio.get_event_loop().time()

    def __len__(self) -> int:
        """Get the number of collected calls."""
        return len(self._collected_calls)

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"ServiceCallCollector(calls={len(self._collected_calls)})"


def create_event_collector(pattern_filter: str | None = None) -> EventCollector:
    """
    Create an event collector.

    Args:
        pattern_filter: Optional pattern to filter events

    Returns:
        Event collector instance
    """
    return EventCollector(pattern_filter)


def create_service_call_collector() -> ServiceCallCollector:
    """
    Create a service call collector.

    Returns:
        Service call collector instance
    """
    return ServiceCallCollector()


class CollectorManager:
    """
    Manages multiple collectors for comprehensive testing.

    This manager coordinates event and service call collectors to provide
    a unified interface for test verification.
    """

    def __init__(self) -> None:
        """Initialize the collector manager."""
        self._evt_collectors: dict[str, EventCollector] = {}
        self._svc_collectors: dict[str, ServiceCallCollector] = {}

    def add_event_collector(self, name: str, pattern_filter: str | None = None) -> EventCollector:
        """
        Add an event collector.

        Args:
            name: Name for the collector
            pattern_filter: Optional pattern to filter events

        Returns:
            The created event collector
        """
        collector = EventCollector(pattern_filter)
        self._evt_collectors[name] = collector
        return collector

    def add_service_collector(self, name: str) -> ServiceCallCollector:
        """
        Add a service call collector.

        Args:
            name: Name for the collector

        Returns:
            The created service call collector
        """
        collector = ServiceCallCollector()
        self._svc_collectors[name] = collector  # type: ignore[assignment]
        return collector

    def get_event_collector(self, name: str) -> EventCollector | None:
        """Get an event collector by name."""
        return self._evt_collectors.get(name)

    def get_service_collector(self, name: str) -> ServiceCallCollector | None:
        """Get a service call collector by name."""
        return self._svc_collectors.get(name)

    def clear_all(self) -> None:
        """Clear all collectors."""
        for collector in self._evt_collectors.values():
            collector.clear()
        for collector in self._svc_collectors.values():
            collector.clear()

    def get_summary(self) -> dict[str, Any]:
        """
        Get a summary of all collectors.

        Returns:
            Summary dictionary with collector statistics
        """
        return {
            "event_collectors": {
                name: {"events": len(collector), "filter": collector._pattern_filter}
                for name, collector in self._evt_collectors.items()
            },
            "service_collectors": {
                name: {"calls": len(collector)}
                for name, collector in self._svc_collectors.items()
            }
        }
