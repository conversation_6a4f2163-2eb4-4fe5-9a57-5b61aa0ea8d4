# src/plugginger/testing/helpers.py

"""
Test helper utilities for the Plugginger framework.

This module provides utilities for setting up test environments, running
plugins in isolation, and creating test fixtures.
"""

from __future__ import annotations

import asyncio
from typing import Any, TypeVar
from unittest.mock import Mock

from plugginger.api.plugin import PluginBase
from plugginger.testing.collectors import <PERSON><PERSON>oll<PERSON>, ServiceCallCollector
from plugginger.testing.mock_app import MockPluggingerAppInstance

T = TypeVar("T", bound=PluginBase)


class PluginTestRunner:
    """
    Test runner for isolated plugin testing.

    This runner provides a controlled environment for testing plugins
    without the full application framework.
    """

    def __init__(self, plugin_class: type[T]) -> None:
        """
        Initialize the plugin test runner.

        Args:
            plugin_class: Plugin class to test
        """
        self._plugin_class = plugin_class
        self._plugin_instance: T | None = None
        self._mock_app = MockPluggingerAppInstance("TestApp")
        self._event_collector = EventCollector()
        self._service_collector = ServiceCallCollector()
        self._injected_dependencies: dict[str, Any] = {}

    def inject_dependency(self, name: str, value: Any) -> None:
        """
        Inject a dependency for the plugin.

        Args:
            name: Name of the dependency
            value: Value to inject
        """
        self._injected_dependencies[name] = value

    def inject_mock_service(self, name: str, return_value: Any = None) -> Mock:
        """
        Inject a mock service dependency.

        Args:
            name: Name of the service dependency
            return_value: Value to return from the mock service

        Returns:
            The created mock object
        """
        mock_service = Mock(return_value=return_value)
        self._injected_dependencies[name] = mock_service
        return mock_service

    async def setup_plugin(self) -> T:
        """
        Set up the plugin instance for testing.

        Returns:
            The initialized plugin instance
        """
        if self._plugin_instance is not None:
            raise RuntimeError("Plugin already set up")

        # Create plugin instance with app reference and injected dependencies
        self._plugin_instance = self._plugin_class(self._mock_app, **self._injected_dependencies)
        self._plugin_instance._plugginger_instance_id = "test_plugin_001"

        # Set up the plugin with empty config
        from pydantic import BaseModel

        class EmptyConfig(BaseModel):
            pass

        await self._plugin_instance.setup(EmptyConfig())

        from typing import cast
        return cast(T, self._plugin_instance)

    async def teardown_plugin(self) -> None:
        """Tear down the plugin instance."""
        if self._plugin_instance is not None:
            await self._plugin_instance.teardown()
            self._plugin_instance = None

    def get_plugin(self) -> T:
        """
        Get the plugin instance.

        Returns:
            The plugin instance

        Raises:
            RuntimeError: If plugin is not set up
        """
        if self._plugin_instance is None:
            raise RuntimeError("Plugin not set up. Call setup_plugin() first.")

        from typing import cast
        return cast(T, self._plugin_instance) # type: ignore[type-var]

    async def call_service(self, service_name: str, *args: Any, **kwargs: Any) -> Any:
        """
        Call a service method on the plugin.

        Args:
            service_name: Name of the service to call
            *args: Positional arguments
            **kwargs: Keyword arguments

        Returns:
            Result from the service call
        """
        from plugginger.api.service import extract_service_methods

        plugin: PluginBase = self.get_plugin()
        services = extract_service_methods(plugin)

        if service_name not in services:
            raise ValueError(f"Service '{service_name}' not found in plugin")

        service_method = services[service_name]
        result = await service_method(*args, **kwargs)

        # Collect the call
        await self._service_collector.collect_call(service_name, args, kwargs, result)

        return result

    async def emit_event(self, event_type: str, event_data: dict[str, Any]) -> None:
        """
        Emit an event to the plugin's event listeners.

        Args:
            event_type: Type of event to emit
            event_data: Event data payload
        """
        from plugginger.api.events import get_listener_patterns

        plugin: PluginBase = self.get_plugin()
        patterns = get_listener_patterns(plugin)

        # Collect the event
        await self._event_collector.collect_event(event_data, event_type)

        # Call matching listeners
        for pattern, handler, _metadata in patterns:
            if self._pattern_matches(pattern, event_type):
                try:
                    await handler(event_data, event_type)
                except Exception:
                    # Let exceptions propagate in tests
                    raise

    def _pattern_matches(self, pattern: str, event_type: str) -> bool:
        """Check if an event pattern matches an event type."""
        if pattern == event_type:
            return True

        # Simple wildcard matching
        if "*" in pattern:
            pattern_parts = pattern.split("*")
            if len(pattern_parts) == 2:
                prefix, suffix = pattern_parts
                return event_type.startswith(prefix) and event_type.endswith(suffix)

        return False

    @property
    def event_collector(self) -> EventCollector:
        """Get the event collector."""
        return self._event_collector

    @property
    def service_collector(self) -> ServiceCallCollector:
        """Get the service call collector."""
        return self._service_collector

    @property
    def mock_app(self) -> MockPluggingerAppInstance:
        """Get the mock app instance."""
        return self._mock_app

    async def __aenter__(self) -> PluginTestRunner:
        """Async context manager entry."""
        await self.setup_plugin()
        return self

    async def __aexit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Async context manager exit."""
        await self.teardown_plugin()


def create_plugin_test_runner(plugin_class: type[T]) -> PluginTestRunner:
    """
    Create a plugin test runner.

    Args:
        plugin_class: Plugin class to test

    Returns:
        Plugin test runner instance
    """
    return PluginTestRunner(plugin_class)


async def run_plugin_standalone(
    plugin_class: type[T], dependencies: dict[str, Any] | None = None
) -> T:
    """
    Run a plugin in standalone mode for testing.

    Args:
        plugin_class: Plugin class to run
        dependencies: Optional dependencies to inject

    Returns:
        The initialized plugin instance
    """
    runner = PluginTestRunner(plugin_class)

    if dependencies:
        for name, value in dependencies.items():
            runner.inject_dependency(name, value)

    return await runner.setup_plugin()


class TestFixture:
    """
    Test fixture for setting up common test scenarios.

    This fixture provides pre-configured mock objects and utilities
    for common testing patterns.
    """

    def __init__(self) -> None:
        """Initialize the test fixture."""
        self.mock_app = MockPluggingerAppInstance("TestApp")
        self.event_collector = EventCollector()
        self.service_collector = ServiceCallCollector()
        self._cleanup_tasks: list[Any] = []

    def create_mock_plugin(self, plugin_name: str = "MockPlugin") -> Mock:
        """
        Create a mock plugin instance.

        Args:
            plugin_name: Name for the mock plugin

        Returns:
            Mock plugin instance
        """
        mock_plugin = Mock()
        mock_plugin.plugin_instance_id = f"{plugin_name}_001"
        mock_plugin.is_setup = True
        mock_plugin.is_torn_down = False

        # Add async methods
        mock_plugin.setup = Mock(return_value=asyncio.Future())
        mock_plugin.setup.return_value.set_result(None)

        mock_plugin.teardown = Mock(return_value=asyncio.Future())
        mock_plugin.teardown.return_value.set_result(None)

        return mock_plugin

    def create_mock_service(self, service_name: str, return_value: Any = None) -> Mock:
        """
        Create a mock service.

        Args:
            service_name: Name of the service
            return_value: Value to return from the service

        Returns:
            Mock service instance
        """
        mock_service = Mock()

        if asyncio.iscoroutinefunction(return_value):
            mock_service.return_value = return_value
        else:
            future: asyncio.Future[Any] = asyncio.Future()
            future.set_result(return_value)
            mock_service.return_value = future

        # Register with mock app
        self.mock_app.service_dispatcher.add_service(service_name, mock_service)

        return mock_service

    async def wait_for_event(
        self, event_type: str, timeout: float = 1.0
    ) -> tuple[str, dict[str, Any], float] | None:
        """
        Wait for an event to be collected.

        Args:
            event_type: Type of event to wait for
            timeout: Maximum time to wait

        Returns:
            Event tuple if found, None if timeout
        """
        # Simple implementation for testing
        # In a real implementation, this would use asyncio.wait_for
        await asyncio.sleep(0.01)  # Give events time to be processed
        return self.event_collector.get_latest_event(event_type)

    def add_cleanup_task(self, task: Any) -> None:
        """
        Add a cleanup task to be executed during teardown.

        Args:
            task: Task or coroutine to execute during cleanup
        """
        self._cleanup_tasks.append(task)

    async def cleanup(self) -> None:
        """Clean up the test fixture."""
        # Execute cleanup tasks
        for task in self._cleanup_tasks:
            try:
                if asyncio.iscoroutine(task):
                    await task
                elif callable(task):
                    result = task()
                    if asyncio.iscoroutine(result):
                        await result
            except Exception:
                # Ignore cleanup errors
                pass

        # Shutdown mock app
        await self.mock_app.shutdown()

        # Clear collectors
        self.event_collector.clear()
        self.service_collector.clear()

    async def __aenter__(self) -> TestFixture:
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Async context manager exit."""
        await self.cleanup()


def create_test_fixture() -> TestFixture:
    """
    Create a test fixture.

    Returns:
        Test fixture instance
    """
    return TestFixture()
