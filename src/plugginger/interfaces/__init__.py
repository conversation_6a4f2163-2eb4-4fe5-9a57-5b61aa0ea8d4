# src/plugginger/interfaces/__init__.py

"""
Interface definitions using Protocol for dependency injection.

This package contains abstract interfaces (Protocols) that define the contracts
for various components in the Plugginger framework. These interfaces enable
dependency injection and make the system testable and modular.

Modules:
- services: Service-related interfaces
- events: Event system interfaces
- lifecycle: Plugin lifecycle interfaces
- runtime: Runtime component interfaces
"""

__all__ = [
    "services",
    "events",
    "lifecycle",
    "runtime",
]
