# src/plugginger/interfaces/services.py

"""
Service-related interface definitions.

This module defines the abstract interfaces for service management and dispatch
using Python's Protocol system. These interfaces enable dependency injection
and testing without circular imports.
"""

from __future__ import annotations

from typing import Any, Protocol

from plugginger.core.types import ServiceMethodType, ServiceName


class ServiceDispatcher(Protocol):
    """Interface for service call routing and dispatch."""

    async def call_service(self, service_name: ServiceName, *args: Any, **kwargs: Any) -> Any:
        """
        Dispatch a call to the named service and return its result.

        Args:
            service_name: Fully qualified name of the service to call
            *args: Positional arguments to pass to the service
            **kwargs: Keyword arguments to pass to the service

        Returns:
            The result returned by the service method

        Raises:
            ServiceNotFoundError: If the service is not registered
            ServiceExecutionError: If the service execution fails
        """
        ...

    def add_service(
        self, service_name: ServiceName, service_method: ServiceMethodType[..., Any]
    ) -> None:
        """
        Register a service method with the dispatcher.

        Args:
            service_name: Fully qualified name for the service
            service_method: The async method to register

        Raises:
            ServiceNameConflictError: If the service name is already registered
        """
        ...

    def has_service(self, service_name: ServiceName) -> bool:
        """
        Check if a service is registered.

        Args:
            service_name: Fully qualified name of the service

        Returns:
            True if the service is registered, False otherwise
        """
        ...

    def list_services(self) -> list[ServiceName]:
        """
        Get a list of all registered service names.

        Returns:
            List of fully qualified service names
        """
        ...

    def remove_service(self, service_name: ServiceName) -> bool:
        """
        Remove a service from the dispatcher.

        Args:
            service_name: Fully qualified name of the service to remove

        Returns:
            True if the service was removed, False if it wasn't registered
        """
        ...


class ServiceRegistry(Protocol):
    """Interface for service registration and discovery."""

    def register_service(
        self,
        plugin_instance_id: str,
        method_name: str,
        service_method: ServiceMethodType[..., Any],
        custom_name: str | None = None,
    ) -> ServiceName:
        """
        Register a service method from a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance
            method_name: Name of the method on the plugin
            service_method: The async method to register
            custom_name: Optional custom name for the service

        Returns:
            The fully qualified service name that was registered

        Raises:
            ServiceNameConflictError: If the service name is already registered
        """
        ...

    def unregister_plugin_services(self, plugin_instance_id: str) -> list[ServiceName]:
        """
        Unregister all services for a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance

        Returns:
            List of service names that were unregistered
        """
        ...

    def get_plugin_services(self, plugin_instance_id: str) -> list[ServiceName]:
        """
        Get all service names registered by a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance

        Returns:
            List of service names registered by the plugin
        """
        ...


class ServiceProxy(Protocol):
    """Interface for service call proxying (used in plugin dependency injection)."""

    async def __call__(self, service_name: ServiceName, *args: Any, **kwargs: Any) -> Any:
        """
        Proxy a service call.

        Args:
            service_name: Fully qualified name of the service to call
            *args: Positional arguments to pass to the service
            **kwargs: Keyword arguments to pass to the service

        Returns:
            The result returned by the service method
        """
        ...

    def __getattr__(self, name: str) -> Any:
        """
        Enable attribute-style service access.

        Args:
            name: Service name or plugin name

        Returns:
            A callable or proxy object for the service
        """
        ...
