# src/plugginger/implementations/__init__.py

"""
Concrete implementations of framework interfaces.

This package contains the actual implementations of the interfaces defined
in the interfaces package. These implementations can be swapped out for
testing or different runtime configurations.

Modules:
- container: Dependency injection container
- services: Service dispatcher implementation
- events: Event dispatcher implementation
- runtime: Runtime facade implementation
"""

__all__ = [
    "container",
    "services",
    "events",
    "runtime",
]
