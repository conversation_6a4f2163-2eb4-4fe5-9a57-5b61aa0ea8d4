# src/plugginger/implementations/container.py

"""
Dependency Injection Container implementation.

This module provides a simple but powerful DI container that resolves
circular import issues and enables testable, modular architecture.
"""

from __future__ import annotations

import inspect
from dataclasses import dataclass
from typing import Any, TypeVar, cast, get_type_hints

from plugginger.core.types import LoggerCallable

T = TypeVar("T")


@dataclass
class ParameterInfo:
    """
    Information about a constructor parameter for dependency injection.

    This dataclass encapsulates all relevant information about a parameter
    needed for dependency resolution and injection.

    Attributes:
        name: Parameter name
        annotation: Type annotation of the parameter
        default: Default value (inspect.Parameter.empty if no default)
        is_required: Whether the parameter is required (no default value)
        param_type: Resolved parameter type for dependency lookup
    """
    name: str
    annotation: Any
    default: Any
    is_required: bool
    param_type: type


class ParameterAnalyzer:
    """
    Analyzes constructor signatures for dependency injection.

    This class handles the complex logic of inspecting constructor signatures,
    extracting type annotations, and determining parameter requirements.
    """

    def __init__(self, logger: LoggerCallable) -> None:
        """
        Initialize the parameter analyzer.

        Args:
            logger: Logger function for debugging
        """
        self._logger = logger

    def analyze_constructor(self, cls: type) -> list[ParameterInfo]:
        """
        Analyze a class constructor and extract parameter information.

        Args:
            cls: The class whose constructor to analyze

        Returns:
            List of ParameterInfo objects for each injectable parameter

        Raises:
            MissingTypeAnnotationForDIError: If required parameter lacks type annotation
        """
        from plugginger.core.exceptions import MissingTypeAnnotationForDIError

        self._logger(f"[ParameterAnalyzer] Analyzing constructor for {cls.__name__}")

        # Get constructor signature
        signature = inspect.signature(cls.__init__)  # type: ignore[misc]

        # Get type hints for the constructor
        try:
            type_hints = get_type_hints(cls.__init__)  # type: ignore[misc]
        except (NameError, AttributeError) as e:
            self._logger(
                f"[ParameterAnalyzer] Warning: Could not get type hints for {cls.__name__}: {e}"
            )
            type_hints = {}

        parameters: list[ParameterInfo] = []

        # Process each parameter (skip 'self')
        for param_name, param in signature.parameters.items():
            if param_name == "self":
                continue

            # Handle special parameter kinds
            if param.kind in (inspect.Parameter.VAR_POSITIONAL, inspect.Parameter.VAR_KEYWORD):
                self._logger(
                    f"[ParameterAnalyzer] Skipping parameter '{param_name}' (*args/**kwargs not supported for DI)"
                )
                continue

            # Check if parameter has a type annotation
            if param_name not in type_hints and param.annotation == inspect.Parameter.empty:
                # No type annotation
                if param.default == inspect.Parameter.empty:
                    # Required parameter without type annotation - this is an error
                    raise MissingTypeAnnotationForDIError(
                        f"Constructor parameter '{param_name}' of class '{cls.__name__}' "
                        f"lacks type annotation and has no default value. "
                        f"DI container requires type annotations to resolve dependencies.",
                        class_name=cls.__name__,
                        parameter_name=param_name,
                    )
                else:
                    # Optional parameter without type annotation - skip injection, use default
                    self._logger(
                        f"[ParameterAnalyzer] Skipping parameter '{param_name}' (no type annotation, has default)"
                    )
                    continue

            # Get the parameter type
            param_type = type_hints.get(param_name, param.annotation)
            is_required = param.default == inspect.Parameter.empty

            param_info = ParameterInfo(
                name=param_name,
                annotation=param.annotation,
                default=param.default,
                is_required=is_required,
                param_type=param_type
            )

            parameters.append(param_info)
            self._logger(
                f"[ParameterAnalyzer] Found parameter '{param_name}': {param_type.__name__} "
                f"(required: {is_required})"
            )

        self._logger(f"[ParameterAnalyzer] Found {len(parameters)} injectable parameters for {cls.__name__}")
        return parameters


class DependencyResolver:
    """
    Resolves dependencies for constructor parameters.

    This class handles the logic of resolving dependencies from the DI container
    based on parameter information provided by the ParameterAnalyzer.
    """

    def __init__(self, container: DIContainer) -> None:
        """
        Initialize the dependency resolver.

        Args:
            container: The DI container to resolve dependencies from
        """
        self._container = container
        self._logger = container._logger

    def resolve_dependencies(self, params: list[ParameterInfo]) -> dict[str, Any]:
        """
        Resolve dependencies for the given parameters.

        Args:
            params: List of parameter information to resolve

        Returns:
            Dictionary mapping parameter names to resolved dependency instances

        Raises:
            DependencyResolutionError: If required dependency cannot be resolved
        """
        from plugginger.core.exceptions import DependencyResolutionError

        kwargs_for_init: dict[str, Any] = {}

        for param_info in params:
            self._logger(
                f"[DependencyResolver] Resolving dependency {param_info.param_type.__name__} "
                f"for parameter '{param_info.name}'"
            )

            try:
                dependency_instance: Any = self._container.get(param_info.param_type)
                kwargs_for_init[param_info.name] = dependency_instance
                self._logger(
                    f"[DependencyResolver] Successfully resolved {param_info.param_type.__name__} "
                    f"for '{param_info.name}'"
                )

            except ValueError as e:
                # Dependency not found in container
                if param_info.is_required:
                    # Required dependency that cannot be resolved
                    raise DependencyResolutionError(
                        f"Cannot resolve required dependency '{param_info.param_type.__name__}' "
                        f"for parameter '{param_info.name}'. "
                        f"Make sure the dependency is registered in the DI container.",
                        target_class="unknown",  # Will be set by caller
                        dependency_type=param_info.param_type.__name__,
                        parameter_name=param_info.name,
                    ) from e
                else:
                    # Optional dependency - use default value
                    self._logger(
                        f"[DependencyResolver] Optional dependency {param_info.param_type.__name__} "
                        f"not found, using default for '{param_info.name}'"
                    )
                    continue

        return kwargs_for_init


class DIContainer:
    """
    Simple dependency injection container.

    This container manages service registration and resolution, enabling
    loose coupling between components and eliminating circular import issues.
    Uses specialized components for parameter analysis and dependency resolution.
    """

    def __init__(self, logger: LoggerCallable | None = None) -> None:
        """
        Initialize the DI container.

        Args:
            logger: Optional logger function for debugging
        """
        self._services: dict[type, tuple[type, bool]] = {}
        self._singletons: dict[type, Any] = {}
        self._instances: dict[type, Any] = {}
        self._logger = logger or (lambda msg: None)

        # Initialize specialized components
        self._parameter_analyzer = ParameterAnalyzer(self._logger)
        self._dependency_resolver = DependencyResolver(self)

    def register(self, interface: type[T], implementation: type[T], singleton: bool = True) -> None:
        """
        Register a service implementation for an interface.

        Args:
            interface: The interface type (usually a Protocol)
            implementation: The concrete implementation class
            singleton: Whether to create only one instance (default: True)
        """
        self._logger(
            f"[DIContainer] Registering {implementation.__name__} for {interface.__name__}"
        )
        self._services[interface] = (implementation, singleton)

    def register_instance(self, interface: type[T], instance: T) -> None:
        """
        Register a pre-created instance for an interface.

        Args:
            interface: The interface type
            instance: The pre-created instance
        """
        self._logger(
            f"[DIContainer] Registering instance {type(instance).__name__} for {interface.__name__}"
        )
        self._instances[interface] = instance

    def register_concrete_instance(self, instance: T) -> None:
        """
        Register a pre-created instance using its concrete type.

        Args:
            instance: The pre-created instance
        """
        interface = type(instance)
        self._logger(f"[DIContainer] Registering concrete instance {interface.__name__}")
        self._instances[interface] = instance

    def get(self, interface: type[T]) -> T:
        """
        Get an instance of the requested interface.

        Args:
            interface: The interface type to resolve

        Returns:
            An instance implementing the interface

        Raises:
            ValueError: If the interface is not registered
        """
        # Check for pre-registered instances first
        if interface in self._instances:
            return cast(T, self._instances[interface])

        # Check for singleton instances
        if interface in self._singletons:
            return cast(T, self._singletons[interface])

        # Check for registered services
        if interface not in self._services:
            raise ValueError(f"Interface {interface.__name__} is not registered")

        implementation_class, is_singleton = self._services[interface]

        self._logger(f"[DIContainer] Creating instance of {implementation_class.__name__}")

        # Create instance (with dependency injection if needed)
        instance = self._create_instance(implementation_class)

        # Store as singleton if configured
        if is_singleton:
            self._singletons[interface] = instance

        return cast(T, instance)

    def _create_instance(self, implementation_class: type) -> Any:
        """
        Create an instance of the implementation class with recursive dependency injection.

        This method uses specialized components for parameter analysis and dependency
        resolution to reduce complexity and improve maintainability.

        Args:
            implementation_class: The class to instantiate

        Returns:
            An instance of the class with all dependencies injected

        Raises:
            MissingTypeAnnotationForDIError: If a required parameter lacks type annotation
            DependencyResolutionError: If a required dependency cannot be resolved
        """
        from plugginger.core.exceptions import DependencyResolutionError

        self._logger(f"[DIContainer] Resolving dependencies for {implementation_class.__name__}")

        try:
            # Analyze constructor parameters using specialized component
            parameters = self._parameter_analyzer.analyze_constructor(implementation_class)

            # Resolve dependencies using specialized component
            kwargs_for_init = self._dependency_resolver.resolve_dependencies(parameters)

            # Update error context for any DependencyResolutionError
            try:
                pass  # kwargs_for_init is already resolved
            except DependencyResolutionError as e:
                # Update the target_class in the error
                e.target_class = implementation_class.__name__
                raise

            # Create instance with resolved dependencies
            self._logger(
                f"[DIContainer] Instantiating {implementation_class.__name__} with resolved dependencies"
            )
            instance = implementation_class(**kwargs_for_init)
            self._logger(
                f"[DIContainer] Successfully created instance of {implementation_class.__name__}"
            )

            return instance

        except Exception as e:
            # Handle any errors from the specialized components
            if isinstance(e, (DependencyResolutionError,)):
                # Update target class if not already set
                if hasattr(e, 'target_class') and e.target_class == "unknown":
                    e.target_class = implementation_class.__name__
                raise
            else:
                # Wrap any other unexpected errors
                raise DependencyResolutionError(
                    f"Unexpected error while creating instance of {implementation_class.__name__}: {e}",
                    target_class=implementation_class.__name__,
                ) from e

    def has(self, interface: type) -> bool:
        """
        Check if an interface is registered.

        Args:
            interface: The interface type to check

        Returns:
            True if the interface is registered, False otherwise
        """
        return (
            interface in self._services
            or interface in self._instances
            or interface in self._singletons
        )

    def clear(self) -> None:
        """
        Clear all registrations and instances.

        This is useful for testing or resetting the container state.
        """
        self._logger("[DIContainer] Clearing all registrations")
        self._services.clear()
        self._singletons.clear()
        self._instances.clear()

    def list_registrations(self) -> dict[str, str]:
        """
        Get a list of all registered interfaces and their implementations.

        Returns:
            Dictionary mapping interface names to implementation names
        """
        registrations = {}

        for interface, (implementation, _) in self._services.items():
            registrations[interface.__name__] = implementation.__name__

        for interface, instance in self._instances.items():
            registrations[interface.__name__] = f"{type(instance).__name__} (instance)"

        return registrations


# Global container instance (can be replaced for testing)
_global_container: DIContainer | None = None


def get_container() -> DIContainer:
    """
    Get the global DI container instance.

    Returns:
        The global DIContainer instance
    """
    global _global_container
    if _global_container is None:
        _global_container = DIContainer()
    return _global_container


def set_container(container: DIContainer) -> None:
    """
    Set the global DI container instance.

    This is useful for testing or custom container configurations.

    Args:
        container: The DIContainer instance to use globally
    """
    global _global_container
    _global_container = container


def reset_container() -> None:
    """
    Reset the global DI container.

    This creates a new empty container, useful for testing.
    """
    global _global_container
    _global_container = DIContainer()
