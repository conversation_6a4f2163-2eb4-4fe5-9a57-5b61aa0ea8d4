# src/plugginger/implementations/services.py

"""
Service dispatcher implementation using dependency injection.

This module provides concrete implementations of service-related interfaces
without circular import dependencies.
"""

from __future__ import annotations

from typing import Any

from plugginger.core.types import LoggerCallable, ServiceMethodType, ServiceName
from plugginger.interfaces.services import ServiceDispatcher


class SimpleServiceDispatcher:
    """
    Simple implementation of ServiceDispatcher interface.

    This dispatcher routes service calls to registered methods without
    any external dependencies that could cause circular imports.
    """

    def __init__(self, logger: LoggerCallable | None = None) -> None:
        """
        Initialize the service dispatcher.

        Args:
            logger: Optional logger function
        """
        self._services: dict[ServiceName, ServiceMethodType[..., Any]] = {}
        self._logger = logger or (lambda msg: None)

    async def call_service(self, service_name: ServiceName, *args: Any, **kwargs: Any) -> Any:
        """
        Dispatch a call to the named service and return its result.

        Args:
            service_name: Fully qualified name of the service to call
            *args: Positional arguments to pass to the service
            **kwargs: Keyword arguments to pass to the service

        Returns:
            The result returned by the service method

        Raises:
            ServiceNotFoundError: If the service is not registered
            ServiceExecutionError: If the service execution fails
        """
        self._logger(f"[ServiceDispatcher] Calling service '{service_name}'")

        if service_name not in self._services:
            from plugginger.core.exceptions import ServiceNotFoundError

            raise ServiceNotFoundError(f"Service '{service_name}' not found")

        service_method = self._services[service_name]

        try:
            result = await service_method(*args, **kwargs)
            self._logger(f"[ServiceDispatcher] Service '{service_name}' completed successfully")
            return result
        except Exception as exc:
            self._logger(f"[ServiceDispatcher] Service '{service_name}' failed: {exc}")
            from plugginger.core.exceptions import ServiceExecutionError

            raise ServiceExecutionError(f"Error executing service '{service_name}'") from exc

    def add_service(
        self, service_name: ServiceName, service_method: ServiceMethodType[..., Any]
    ) -> None:
        """
        Register a service method with the dispatcher.

        Args:
            service_name: Fully qualified name for the service
            service_method: The async method to register

        Raises:
            ServiceNameConflictError: If the service name is already registered
        """
        if service_name in self._services:
            from plugginger.core.exceptions import ServiceNameConflictError

            raise ServiceNameConflictError(f"Service '{service_name}' is already registered")

        self._services[service_name] = service_method
        self._logger(f"[ServiceDispatcher] Registered service '{service_name}'")

    def has_service(self, service_name: ServiceName) -> bool:
        """
        Check if a service is registered.

        Args:
            service_name: Fully qualified name of the service

        Returns:
            True if the service is registered, False otherwise
        """
        return service_name in self._services

    def list_services(self) -> list[ServiceName]:
        """
        Get a list of all registered service names.

        Returns:
            List of fully qualified service names
        """
        return list(self._services.keys())

    def remove_service(self, service_name: ServiceName) -> bool:
        """
        Remove a service from the dispatcher.

        Args:
            service_name: Fully qualified name of the service to remove

        Returns:
            True if the service was removed, False if it wasn't registered
        """
        if service_name in self._services:
            del self._services[service_name]
            return True
        return False


class SimpleServiceRegistry:
    """
    Simple implementation of ServiceRegistry interface.

    This registry manages service registration from plugins and generates
    fully qualified service names.
    """

    def __init__(self, dispatcher: ServiceDispatcher, logger: LoggerCallable | None = None) -> None:
        """
        Initialize the service registry.

        Args:
            dispatcher: The service dispatcher to register services with
            logger: Optional logger function
        """
        self._dispatcher = dispatcher
        self._logger = logger or (lambda msg: None)
        self._plugin_services: dict[str, list[ServiceName]] = {}

    def register_service(
        self,
        plugin_instance_id: str,
        method_name: str,
        service_method: ServiceMethodType[..., Any],
        custom_name: str | None = None,
    ) -> ServiceName:
        """
        Register a service method from a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance
            method_name: Name of the method on the plugin
            service_method: The async method to register
            custom_name: Optional custom name for the service

        Returns:
            The fully qualified service name that was registered

        Raises:
            ServiceNameConflictError: If the service name is already registered
        """
        # Generate fully qualified service name
        local_name = custom_name or method_name
        service_name = f"{plugin_instance_id}.{local_name}"

        # Register with dispatcher
        self._dispatcher.add_service(service_name, service_method)

        # Track for plugin
        if plugin_instance_id not in self._plugin_services:
            self._plugin_services[plugin_instance_id] = []
        self._plugin_services[plugin_instance_id].append(service_name)

        self._logger(
            f"[ServiceRegistry] Registered service '{service_name}' for plugin '{plugin_instance_id}'"
        )
        return service_name

    def unregister_plugin_services(self, plugin_instance_id: str) -> list[ServiceName]:
        """
        Unregister all services for a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance

        Returns:
            List of service names that were unregistered
        """
        if plugin_instance_id not in self._plugin_services:
            return []

        service_names = self._plugin_services[plugin_instance_id].copy()

        # Remove from dispatcher (if it supports removal)
        if hasattr(self._dispatcher, "remove_service"):
            for service_name in service_names:
                self._dispatcher.remove_service(service_name)  # type: ignore

        # Remove from tracking
        del self._plugin_services[plugin_instance_id]

        self._logger(
            f"[ServiceRegistry] Unregistered {len(service_names)} services for plugin '{plugin_instance_id}'"
        )
        return service_names

    def get_plugin_services(self, plugin_instance_id: str) -> list[ServiceName]:
        """
        Get all service names registered by a plugin.

        Args:
            plugin_instance_id: ID of the plugin instance

        Returns:
            List of service names registered by the plugin
        """
        return self._plugin_services.get(plugin_instance_id, []).copy()
