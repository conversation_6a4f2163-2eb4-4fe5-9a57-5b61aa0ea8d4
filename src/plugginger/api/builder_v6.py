# src/plugginger/api/builder.py

"""
Application builder API using dependency injection.

This module provides the PluggingerAppBuilder class for constructing and
configuring Plugginger applications with comprehensive dependency management,
validation, and fractal composition support.
"""

from __future__ import annotations

import logging
from typing import TYPE_CHECKING, Any

from pydantic import BaseModel, ValidationError

from plugginger._internal.graph import DependencyGraph
from plugginger._internal.runtime_facade import RuntimeFacade
from plugginger._internal.validation import DependencyValidator
from plugginger.api.app import PluggingerAppInstance
from plugginger.api.depends import Depends
from plugginger.api.plugin import PluginBase, is_plugin_class
from plugginger.config.models import ExecutorConfig, GlobalAppConfig
from plugginger.core.config import DEFAULT_APP_NAME
from plugginger.core.exceptions import (
    AppPluginError,
    ConfigurationError,
    PluginRegistrationError,
)
from plugginger.utils.generic_proxy import GenericPluginProxy

if TYPE_CHECKING:
    from plugginger.api.app_plugin import AppPluginBase


class PluggingerAppBuilder:
    """
    Builder for constructing Plugginger applications with dependency injection.

    This class provides comprehensive application building functionality including
    dependency graph management, validation, plugin instantiation with DI,
    and fractal composition support for nested applications.

    Attributes:
        _app_name: Name of the application being built
        _parent_app_plugin_context: Parent AppPlugin context for fractal composition
        _current_depth: Current nesting depth for fractal composition
        _max_fractal_depth: Maximum allowed nesting depth
        _registered_item_classes: Mapping of registration names to plugin classes
        _plugin_dependency_declarations: Mapping of plugin names to their dependencies
        _logger: Logger instance for build process logging
    """

    def __init__(
        self,
        app_name: str = DEFAULT_APP_NAME,
        parent_app_plugin_context: AppPluginBase | None = None,
        _current_depth: int = 0,
        _max_depth_from_config: int | None = None,
    ) -> None:
        """
        Initialize the application builder.

        Args:
            app_name: Name of the application to build
            parent_app_plugin_context: Parent AppPlugin for fractal composition
            _current_depth: Current nesting depth (internal use)
            _max_depth_from_config: Maximum depth override (internal use)
        """
        self._app_name = app_name
        self._parent_app_plugin_context = parent_app_plugin_context
        self._current_depth = _current_depth
        self._max_fractal_depth = _max_depth_from_config or 3  # Default from GlobalAppConfig

        # Plugin registration storage
        self._registered_item_classes: dict[str, type[PluginBase] | type[AppPluginBase]] = {}
        self._plugin_dependency_declarations: dict[str, list[Depends]] = {}

        # Logger for build process
        self._logger = logging.getLogger("plugginger.builder")

    def _get_plugin_metadata_attr(self, plugin_class: type[PluginBase], attr_name: str) -> Any:
        """
        Get a metadata attribute from a plugin class.

        Args:
            plugin_class: Plugin class to inspect
            attr_name: Name of the metadata attribute

        Returns:
            The metadata attribute value

        Raises:
            PluginRegistrationError: If metadata attribute is missing
        """
        if not hasattr(plugin_class, attr_name):
            raise PluginRegistrationError(
                f"Plugin class '{plugin_class.__name__}' is missing required metadata attribute '{attr_name}'. "
                f"Ensure the class is decorated with @plugin."
            )
        return getattr(plugin_class, attr_name)

    def _register_item(self, registration_name: str, item_class: type[PluginBase]) -> None:
        """
        Register a plugin or app-plugin class.

        Args:
            registration_name: Name to register the item under
            item_class: Plugin class to register

        Raises:
            PluginRegistrationError: If registration fails
        """
        # Check for duplicate registration
        if registration_name in self._registered_item_classes:
            raise PluginRegistrationError(
                f"Plugin with name '{registration_name}' is already registered"
            )

        # Store the class
        self._registered_item_classes[registration_name] = item_class

        # Extract and store dependency declarations
        needs_attr = getattr(item_class, 'needs', [])
        if not isinstance(needs_attr, list):
            raise PluginRegistrationError(
                f"Plugin '{registration_name}' has invalid 'needs' attribute. Must be a list of Depends objects."
            )

        self._plugin_dependency_declarations[registration_name] = needs_attr.copy()

        self._logger.debug(f"Registered plugin '{registration_name}' with {len(needs_attr)} dependencies")

    def include(self, plugin_class: type[PluginBase]) -> PluggingerAppBuilder:
        """
        Include a plugin class in the application.

        Args:
            plugin_class: Plugin class to include

        Returns:
            Self for method chaining

        Raises:
            PluginRegistrationError: If plugin registration fails
        """
        # Validate plugin class
        if not is_plugin_class(plugin_class):
            raise PluginRegistrationError(
                f"Class '{plugin_class.__name__}' is not a valid plugin class. "
                f"Make sure it inherits from PluginBase and has the @plugin decorator."
            )

        # Get plugin metadata
        plugin_name = self._get_plugin_metadata_attr(plugin_class, '_plugginger_plugin_name')

        # Register the plugin
        self._register_item(plugin_name, plugin_class)

        return self

    def include_app(self, app_plugin_class: type[AppPluginBase]) -> PluggingerAppBuilder:
        """
        Include an AppPlugin class for fractal composition.

        Args:
            app_plugin_class: AppPlugin class to include

        Returns:
            Self for method chaining

        Raises:
            AppPluginError: If fractal depth limit is exceeded
            PluginRegistrationError: If plugin registration fails
        """
        # Check fractal depth limit
        if self._current_depth + 1 > self._max_fractal_depth:
            raise AppPluginError(
                f"Maximum fractal depth ({self._max_fractal_depth}) exceeded. "
                f"Cannot include AppPlugin at depth {self._current_depth + 1}"
            )

        # Validate and register as a regular plugin
        if not is_plugin_class(app_plugin_class):
            raise PluginRegistrationError(
                f"Class '{app_plugin_class.__name__}' is not a valid plugin class. "
                f"Make sure it inherits from AppPluginBase and has the @plugin decorator."
            )

        # Get plugin metadata
        plugin_name = self._get_plugin_metadata_attr(app_plugin_class, '_plugginger_plugin_name')

        # Register the app plugin
        self._register_item(plugin_name, app_plugin_class)

        return self

    def _generate_plugin_instance_id(self, registration_name: str) -> str:
        """
        Generate a hierarchical instance ID for a plugin.

        Args:
            registration_name: Registration name of the plugin

        Returns:
            Hierarchical instance ID
        """
        if self._parent_app_plugin_context is not None:
            parent_id = self._parent_app_plugin_context._plugginger_instance_id
            return f"{parent_id}.{registration_name}"
        else:
            return registration_name
