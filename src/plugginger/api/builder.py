# src/plugginger/api/builder.py

"""
Defines the `PluggingerAppBuilder` class, used to configure and build
a `PluggingerAppInstance`. This is the primary entry point for assembling
a Plugginger application from its constituent plugins and app-plugins,
incorporating comprehensive dependency management, validation, and support
for fractal composition.
"""

from __future__ import annotations

import inspect
import logging # Using standard Python logging
from typing import Any, Dict, List, Type, Optional, Union, cast, Set, Tuple

from pydantic import BaseModel, ValidationError # For config validation

# Plugginger API and internal imports
from plugginger.api.plugin import PluginBase # For type checking and metadata access
from plugginger.api.depends import Depends
from plugginger.api.app_plugin import AppPluginBase # For fractal composition
from plugginger.api.app import PluggingerAppInstance # The product of the builder
from plugginger.api.config_schemas import GlobalAppConfig, ExecutorConfig # Default config model
from plugginger.api.exceptions import (
    ConfigurationError,
    PluginRegistrationError,
    MissingDependencyError,
    CircularDependencyError,
    DependencyVersionConflictError,
    ServiceNameConflictError,
    AppPluginError,
    DependencyError,
)
# Internal components
from plugginger._internal.runtime_facade import RuntimeFacade
from plugginger._internal.graph import DependencyGraph
from plugginger._internal.validation import DependencyValidator, VersionResolverFunc, ClassResolverFunc
from plugginger._internal.proxy import GenericPluginProxy
from plugginger._internal.constants import (
    SERVICE_METADATA_KEY,
    EVENT_METADATA_KEY,
    PLUGIN_METADATA_KEY,
)
from plugginger._internal.typing_helpers import (
    LoggerCallable, ServiceMethodType, EventHandlerType
)

# Standard Python logger for the builder module
builder_logger: logging.Logger = logging.getLogger("plugginger.builder")
# Ensure a handler is present for the logger if not configured elsewhere (e.g., for library use)
if not builder_logger.handlers and not logging.getLogger("plugginger").handlers: # Check root too
    _handler = logging.StreamHandler()
    _formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    _handler.setFormatter(_formatter)
    # Add handler to the 'plugginger' root logger to affect all sub-loggers
    logging.getLogger("plugginger").addHandler(_handler)
    logging.getLogger("plugginger").setLevel(logging.WARNING) # Default for library
    builder_logger.setLevel(logging.INFO) # Builder can be more verbose if needed


class PluggingerAppBuilder:
    """
    Collects plugin and app-plugin class definitions, resolves their dependencies,
    validates configurations, and ultimately builds a runnable `PluggingerAppInstance`.

    This builder implements a comprehensive build process including:
    - Registration of `PluginBase` and `AppPluginBase` subclasses.
    - Construction and validation of a dependency graph (detecting cycles, missing deps).
    - Validation of version constraints between plugins.
    - Enforcement of maximum fractal depth for nested AppPlugins.
    - Instantiation of plugins in topological order.
    - Dependency Injection (DI) of plugin proxies into constructors, validated against
      `__init__` signatures and `needs` declarations.
    - Processing of global and plugin-specific configurations using Pydantic schemas.
    - Registration of `@service` methods and `@on_event` listeners with the runtime.
    - Setup of event bridges for `AppPluginBase` instances.
    """

    _app_name: str
    _parent_app_plugin_context: Optional[AppPluginBase]
    _current_fractal_depth: int
    _max_fractal_depth: int
    _instance_id_prefix: Optional[str]

    _registered_item_classes: Dict[str, Union[Type[PluginBase], Type[AppPluginBase]]]
    _plugin_dependency_declarations: Dict[str, List[Depends]]
    _logger: logging.Logger # Instance logger

    def __init__(
        self,
        app_name: str,
        parent_app_plugin_context: Optional[AppPluginBase] = None,
        _current_depth: int = 0, # Internal: tracks nesting for fractal depth limit
        _max_depth_from_config: Optional[int] = None, # Passed from outer builder if sub-app
    ):
        """
        Initializes a new `PluggingerAppBuilder`.

        Args:
            app_name: The conceptual name of the application being built. This name
                      is used for logging and identification.
            parent_app_plugin_context: If this builder is used by an `AppPluginBase`
                                       to construct its internal app, this should be the
                                       instance of that `AppPluginBase`. It's used for
                                       context in fractal compositions (e.g., event bridging).
            _current_depth: Internal parameter to track the current nesting depth
                            of AppPlugins. Users should not set this directly.
            _max_depth_from_config: Internal parameter to propagate the maximum
                                    fractal depth from a parent builder.
        """
        if not isinstance(app_name, str) or not app_name:
            raise ValueError("PluggingerAppBuilder.app_name must be a non-empty string.")

        self._app_name = app_name
        self._parent_app_plugin_context = parent_app_plugin_context
        self._current_fractal_depth = _current_depth

        # Set instance ID prefix for hierarchical naming
        if parent_app_plugin_context is not None:
            self._instance_id_prefix = getattr(parent_app_plugin_context, '_plugginger_instance_id', app_name)
        else:
            self._instance_id_prefix = None

        # Max depth is either passed down or taken from GlobalAppConfig's default
        # It will be re-evaluated when the actual GlobalAppConfig is processed in build()
        default_max_depth = GlobalAppConfig.model_fields["max_fractal_depth"].default \
            if GlobalAppConfig.model_fields["max_fractal_depth"].default is not None else 10

        self._max_fractal_depth = _max_depth_from_config if _max_depth_from_config is not None \
                                  else default_max_depth

        self._registered_item_classes = {}
        self._plugin_dependency_declarations = {}
        self._logger = builder_logger # Use the module-level logger

        self._logger.info(f"PluggingerAppBuilder initialized for app '{app_name}' "
                          f"(depth: {_current_depth}, max_depth_initial: {self._max_fractal_depth}).")

    def _get_plugin_metadata_attr(
        self,
        plugin_class_to_check: Type[Any],
        attribute_name: str,
        context_message: str = "plugin processing"
    ) -> Any:
        """
        Safely retrieves a metadata attribute (like `_plugginger_plugin_name`)
        from a class, ensuring it was decorated with `@plugin`.

        Args:
            plugin_class_to_check: The class to inspect.
            attribute_name: The name of the metadata attribute to retrieve.
            context_message: A string describing the context of this call, for error messages.

        Returns:
            The value of the metadata attribute.

        Raises:
            PluginRegistrationError: If the class is not a valid plugin or lacks the attribute.
        """
        if not (inspect.isclass(plugin_class_to_check) and hasattr(plugin_class_to_check, PLUGIN_METADATA_KEY)):
             raise PluginRegistrationError(
                f"Class '{plugin_class_to_check.__name__}' used in '{context_message}' is not a valid "
                "Plugginger plugin (missing @plugin decorator or not a class)."
            )
        if not hasattr(plugin_class_to_check, attribute_name):
            # This should ideally be caught by the @plugin decorator itself if it ensures
            # all necessary attributes are set. This is a safeguard.
            raise PluginRegistrationError( # pragma: no cover
                f"Plugin class '{plugin_class_to_check.__name__}' is missing essential Plugginger "
                f"metadata attribute '{attribute_name}'. Ensure it's correctly processed by @plugin."
            )
        return getattr(plugin_class_to_check, attribute_name)

    def _register_item_class(
        self,
        registration_name: str, # The name under which this item is registered in *this* builder
        item_class: Union[Type[PluginBase], Type[AppPluginBase]],
        is_app_plugin_type: bool
    ) -> None:
        """
        Internal helper to register a plugin or AppPlugin class.
        Validates the class and stores its definition and dependency declarations.
        """
        if not registration_name or not registration_name.isidentifier():
             raise PluginRegistrationError(
                 f"Invalid registration name '{registration_name}' for class '{item_class.__name__}'. "
                 "Must be a valid Python identifier (e.g., 'my_plugin')."
            )

        if registration_name in self._registered_item_classes:
            raise PluginRegistrationError(
                f"An item (Plugin or AppPlugin) with the registration name '{registration_name}' "
                f"is already included in the builder for app '{self._app_name}'."
            )

        # Ensure @plugin decorator was applied (checks for PLUGIN_METADATA_KEY)
        # and retrieve its declared name for consistency check if needed.
        # The actual @plugin(name=...) is more for self-identification of the plugin class.
        self._get_plugin_metadata_attr(item_class, "_plugginger_plugin_name", f"register_item({registration_name})")

        # Type checking for base classes
        if is_app_plugin_type:
            if not issubclass(item_class, AppPluginBase): # pragma: no cover
                raise PluginRegistrationError(
                    f"Class '{item_class.__name__}' registered as an AppPlugin (via include_app as '{registration_name}') "
                    "must inherit from `plugginger.api.app_plugin.AppPluginBase`."
                )
        else: # Regular plugin
            if not issubclass(item_class, PluginBase): # pragma: no cover
                # This case should be caught by @plugin decorator if PluginBase inheritance is enforced there.
                # If @plugin allows decorating non-PluginBase, this check is vital.
                # Assuming @plugin enforces PluginBase inheritance.
                raise PluginRegistrationError(
                    f"Class '{item_class.__name__}' registered as a Plugin (via include as '{registration_name}') "
                    "must inherit from `plugginger.api.plugin.PluginBase`."
                )
            if issubclass(item_class, AppPluginBase): # Regular plugins cannot be AppPlugins
                raise PluginRegistrationError(
                    f"Class '{item_class.__name__}' (an AppPlugin) was registered using `include()`. "
                    f"Please use `builder.include_app(..., as_plugin_name='{registration_name}')` for AppPlugins."
                )


        self._registered_item_classes[registration_name] = item_class

        # Retrieve 'needs' from the class, defaulting to empty list (PluginBase provides this default)
        class_level_needs: List[Depends] = getattr(item_class, "needs", [])
        if not isinstance(class_level_needs, list) or \
           not all(isinstance(dep, Depends) for dep in class_level_needs):
            raise PluginRegistrationError( # pragma: no cover
                f"Plugin '{registration_name}' (class {item_class.__name__}) has an invalid 'needs' attribute. "
                "It must be a `List[Depends]`."
            )
        self._plugin_dependency_declarations[registration_name] = list(class_level_needs) # Store a copy

        log_type = "AppPlugin class" if is_app_plugin_type else "Plugin class"
        self._logger.debug(f"{log_type} '{item_class.__name__}' registered as '{registration_name}' "
                           f"for app '{self._app_name}' with {len(class_level_needs)} declared dependencies.")

    def include(self, plugin_class: Type[PluginBase]) -> "PluggingerAppBuilder":
        """
        Registers a standard plugin class with the builder.

        The class must be decorated with `@plugin` and inherit from `PluginBase`.
        It must **not** be an `AppPluginBase` subclass; use `include_app()` for those.
        The plugin will be registered under the name specified in its `@plugin(name=...)` decorator.

        Args:
            plugin_class: The `PluginBase` subclass to include.

        Returns:
            The builder instance, allowing for a fluent (chained) interface.
        """
        if not inspect.isclass(plugin_class):
            raise TypeError(f"Argument to `include()` must be a class, got {type(plugin_class)}.")

        # Retrieve the registration name from the @plugin decorator
        registration_name: str = self._get_plugin_metadata_attr(plugin_class, "_plugginger_plugin_name", "include")
        self._register_item_class(registration_name, plugin_class, is_app_plugin_type=False)
        return self

    def include_app(
        self,
        app_plugin_class: Type[AppPluginBase],
        as_plugin_name: str, # This is how the outer app refers to this AppPlugin
    ) -> "PluggingerAppBuilder":
        """
        Registers an `AppPluginBase` subclass, enabling fractal composition.

        The `app_plugin_class` (which must inherit from `AppPluginBase` and be
        decorated with `@plugin`) will manage its own internal `PluggingerAppInstance`.

        Args:
            app_plugin_class: The `AppPluginBase` subclass to include.
            as_plugin_name: The name under which this AppPlugin will be known and
                            addressable within the current application being built.
                            This name is used for dependency injection targeting this
                            AppPlugin and for namespacing its exported services/events.
                            It must be a valid Python identifier.

        Returns:
            The builder instance for chaining.
        """
        if self._current_fractal_depth + 1 > self._max_fractal_depth:
            raise AppPluginError(
                f"Cannot include AppPlugin '{as_plugin_name}' (class {app_plugin_class.__name__}): "
                f"Exceeds maximum configured fractal depth of {self._max_fractal_depth} "
                f"(attempting to build at depth {self._current_fractal_depth + 1})."
            )

        if not inspect.isclass(app_plugin_class):
            raise TypeError(f"Argument to `include_app()` must be a class, got {type(app_plugin_class)}.")

        # The AppPlugin is registered in *this* builder under `as_plugin_name`.
        # Its own `@plugin(name=...)` is for its self-identification if it were top-level,
        # or if other plugins *within its own internal app* were to depend on its facade (less common).
        self._register_item_class(as_plugin_name, app_plugin_class, is_app_plugin_type=True)
        return self

    def _generate_plugin_instance_id(self, registration_name_in_current_scope: str) -> str:
        """
        Generates a globally unique, hierarchical instance ID for a plugin.
        If this builder is for a top-level app, `_app_name` is used as prefix.
        If for a sub-app, `_parent_app_plugin_context._plugginger_instance_id` is used.

        Args:
            registration_name_in_current_scope: The name under which the plugin
                is registered in the current builder's scope.

        Returns:
            The globally unique instance ID string.
        """
        # The instance_id prefix is determined by the context this builder operates in.
        # If it's a top-level builder, _parent_app_plugin_context is None.
        # If it's a builder for an AppPlugin's internal app, _parent_app_plugin_context is the AppPlugin instance.

        # Let's use self._app_name as the conceptual prefix for the current app level.
        # The builder's own `_app_name` should already be correctly namespaced if it's a sub-app builder.
        # Example:
        # Top builder: app_name="main"
        #   - Plugin "db" -> instance_id="main:db"
        #   - AppPlugin "users_module" (class UserManagementAppPlugin @plugin(name="user_mgmt_facade"))
        #     -> instance_id="main:users_module"
        #     Its internal builder: app_name="main:users_module_internal_app"
        #       - Internal Plugin "auth" -> instance_id="main:users_module_internal_app:auth"
        # This seems overly complex.
        # Simpler: instance_id is just the registration_name at its current level,
        # and full qualification happens at runtime if needed by looking up the hierarchy.
        # Or, the builder gets an `instance_id_prefix` at __init__.

        # Using the `_instance_id_prefix` set during builder initialization:
        if self._instance_id_prefix: # This builder is for a sub-app
            return f"{self._instance_id_prefix}:{registration_name_in_current_scope}"
        # This builder is for a top-level app, use its conceptual name as prefix
        return f"{self._app_name}:{registration_name_in_current_scope}"


    def build(
        self,
        app_config_input: Optional[Union[GlobalAppConfig, Dict[str, Any]]] = None,
    ) -> PluggingerAppInstance:
        """
        Builds and returns a `PluggingerAppInstance` based on the registered items
        and provided configuration. (Full docstring in Tranche 4 Prompt)
        """
        self._logger.info(f"Starting build for PluggingerAppInstance '{self._app_name}' "
                          f"(prefix: '{self._instance_id_prefix}', depth: {self._current_fractal_depth}).")

        # 0. Resolve and Validate Global Configuration
        global_config: GlobalAppConfig = self._resolve_and_validate_global_config(app_config_input)
        # Update max_fractal_depth based on potentially more restrictive config
        self._max_fractal_depth = min(self._max_fractal_depth, global_config.max_fractal_depth)
        if self._current_fractal_depth >= self._max_fractal_depth: # Check initial depth too
            raise AppPluginError(f"Initial build depth {self._current_fractal_depth} already meets or "
                                 f"exceeds max_fractal_depth {self._max_fractal_depth} for app '{self._app_name}'.")


        # 1. Initialize Runtime Components (via RuntimeFacade)
        # The logger passed to RuntimeFacade should be configured based on global_config.log_level
        # For now, builder's logger is used as a placeholder for this detail.
        runtime_facade = RuntimeFacade(
            fault_policy=global_config.event_listener_fault_policy,
            default_listener_timeout=global_config.default_event_listener_timeout_seconds,
            max_concurrent_listener_tasks=getattr(global_config, 'max_concurrent_listener_tasks', 50),
            default_executor_config=global_config.executors[0] if global_config.executors else ExecutorConfig(name="default"),
            logger=self._logger.info # Pass a callable logger
        )

        # 2. Dependency Graph Construction
        graph = DependencyGraph[str]() # Nodes are registration_names in current scope
        for reg_name in self._registered_item_classes:
            graph.add_node(reg_name)

        for dependent_reg_name, depends_list in self._plugin_dependency_declarations.items():
            for dep_obj in depends_list:
                prerequisite_reg_name: str
                if isinstance(dep_obj.plugin_identifier, str):
                    prerequisite_reg_name = dep_obj.plugin_identifier
                else: # It's a Type
                    prerequisite_reg_name = self._get_plugin_metadata_attr(
                        dep_obj.plugin_identifier, "_plugginger_plugin_name", "dependency_resolution"
                    )
                graph.add_dependency_edge(dependent_node=dependent_reg_name, prerequisite_node=prerequisite_reg_name)
        self._logger.debug(f"Dependency graph constructed for '{self._app_name}' with {len(graph)} nodes.")

        # 3. Dependency Graph Validation
        def get_class_by_reg_name(reg_name: str) -> Type[PluginBase]:
            cls = self._registered_item_classes.get(reg_name)
            if cls is None: raise MissingDependencyError(f"Class for '{reg_name}' not found.") # Should not happen
            return cls  # type: ignore[return-value]  # AppPluginBase is also valid
        def get_version_by_reg_name(reg_name: str) -> str:
            cls = get_class_by_reg_name(reg_name)
            version = self._get_plugin_metadata_attr(cls, "_plugginger_plugin_version", "version_resolver")
            return str(version)

        validator = DependencyValidator(
            version_resolver_func=get_version_by_reg_name,
            class_resolver_func=get_class_by_reg_name
        )
        try:
            validator.validate_graph_structure(graph, set(self._registered_item_classes.keys()))
            sorted_registration_names: List[str] = graph.topological_sort() # Detects cycles
            validator.validate_dependency_versions_and_signatures(self._plugin_dependency_declarations)
        except (MissingDependencyError, CircularDependencyError, DependencyVersionConflictError, DependencyError) as e:
            self._logger.error(f"Dependency validation failed for app '{self._app_name}': {e!r}", exc_info=True)
            raise
        self._logger.info(f"Dependency validation successful. Plugin setup order: {sorted_registration_names}")

        # 4. Create Provisional PluggingerAppInstance (passed to plugin constructors)
        # This instance_id is for the app being built.
        app_instance_id = self._instance_id_prefix or self._app_name

        provisional_app_instance = PluggingerAppInstance(
            app_name=self._app_name,
            runtime_facade=runtime_facade,
            global_config=global_config,
            parent_app_plugin_context=self._parent_app_plugin_context,
            _builder_fractal_depth=self._current_fractal_depth
        )
        # Make builder's fractal depth info available to AppPluginBase._configure_internal_app
        setattr(provisional_app_instance, "_current_build_depth_for_sub_apps", self._current_fractal_depth)
        setattr(provisional_app_instance, "_max_build_depth_for_sub_apps", self._max_fractal_depth)


        # 5. Plugin Instantiation & Dependency Injection
        plugin_instances_map: Dict[str, PluginBase] = {} # registration_name -> instance
        plugin_configs_for_setup: Dict[str, BaseModel] = {} # instance_id -> validated_config

        for registration_name in sorted_registration_names:
            plugin_class = self._registered_item_classes[registration_name]
            # Instance ID for this specific plugin instance
            instance_id = self._generate_plugin_instance_id(registration_name)

            injected_deps_kwargs: Dict[str, GenericPluginProxy] = {}
            for dep_obj in self._plugin_dependency_declarations.get(registration_name, []):
                dep_target_reg_name: str
                if isinstance(dep_obj.plugin_identifier, str):
                    dep_target_reg_name = dep_obj.plugin_identifier
                else:
                    dep_target_reg_name = self._get_plugin_metadata_attr(
                        dep_obj.plugin_identifier, "_plugginger_plugin_name", "di_target_resolution"
                    )

                # The key for kwargs must match the __init__ parameter name.
                # DependencyValidator already confirmed this match.
                target_plugin_instance_id = self._generate_plugin_instance_id(dep_target_reg_name)
                injected_deps_kwargs[dep_target_reg_name] = GenericPluginProxy(
                    provisional_app_instance, # App context for proxied calls
                    target_plugin_instance_id,       # Instance ID of the dependency
                    dep_target_reg_name       # Registration name of the dependency (for service FQN)
                )

            try:
                # Pass provisional_app_instance as 'app' to constructor
                plugin_instance = plugin_class(app=provisional_app_instance, **injected_deps_kwargs)
            except Exception as e:
                self._logger.error(f"Failed to instantiate plugin '{registration_name}' (class: {plugin_class.__name__}): {e!r}", exc_info=True)
                raise PluginRegistrationError(f"Instantiation failed for '{registration_name}'.") from e

            setattr(plugin_instance, "_plugginger_instance_id", instance_id) # Set the unique ID
            plugin_instances_map[registration_name] = plugin_instance

            # Prepare validated config for its setup() method
            plugin_class_config_schema = self._get_plugin_metadata_attr(plugin_class, "_plugginger_config_schema", "config_schema_lookup")
            raw_config_for_plugin = global_config.plugin_configs.get(instance_id, {})

            if plugin_class_config_schema:
                try:
                    validated_config = plugin_class_config_schema(**raw_config_for_plugin)
                    plugin_configs_for_setup[instance_id] = validated_config
                except ValidationError as e:
                    err_msg = (f"Invalid configuration for plugin instance '{instance_id}' "
                               f"(class {plugin_class.__name__}, registration name '{registration_name}'). "
                               f"Pydantic errors: {e.errors()}")
                    self._logger.error(err_msg)
                    raise ConfigurationError(err_msg, validation_errors=e.errors()) from e
            elif raw_config_for_plugin:
                plugin_configs_for_setup[instance_id] = cast(BaseModel, raw_config_for_plugin) # Pass as dict
                self._logger.warning(f"Plugin instance '{instance_id}' received configuration but has no "
                                   "`config_schema` defined. Config passed as raw dict to `setup`.")
            else: # No config in GlobalAppConfig and no schema, or schema allows all defaults
                if plugin_class_config_schema: # Schema exists, use its defaults
                    plugin_configs_for_setup[instance_id] = plugin_class_config_schema()
                else: # No schema, no config: empty dict
                    plugin_configs_for_setup[instance_id] = cast(BaseModel, {})
            self._logger.debug(f"Prepared config for plugin instance '{instance_id}'.")


        # 6. Service and Event Listener Registration
        self._register_all_plugin_interfaces(plugin_instances_map, runtime_facade)

        # 7. Finalize RuntimeFacade with all instantiated plugins and their setup configs
        final_plugins_in_order_list = [plugin_instances_map[reg_name] for reg_name in sorted_registration_names]
        runtime_facade.finalize_setup(
            plugins_in_order=final_plugins_in_order_list,
            plugin_configs_for_setup=plugin_configs_for_setup
        )

        self._logger.info(f"PluggingerAppInstance '{self._app_name}' "
                            f"built successfully with {len(final_plugins_in_order_list)} plugins.")
        return provisional_app_instance


    def _resolve_and_validate_global_config(
        self, app_config_input: Optional[Union[GlobalAppConfig, Dict[str, Any]]]
    ) -> GlobalAppConfig:
        """Validates and returns a GlobalAppConfig instance."""
        if app_config_input is None:
            cfg = GlobalAppConfig(app_name=self._app_name, max_fractal_depth=self._max_fractal_depth)
            self._logger.debug(f"No app_config_input provided for '{self._app_name}', using default GlobalAppConfig.")
            return cfg
        if isinstance(app_config_input, GlobalAppConfig):
            # Ensure app_name consistency
            if app_config_input.app_name != self._app_name and self._app_name : # Allow override if builder name was placeholder
                 self._logger.warning(
                    f"Provided GlobalAppConfig.app_name ('{app_config_input.app_name}') differs from "
                    f"builder's app_name ('{self._app_name}'). Builder's name ('{self._app_name}') will be used for this instance."
                )
            app_config_input.app_name = self._app_name # Builder's name is authoritative
            return app_config_input
        if isinstance(app_config_input, dict):
            try:
                # Ensure app_name from builder is used, and max_fractal_depth is respected
                config_dict_with_defaults = {"app_name": self._app_name, "max_fractal_depth": self._max_fractal_depth, **app_config_input}
                return GlobalAppConfig(**config_dict_with_defaults)
            except ValidationError as e:
                self._logger.error(f"Invalid app_config dictionary for '{self._app_name}': {e.errors()}")
                raise ConfigurationError(f"Invalid app_config dictionary for '{self._app_name}'.", validation_errors=e.errors()) from e

        raise ConfigurationError( # pragma: no cover
            f"app_config_input for '{self._app_name}' must be a GlobalAppConfig instance, a compatible dict, or None. "
            f"Got {type(app_config_input)}."
        )

    def _register_all_plugin_interfaces(
        self,
        plugin_instances_map: Dict[str, PluginBase], # registration_name -> instance
        runtime_facade: RuntimeFacade
    ) -> None:
        """Iterates through instantiated plugins and registers their services and event listeners."""
        self._logger.debug("Registering all plugin interfaces (services and event listeners).")
        for registration_name, plugin_instance in plugin_instances_map.items():
            self._register_plugin_services_and_events(
                plugin_instance,
                registration_name, # This is the name it's known by in this app scope
                runtime_facade.get_service_dispatcher(), # type: ignore[attr-defined]
                runtime_facade.get_event_dispatcher()    # type: ignore[attr-defined]
            )
            # If it's an AppPlugin, finalize its event bridges
            if isinstance(plugin_instance, AppPluginBase):
                self._finalize_app_plugin_event_bridges(
                    plugin_instance,
                    runtime_facade.get_event_dispatcher() # type: ignore[attr-defined]
                )
        self._logger.debug("All plugin interfaces registered.")


    def _register_plugin_services_and_events(
        self,
        plugin_instance: PluginBase,
        registration_name: str, # Name under which plugin is known in *this* app's scope
        service_dispatcher: Any, # Actual dispatcher instances
        event_dispatcher: Any
    ) -> None:
        """
        Registers services and event listeners for a single plugin instance.
        (Full docstring in Tranche 4 Prompt)
        """
        instance_id = plugin_instance._plugginger_instance_id
        self._logger.debug(f"Registering services/events for plugin instance '{instance_id}' (registered as '{registration_name}').")

        for member_name in dir(plugin_instance):
            if member_name.startswith("_"): continue
            member_obj = getattr(plugin_instance, member_name)
            if not callable(member_obj): continue

            # Register services
            service_meta = getattr(member_obj, SERVICE_METADATA_KEY, None)
            if service_meta and service_meta.get("is_service"):
                custom_name_suffix = service_meta.get("custom_name")
                service_local_name = custom_name_suffix or member_name

                fully_qualified_service_name: str
                if ":" in service_local_name: # Assumed pre-qualified global name
                    fully_qualified_service_name = service_local_name
                else: # Prefix with registration_name (plugin's name in this app scope)
                    fully_qualified_service_name = f"{registration_name}.{service_local_name}"
                try:
                    service_dispatcher.add_service(fully_qualified_service_name, member_obj)
                except ServiceNameConflictError as e:
                    self._logger.error(f"Service name conflict for '{fully_qualified_service_name}' from plugin '{instance_id}'.")
                    raise ServiceNameConflictError(
                        f"Service '{fully_qualified_service_name}' (from plugin instance '{instance_id}', method '{member_name}') "
                        f"conflicts with an existing service."
                    ) from e

            # Register event listeners (for events within *this* app instance)
            event_meta = getattr(member_obj, EVENT_METADATA_KEY, None)
            if event_meta and "event_patterns" in event_meta:
                patterns_tuple: Tuple[str, ...] = event_meta["event_patterns"]
                for pattern_str in patterns_tuple:
                    event_dispatcher.add_listener(pattern_str, member_obj)
                    self._logger.debug(f"  Registered event listener for pattern '{pattern_str}' "
                                       f"from plugin '{instance_id}' (handler: {member_name}).")


    def _finalize_app_plugin_event_bridges(
        self,
        app_plugin_instance: AppPluginBase,
        outer_event_dispatcher: Any # EventDispatcher of the app being built
    ) -> None:
        """
        Sets up event bridges for an AppPlugin instance after it and its internal
        app have been initialized.
        (Full docstring in Tranche 4 Prompt)
        """
        # TODO: Implement AppPlugin event bridges in future version
        # For now, this is a placeholder to avoid mypy errors
        self._logger.debug(f"AppPlugin event bridges not yet implemented for '{app_plugin_instance._plugginger_instance_id}'")