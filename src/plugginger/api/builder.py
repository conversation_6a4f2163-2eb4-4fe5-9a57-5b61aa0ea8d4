# src/plugginger/api/builder_new.py

"""
Application builder API using dependency injection.

This module provides the PluggingerAppBuilder class for constructing and
configuring Plugginger applications with comprehensive dependency management,
validation, and fractal composition support.
"""


import logging
from typing import TYPE_CHECKING, Any

from pydantic import BaseModel, ValidationError

from plugginger._internal.graph import DependencyGraph
from plugginger._internal.runtime_facade import RuntimeFacade
from plugginger._internal.validation import DependencyValidator
from plugginger.api.app import PluggingerAppInstance
from plugginger.api.depends import Depends
from plugginger.api.plugin import PluginBase, is_plugin_class
from plugginger.config.models import ExecutorConfig, GlobalAppConfig
from plugginger.core.config import DEFAULT_APP_NAME
from plugginger.core.exceptions import (
AppPluginError,
ConfigurationError,
PluginRegistrationError,
)
from plugginger.utils.generic_proxy import GenericPluginProxy

if TYPE_CHECKING:
    from plugginger.api.app_plugin import AppPluginBase


class PluggingerAppBuilder:
    """
Builder for constructing Plugginger applications with dependency injection.


This class provides comprehensive application building functionality including
dependency graph management, validation, plugin instantiation with DI,
and fractal composition support for nested applications.

Attributes:
    _app_name: Name of the application being built
    _parent_app_plugin_context: Parent AppPlugin context for fractal composition
    _current_depth: Current nesting depth for fractal composition
    _max_fractal_depth: Maximum allowed nesting depth
    _registered_item_classes: Mapping of registration names to plugin classes
    _plugin_dependency_declarations: Mapping of plugin names to their dependencies
    _logger: Logger instance for build process logging
"""

def __init__(
    self,
    app_name: str = DEFAULT_APP_NAME,
    parent_app_plugin_context: AppPluginBase | None = None,
    _current_depth: int = 0,
    _max_depth_from_config: int | None = None,
) -> None:
    """
    Initialize the application builder.

    Args:
        app_name: Name of the application to build
        parent_app_plugin_context: Parent AppPlugin for fractal composition
        _current_depth: Current nesting depth (internal use)
        _max_depth_from_config: Maximum depth override (internal use)
    """
    self._app_name = app_name
    self._parent_app_plugin_context = parent_app_plugin_context
    self._current_depth = _current_depth
    self._max_fractal_depth = _max_depth_from_config or 3  # Default from GlobalAppConfig

    # Plugin registration storage
    self._registered_item_classes: dict[str, type[PluginBase] | type[AppPluginBase]] = {}
    self._plugin_dependency_declarations: dict[str, list[Depends]] = {}

    # Logger for build process
    self._logger = logging.getLogger("plugginger.builder")

def _get_plugin_metadata_attr(self, plugin_class: type[PluginBase], attr_name: str) -> Any:
    """
    Get a metadata attribute from a plugin class.

    Args:
        plugin_class: Plugin class to inspect
        attr_name: Name of the metadata attribute

    Returns:
        The metadata attribute value

    Raises:
        PluginRegistrationError: If metadata attribute is missing
    """
    if not hasattr(plugin_class, attr_name):
        raise PluginRegistrationError(
            f"Plugin class '{plugin_class.__name__}' is missing required metadata attribute '{attr_name}'. "
            f"Ensure the class is decorated with @plugin."
        )
    return getattr(plugin_class, attr_name)

def _register_item(self, registration_name: str, item_class: type[PluginBase]) -> None:
    """
    Register a plugin or app-plugin class.

    Args:
        registration_name: Name to register the item under
        item_class: Plugin class to register

    Raises:
        PluginRegistrationError: If registration fails
    """
    # Check for duplicate registration
    if registration_name in self._registered_item_classes:
        raise PluginRegistrationError(
            f"Plugin with name '{registration_name}' is already registered"
        )

    # Store the class
    self._registered_item_classes[registration_name] = item_class

    # Extract and store dependency declarations
    needs_attr = getattr(item_class, 'needs', [])
    if not isinstance(needs_attr, list):
        raise PluginRegistrationError(
            f"Plugin '{registration_name}' has invalid 'needs' attribute. Must be a list of Depends objects."
        )

    self._plugin_dependency_declarations[registration_name] = needs_attr.copy()

    self._logger.debug(f"Registered plugin '{registration_name}' with {len(needs_attr)} dependencies")

def include(self, plugin_class: type[PluginBase]) -> PluggingerAppBuilder:
    """
    Include a plugin class in the application.

    Args:
        plugin_class: Plugin class to include

    Returns:
        Self for method chaining

    Raises:
        PluginRegistrationError: If plugin registration fails
    """
    # Validate plugin class
    if not is_plugin_class(plugin_class):
        raise PluginRegistrationError(
            f"Class '{plugin_class.__name__}' is not a valid plugin class. "
            f"Make sure it inherits from PluginBase and has the @plugin decorator."
        )

    # Get plugin metadata
    plugin_name = self._get_plugin_metadata_attr(plugin_class, '_plugginger_plugin_name')

    # Register the plugin
    self._register_item(plugin_name, plugin_class)

    return self

def include_app(self, app_plugin_class: type[AppPluginBase]) -> PluggingerAppBuilder:
    """
    Include an AppPlugin class for fractal composition.

    Args:
        app_plugin_class: AppPlugin class to include

    Returns:
        Self for method chaining

    Raises:
        AppPluginError: If fractal depth limit is exceeded
        PluginRegistrationError: If plugin registration fails
    """
    # Check fractal depth limit
    if self._current_depth + 1 > self._max_fractal_depth:
        raise AppPluginError(
            f"Maximum fractal depth ({self._max_fractal_depth}) exceeded. "
            f"Cannot include AppPlugin at depth {self._current_depth + 1}"
        )

    # Validate and register as a regular plugin
    if not is_plugin_class(app_plugin_class):
        raise PluginRegistrationError(
            f"Class '{app_plugin_class.__name__}' is not a valid plugin class. "
            f"Make sure it inherits from AppPluginBase and has the @plugin decorator."
        )

    # Get plugin metadata
    plugin_name = self._get_plugin_metadata_attr(app_plugin_class, '_plugginger_plugin_name')

    # Register the app plugin
    self._register_item(plugin_name, app_plugin_class)

    return self

def _generate_plugin_instance_id(self, registration_name: str) -> str:
    """
    Generate a hierarchical instance ID for a plugin.

    Args:
        registration_name: Registration name of the plugin

    Returns:
        Hierarchical instance ID
    """
    if self._parent_app_plugin_context is not None:
        parent_id = self._parent_app_plugin_context._plugginger_instance_id
        return f"{parent_id}.{registration_name}"
    else:
        return registration_name

def build(
    self,
    app_config_input: GlobalAppConfig | dict[str, Any] | None = None,
) -> PluggingerAppInstance:
    """
    Build the final application instance.

    This method performs the complete build process including dependency
    validation, plugin instantiation with DI, service/event registration,
    and runtime facade setup.

    Args:
        app_config_input: Global application configuration

    Returns:
        Configured and ready-to-use application instance

    Raises:
        ConfigurationError: If configuration validation fails
        DependencyError: If dependency validation fails
        PluginRegistrationError: If plugin instantiation fails
    """
    self._logger.info(f"Building application '{self._app_name}'...")

    # 1. Global Configuration Validation
    global_config = self._validate_and_create_global_config(app_config_input)
    self._max_fractal_depth = global_config.max_fractal_depth

    # 2. Create Runtime Components
    runtime_facade = self._create_runtime_facade(global_config)

    # 3. Build and Validate Dependency Graph
    dependency_graph = self._build_dependency_graph()
    sorted_registration_names = self._validate_dependencies(dependency_graph)

    # 4. Create Provisional App Instance
    provisional_app_instance = self._create_provisional_app_instance(
        runtime_facade, global_config
    )

    # 5. Instantiate Plugins with Dependency Injection
    plugin_instances_map, plugin_configs_for_setup = self._instantiate_plugins_with_di(
        sorted_registration_names, provisional_app_instance, global_config
    )

    # 6. Register Services and Events
    self._register_services_and_events(
        sorted_registration_names, plugin_instances_map, runtime_facade
    )

    # 7. Setup AppPlugin Event Bridges
    self._finalize_app_plugin_event_bridges(plugin_instances_map, runtime_facade)

    # 8. Finalize Runtime Facade
    final_plugins_in_order = [
        plugin_instances_map[name] for name in sorted_registration_names
    ]
    runtime_facade.finalize_setup(final_plugins_in_order, plugin_configs_for_setup)

    self._logger.info(f"Application '{self._app_name}' built successfully with {len(final_plugins_in_order)} plugins")
    return provisional_app_instance

def _validate_and_create_global_config(
    self,
    app_config_input: GlobalAppConfig | dict[str, Any] | None,
) -> GlobalAppConfig:
    """
    Validate and create global configuration.

    Args:
        app_config_input: Input configuration

    Returns:
        Validated GlobalAppConfig instance

    Raises:
        ConfigurationError: If validation fails
    """
    if app_config_input is None:
        # Use default configuration
        return GlobalAppConfig(app_name=self._app_name)
    elif isinstance(app_config_input, GlobalAppConfig):
        # Already validated
        return app_config_input
    elif isinstance(app_config_input, dict):
        # Validate dict input
        try:
            config_dict = app_config_input.copy()
            config_dict.setdefault('app_name', self._app_name)
            return GlobalAppConfig(**config_dict)
        except ValidationError as e:
            raise ConfigurationError(
                f"Invalid global configuration: {e}"
            ) from e
    else:
        raise ConfigurationError(
            f"Invalid app_config_input type: {type(app_config_input)}. "
            f"Expected GlobalAppConfig, dict, or None."
        )

def _create_runtime_facade(self, global_config: GlobalAppConfig) -> RuntimeFacade:
    """
    Create and configure the runtime facade.

    Args:
        global_config: Global application configuration

    Returns:
        Configured RuntimeFacade instance
    """
    # Get default executor config
    default_executor_config = global_config.executors[0] if global_config.executors else ExecutorConfig(name="default")

    # Create logger function
    def logger_func(message: str) -> None:
        self._logger.info(message)

    return RuntimeFacade(
        fault_policy=global_config.event_listener_fault_policy,
        default_listener_timeout=global_config.default_event_listener_timeout_seconds,
        max_concurrent_listener_tasks=getattr(global_config, 'max_concurrent_listener_tasks', 50),
        default_executor_config=default_executor_config,
        logger=logger_func,
    )

def _build_dependency_graph(self) -> DependencyGraph[str]:
    """
    Build the dependency graph from registered plugins.

    Returns:
        Constructed dependency graph

    Raises:
        ConfigurationError: If dependency graph construction fails
    """
    graph = DependencyGraph[str]()

    # Add all plugins as nodes
    for registration_name in self._registered_item_classes:
        graph.add_node(registration_name)

    # Add dependency edges
    for dependent_name, depends_list in self._plugin_dependency_declarations.items():
        for dep_obj in depends_list:
            prerequisite_name = dep_obj.plugin_identifier
            graph.add_dependency_edge(prerequisite_name, dependent_name)

        return graph

    def _validate_dependencies(self, dependency_graph: DependencyGraph[str]) -> list[str]:
        """
        Validate dependencies and perform topological sort.

        Args:
            dependency_graph: Dependency graph to validate

        Returns:
            List of plugin names in dependency order

        Raises:
            DependencyError: If validation fails
            CircularDependencyError: If circular dependencies exist
        """
        # Create resolver functions for the validator
        def version_resolver(plugin_name: str) -> str:
            plugin_class = self._registered_item_classes[plugin_name]
            version = self._get_plugin_metadata_attr(plugin_class, '_plugginger_plugin_version')
            return str(version)

        def class_resolver(plugin_name: str) -> type[PluginBase]:
            plugin_class = self._registered_item_classes[plugin_name]
            return plugin_class  # type: ignore[return-value]  # AppPluginBase is also valid

        # Create and run validator
        validator = DependencyValidator(version_resolver, class_resolver)

        # Validate graph structure
        validator.validate_graph_structure(
            dependency_graph, set(self._registered_item_classes.keys())
        )

        # Perform topological sort (this will raise CircularDependencyError if cycles exist)
        sorted_names = dependency_graph.topological_sort()

        # Validate versions and signatures
        validator.validate_dependency_versions_and_signatures(
            self._plugin_dependency_declarations
        )

        return sorted_names

    def _create_provisional_app_instance(
        self,
        runtime_facade: RuntimeFacade,
        global_config: GlobalAppConfig,
    ) -> PluggingerAppInstance:
        """
        Create a provisional app instance for plugin instantiation.

        Args:
            runtime_facade: Configured runtime facade
            global_config: Global application configuration

        Returns:
            Provisional PluggingerAppInstance
        """
        # Import here to avoid circular imports
        from plugginger.api.app import PluggingerAppInstance

        return PluggingerAppInstance(
            app_name=self._app_name,
            runtime_facade=runtime_facade,
            global_config=global_config,
            parent_app_plugin_context=self._parent_app_plugin_context,
            _builder_fractal_depth=self._current_depth,
        )

    def _instantiate_plugins_with_di(
        self,
        sorted_registration_names: list[str],
        provisional_app_instance: PluggingerAppInstance,
        global_config: GlobalAppConfig,
    ) -> tuple[dict[str, PluginBase], dict[str, BaseModel]]:
        """
        Instantiate plugins with dependency injection.

        Args:
            sorted_registration_names: Plugin names in dependency order
            provisional_app_instance: Provisional app instance for injection
            global_config: Global application configuration

        Returns:
            Tuple of (plugin_instances_map, plugin_configs_for_setup)

        Raises:
            PluginRegistrationError: If plugin instantiation fails
        """
        plugin_instances_map: dict[str, PluginBase] = {}
        plugin_configs_for_setup: dict[str, BaseModel] = {}

        for registration_name in sorted_registration_names:
            plugin_class = self._registered_item_classes[registration_name]
            instance_id = self._generate_plugin_instance_id(registration_name)

            # Prepare dependency injection kwargs
            injected_deps_kwargs: dict[str, GenericPluginProxy] = {}
            depends_list = self._plugin_dependency_declarations[registration_name]

            for dep_obj in depends_list:
                dependency_plugin_name = dep_obj.plugin_identifier
                target_instance_id = self._generate_plugin_instance_id(dependency_plugin_name)

                # Create proxy for the dependency
                proxy = GenericPluginProxy(
                    app_instance=provisional_app_instance,
                    target_instance_id=target_instance_id,
                    target_registration_name=dependency_plugin_name,
                )

                # TODO: Map plugin_identifier to __init__ parameter name
                # For now, assume they are the same (simple convention)
                # In future: could support custom parameter mapping
                param_name = dependency_plugin_name
                injected_deps_kwargs[param_name] = proxy

            # Instantiate plugin
            try:
                plugin_instance = plugin_class(
                    app=provisional_app_instance,
                    **injected_deps_kwargs
                )
                plugin_instance._plugginger_instance_id = instance_id
                plugin_instances_map[registration_name] = plugin_instance

                self._logger.debug(f"Instantiated plugin '{registration_name}' with ID '{instance_id}'")

            except Exception as e:
                raise PluginRegistrationError(
                    f"Failed to instantiate plugin '{registration_name}': {e}"
                ) from e

            # Prepare plugin configuration
            plugin_config = self._prepare_plugin_config(
                plugin_class, instance_id, global_config
            )
            plugin_configs_for_setup[instance_id] = plugin_config

        return plugin_instances_map, plugin_configs_for_setup

    def _prepare_plugin_config(
        self,
        plugin_class: type[PluginBase],
        instance_id: str,
        global_config: GlobalAppConfig,
    ) -> BaseModel:
        """
        Prepare plugin-specific configuration.

        Args:
            plugin_class: Plugin class to prepare config for
            instance_id: Instance ID of the plugin
            global_config: Global application configuration

        Returns:
            Validated plugin configuration model
        """
        # Get plugin config schema
        config_schema = getattr(plugin_class, '_plugginger_config_schema', None)

        if config_schema is None:
            # No schema defined, return empty BaseModel
            class EmptyConfig(BaseModel):
                pass
            return EmptyConfig()

        # Get plugin-specific config from global config
        plugin_config_data = global_config.plugin_configs.get(instance_id, {})

        try:
            return config_schema(**plugin_config_data)
        except ValidationError as e:
            raise ConfigurationError(
                f"Invalid configuration for plugin '{instance_id}': {e}"
            ) from e

    def _register_services_and_events(
        self,
        sorted_registration_names: list[str],
        plugin_instances_map: dict[str, PluginBase],
        runtime_facade: RuntimeFacade,
    ) -> None:
        """
        Register services and events from plugins.

        Args:
            sorted_registration_names: Plugin names in dependency order
            plugin_instances_map: Map of plugin instances
            runtime_facade: Runtime facade for registration
        """
        for registration_name in sorted_registration_names:
            plugin_instance = plugin_instances_map[registration_name]
            instance_id = plugin_instance._plugginger_instance_id

            # Register services and events
            self._register_plugin_services_and_events(
                plugin_instance, registration_name, instance_id, runtime_facade
            )

    def _register_plugin_services_and_events(
        self,
        plugin_instance: PluginBase,
        registration_name: str,
        instance_id: str,
        runtime_facade: RuntimeFacade,
    ) -> None:
        """
        Register services and events for a single plugin.

        Args:
            plugin_instance: Plugin instance to register
            registration_name: Registration name of the plugin
            instance_id: Instance ID of the plugin
            runtime_facade: Runtime facade for registration
        """
        # This is a placeholder - in a full implementation, this would:
        # 1. Inspect the plugin for @service decorated methods
        # 2. Register them with the service dispatcher
        # 3. Inspect the plugin for @on_event decorated methods
        # 4. Register them with the event dispatcher

        # For now, just log that we would register services/events
        self._logger.debug(f"Would register services and events for plugin '{registration_name}'")

    def _finalize_app_plugin_event_bridges(
        self,
        plugin_instances_map: dict[str, PluginBase],
        runtime_facade: RuntimeFacade,
    ) -> None:
        """
        Finalize event bridges for AppPlugin instances.

        Args:
            plugin_instances_map: Map of plugin instances
            runtime_facade: Runtime facade for event bridge setup
        """
        # This is a placeholder - in a full implementation, this would:
        # 1. Iterate over plugin instances
        # 2. Check if they are AppPluginBase instances
        # 3. Process their event bridge configurations
        # 4. Set up event forwarding between parent and child apps

        # For now, just log that we would set up event bridges
        app_plugin_count = 0
        for plugin_instance in plugin_instances_map.values():
            # Check if it's an AppPlugin (simplified check)
            if hasattr(plugin_instance, '_internal_app'):
                app_plugin_count += 1

        if app_plugin_count > 0:
            self._logger.debug(f"Would set up event bridges for {app_plugin_count} AppPlugin instances")

