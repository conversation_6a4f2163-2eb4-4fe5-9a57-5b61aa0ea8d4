# src/plugginger/api/__init__.py

"""
Public API for the Plugginger framework.

This module provides the main public interface that users interact with
when building applications with the Plugginger framework.
"""

from plugginger.api.app import PluggingerAppInstance
from plugginger.api.app_plugin import AppPluginBase
from plugginger.api.background import background_task
from plugginger.api.depends import Depends
from plugginger.api.events import on_event
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.api.simple_builder import PluggingerAppBuilder

__all__ = [
    "PluggingerAppInstance",
    "PluggingerAppBuilder",
    "PluginBase",
    "AppPluginBase",
    "plugin",
    "service",
    "on_event",
    "Depends",
    "background_task",
]
