# src/plugginger/api/__init__.py

"""
Public API for the Plugginger framework.

This module provides the main public interface that users interact with
when building applications with the Plugginger framework.
"""

# Import order matters to avoid circular imports
from plugginger.api.depends import Depends
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.api.events import on_event
from plugginger.api.background import background_task
from plugginger.api.app_plugin import AppPluginBase
from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.app import PluggingerAppInstance

__all__ = [
    "PluggingerAppInstance",
    "PluggingerAppBuilder",
    "PluginBase",
    "AppPluginBase",
    "plugin",
    "service",
    "on_event",
    "Depends",
    "background_task",
]
