# src/plugginger/api/app.py

"""
Defines the `PluggingerAppInstance` class, representing a running Plugginger application.
This class provides the main user-facing API for interacting with the application
once it has been configured and built by `PluggingerAppBuilder`.
"""

from __future__ import annotations

import asyncio
import logging # Using standard Python logging
from typing import Any, Iterable, Callable, Awaitable, Optional, Dict, Type, Union, cast, List
from pydantic import BaseModel # For type hint of plugin_configs_for_setup
from concurrent.futures import Executor # For type hinting get_executor

# Internal facade that bundles runtime components
from plugginger._internal.runtime_facade import RuntimeFacade
# Configuration models
from plugginger.api.config_schemas import GlobalAppConfig, ExecutorConfig, EventListenerFaultPolicy
# Base exception for error handling consistency
from plugginger.api.exceptions import PluggingerError, PluginTeardownError, BackgroundTaskError

# Forward references for type hints to avoid circular imports at runtime
if False: # TYPE_CHECKING block pragma: no cover
    from plugginger.api.app_plugin import AppPluginBase
    from plugginger.api.plugin import PluginBase
    from plugginger._internal.runtime_facade import ServiceDispatcher, EventDispatcher, ExecutorRegistry


# Standard Python logger for the app instance module
# The actual log level will be configured based on GlobalAppConfig.log_level
app_instance_logger_base: logging.Logger = logging.getLogger("plugginger.app")


class PluggingerAppInstance:
    """
    Represents a running Plugginger application with its configured plugins.

    This class is typically created by `PluggingerAppBuilder.build()` and
    should not be instantiated directly by users in most cases.

    It provides methods to manage the application lifecycle (start, stop),
    call services provided by plugins, emit events, and access shared resources
    like named thread pool executors.
    """

    app_name: str
    """The conceptual name of this application instance, derived from GlobalAppConfig."""

    instance_id: str
    """
    A globally unique identifier for this specific app instance.
    For a top-level application, this might be the same as `app_name` or prefixed.
    For sub-applications (AppPlugins), this ID will be namespaced to reflect
    its position in the fractal hierarchy (e.g., "outer_app_reg_name:sub_app_reg_name").
    """

    _runtime_facade: RuntimeFacade
    _logger: logging.Logger # Using standard logging.Logger
    _is_running_or_starting: bool # True if start_all_plugins has been called and not yet stopped
    _global_config: GlobalAppConfig # Store the resolved global config
    _parent_app_plugin_context: Optional[AppPluginBase] # Ref to AppPluginBase if this is a sub-app

    # For AppPlugins building sub-apps, to pass down fractal depth info
    # These are set by the builder that creates this AppInstance.
    _current_build_depth_for_sub_apps: int
    _max_build_depth_for_sub_apps: int


    def __init__(
        self,
        app_name: str,
        instance_id: str,
        # These components are now created *by the builder* and passed to RuntimeFacade constructor,
        # which is then passed here.
        # plugins_in_order: Iterable[PluginBase], # Now passed to RuntimeFacade.finalize_setup
        # service_dispatcher: ServiceDispatcher,
        # event_dispatcher: EventDispatcher,
        # executor_registry: ExecutorRegistry,
        runtime_facade: RuntimeFacade, # Builder creates and passes this
        global_config: GlobalAppConfig,
        logger: Optional[logging.Logger] = None,
        parent_app_plugin_context: Optional[AppPluginBase] = None,
        _builder_fractal_depth: int = 0, # The depth at which *this* app instance was built
    ):
        """
        Initializes a new `PluggingerAppInstance`.
        This constructor is primarily intended for use by `PluggingerAppBuilder`.
        """
        self.app_name = global_config.app_name # Take from validated config
        self.instance_id = instance_id
        self._global_config = global_config
        self._parent_app_plugin_context = parent_app_plugin_context
        self._is_running_or_starting = False # Will be true after start_all_plugins begins

        # These are for when *this* app instance's AppPlugins build *their* sub-apps
        self._current_build_depth_for_sub_apps = _builder_fractal_depth
        self._max_build_depth_for_sub_apps = global_config.max_fractal_depth

        if logger:
            self._logger = logger
        else:
            self._logger = logging.getLogger(f"plugginger.app.{self.instance_id}")
            if not self._logger.handlers: # Avoid adding multiple handlers if already configured
                 _handler = logging.StreamHandler()
                 _formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                 _handler.setFormatter(_formatter)
                 self._logger.addHandler(_handler)
            try:
                self._logger.setLevel(self._global_config.log_level.upper()) # Pydantic enum gives value
            except ValueError: # Invalid log level string
                self._logger.setLevel(logging.INFO) # Default
                self._logger.error(f"Invalid log_level '{self._global_config.log_level}' in GlobalAppConfig. Defaulting to INFO.")

        self._runtime_facade = runtime_facade
        
        self._logger.info(f"PluggingerAppInstance '{self.instance_id}' (for app '{self.app_name}') "
                          f"initialized. Depth: {_builder_fractal_depth}. Max Sub-App Depth: {self._max_build_depth_for_sub_apps}")

    async def run(
        self,
        main_coroutine: Optional[Callable[[], Awaitable[Any]]] = None,
    ) -> Any:
        """
        Starts all plugins, runs an optional main coroutine, and then stops all plugins.
        Handles graceful shutdown on KeyboardInterrupt or asyncio.CancelledError.
        (Full docstring in Tranche 4 Prompt)
        """
        if self._is_running_or_starting:
            self._logger.warning(f"AppInstance.run() called on '{self.instance_id}', but it appears to be already running or starting.")
            return None

        self._logger.info(f"PluggingerAppInstance '{self.instance_id}' starting execution...")
        self._is_running_or_starting = True # Set flag early
        
        result: Any = None
        try:
            await self.start_all_plugins() # Uses configs from build time

            if main_coroutine is not None:
                self._logger.info(f"AppInstance '{self.instance_id}' executing main coroutine.")
                result = await main_coroutine()
                self._logger.info(f"AppInstance '{self.instance_id}' main coroutine completed.")
            else:
                self._logger.info(f"AppInstance '{self.instance_id}' running indefinitely (no main coroutine). "
                                  "Awaiting external shutdown signal or explicit stop.")
                shutdown_event = asyncio.Event()
                # TODO: Need a way for external signals or app.stop() to set this event.
                # For now, it will run until CancelledError.
                await shutdown_event.wait()
        
        except asyncio.CancelledError:
            self._logger.info(f"PluggingerAppInstance '{self.instance_id}' run sequence was cancelled.")
            raise 
        except Exception as e: 
            self._logger.error(f"Unhandled exception in main coroutine or startup of AppInstance '{self.instance_id}': {e!r}", exc_info=True)
            raise 
        finally:
            self._logger.info(f"PluggingerAppInstance '{self.instance_id}' initiating final shutdown sequence.")
            await self.stop_all_plugins() 
            # _is_running_or_starting is reset by stop_all_plugins if successful
            self._logger.info(f"PluggingerAppInstance '{self.instance_id}' has shut down.")
        return result

    async def start_all_plugins(self) -> None:
        """
        Calls the `setup` method on all registered plugins in their topological order.
        (Full docstring in Tranche 4 Prompt)
        """
        if self._runtime_facade.were_plugins_started():
             self._logger.warning(f"AppInstance '{self.instance_id}': start_all_plugins called again while plugins are already started. Ignoring.")
             return

        self._logger.info(f"AppInstance '{self.instance_id}' preparing to start all plugins.")
        self._is_running_or_starting = True # Mark that startup process has begun
        await self._runtime_facade.setup_all_plugins()
        self._logger.info(f"AppInstance '{self.instance_id}' all plugins successfully started.")

    async def stop_all_plugins(self, timeout: Optional[float] = 30.0) -> None:
        """
        Calls the `teardown()` method on all plugins and shuts down executors.
        (Full docstring in Tranche 4 Prompt, timeout is conceptual for MVP)
        """
        if not self._runtime_facade.were_plugins_started() and not self._is_running_or_starting:
             self._logger.info(f"AppInstance '{self.instance_id}': stop_all_plugins called, but plugins were not started or already stopped. Teardown skipped.")
             return

        self._logger.info(f"AppInstance '{self.instance_id}' initiating plugin teardown (timeout: {timeout}s).")
        try:
            teardown_task = self._runtime_facade.teardown_all_plugins()
            if timeout is not None:
                try:
                    await asyncio.wait_for(teardown_task, timeout=timeout)
                except asyncio.TimeoutError:
                    self._logger.error(f"AppInstance '{self.instance_id}' plugin teardown timed out after {timeout}s. "
                                       "Some plugins may not have completed teardown gracefully.")
                    # Continue to executor shutdown
            else: # No timeout
                await teardown_task
        except PluginTeardownError as e: 
            self._logger.error(f"AppInstance '{self.instance_id}' encountered errors during plugin teardown: {e.individual_errors}")
            raise # Re-raise to signal overall teardown failure
        except Exception as e: # Catch any other unexpected error during teardown phase
            self._logger.error(f"Unexpected error during plugin teardown phase for '{self.instance_id}': {e!r}", exc_info=True)
            # Potentially wrap and re-raise
            raise PluggingerError(f"Unexpected teardown error for '{self.instance_id}'") from e
        finally:
            self._logger.info(f"AppInstance '{self.instance_id}' proceeding to executor shutdown.")
            self._runtime_facade.shutdown_executors(wait=True) # Wait for executors to finish
            self._is_running_or_starting = False # Mark as definitively not running after teardown attempt
        self._logger.info(f"AppInstance '{self.instance_id}' plugin teardown and executor shutdown completed.")


    async def call_service(self, service_name: str, *args: Any, **kwargs: Any) -> Any:
        """Calls a registered service by its fully qualified name."""
        self._logger.debug(f"AppInstance '{self.instance_id}' dispatching call to service: {service_name}")
        return await self._runtime_facade.call_service(service_name, *args, **kwargs)

    async def emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Emits an event to all registered listeners matching the event type or pattern."""
        self._logger.debug(f"AppInstance '{self.instance_id}' dispatching emit for event: {event_type}")
        await self._runtime_facade.emit_event(event_type, data)

    def get_executor(self, name: str = "default") -> Executor:
        """Retrieves a named `concurrent.futures.Executor` instance."""
        return self._runtime_facade.get_executor(name)

    def register_executor(self, name: str, executor_or_config: Union[ExecutorConfig, Executor]) -> None:
        """Registers a custom executor instance or configuration."""
        self._runtime_facade.register_executor(name, executor_or_config)
        self._logger.info(f"AppInstance '{self.instance_id}': Executor '{name}' registered/updated.")

    def get_plugin_instance(self, plugin_instance_id: str) -> Optional[PluginBase]:
        """Retrieves an active plugin instance by its unique `instance_id`."""
        if not self._runtime_facade.were_plugins_started():
            self._logger.warning(
                f"Attempted to get plugin instance '{plugin_instance_id}' from app '{self.instance_id}' "
                "but plugins are not (yet) fully started."
            )
            return None
        plugin = self._runtime_facade.get_plugin_by_id(plugin_instance_id)
        if plugin is None:
            self._logger.debug(f"Plugin instance '{plugin_instance_id}' not found in app '{self.instance_id}'.")
        return plugin
    
    def get_runtime_facade(self) -> RuntimeFacade:
        """
        INTERNAL USE: Gets the RuntimeFacade.
        Needed by builder for certain operations like AppPlugin event bridge setup.
        """
        return self._runtime_facade

    @property
    def is_running(self) -> bool:
        """Indicates if the application's plugins have been started and not yet stopped."""
        return self._is_running_or_starting and self._runtime_facade.were_plugins_started()


    def __repr__(self) -> str:
        """Provides a string representation of the app instance."""
        status = "running" if self.is_running else "stopped"
        plugin_count = self._runtime_facade.get_plugin_count()
        return (
            f"<PluggingerAppInstance(app_name='{self.app_name}', id='{self.instance_id}', "
            f"plugins={plugin_count}, status='{status}')>"
        )