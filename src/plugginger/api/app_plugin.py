# src/plugginger/api/app_plugin.py

"""
AppPlugin base class for fractal application composition.

This module provides the AppPluginBase class that enables creating plugins
that encapsulate entire PluggingerAppInstance sub-applications with comprehensive
event bridging and hierarchical lifecycle management.
"""

from __future__ import annotations

import asyncio
import logging
from abc import ABC, abstractmethod
from collections.abc import Callable
from typing import TYPE_CHECKING, Any, Optional

from pydantic import BaseModel

from plugginger.api.plugin import PluginBase
from plugginger.core.exceptions import AppPluginError
from plugginger.core.types import EventBridgeConfigEntryType, EventBridgeListType

if TYPE_CHECKING:
    from plugginger.api.app import PluggingerAppInstance


class AppPluginBase(PluginBase, ABC):
    """
    Base class for creating App-Plugins that enable fractal application composition.

    An App-Plugin encapsulates a complete PluggingerAppInstance (sub-app) and exposes
    its functionality to an outer PluggingerAppInstance as if it were a regular plugin.
    This enables building complex, hierarchical systems from smaller, reusable modules
    with comprehensive event bridging capabilities.

    Subclasses must:
    1. Inherit from AppPluginBase
    2. Be decorated with @plugin(...) to define metadata for the outer app
    3. Implement _configure_internal_app() to set up the internal sub-app
    4. Optionally override _configure_event_bridges() for event communication between apps

    Attributes:
        _internal_app: The internal PluggingerAppInstance managed by this AppPlugin
        _event_bridges_config: Configuration for event bridges between apps
        _logger: Logger instance for this AppPlugin
    """

    def __init__(self, app: "PluggingerAppInstance", **injected_outer_dependencies: Any) -> None:
        """
        Initialize the AppPlugin instance.

        Args:
            app: The outer PluggingerAppInstance this AppPlugin belongs to
            **injected_outer_dependencies: Dependencies injected by the DI container
        """
        # Call parent constructor
        super().__init__(app, **injected_outer_dependencies)

        # Initialize event bridge configuration
        self._event_bridges_config: EventBridgeListType = []

        # Initialize internal app (will be set by _configure_internal_app)
        self._internal_app: Optional["PluggingerAppInstance"] = None

        # Setup logger
        self._logger = logging.getLogger(f"plugginger.app_plugin.{self.__class__.__name__}")

        # Configure the internal app (must be implemented by subclasses)
        self._configure_internal_app()

        # Validate that internal app was configured
        if self._internal_app is None:
            raise AppPluginError(
                f"AppPlugin '{self.__class__.__name__}' failed to configure internal app. "
                f"_configure_internal_app() must set self._internal_app"
            )

        # Configure event bridges (can be overridden by subclasses)
        self._configure_event_bridges()

        # Log successful initialization
        self._logger.info(
            f"AppPlugin '{self.__class__.__name__}' initialized successfully with "
            f"internal app '{self._internal_app.app_name}' and "
            f"{len(self._event_bridges_config)} event bridges"
        )

    @abstractmethod
    def _configure_internal_app(self) -> None:
        """
        Configure and build the internal sub-application.

        This method must be implemented by subclasses to set up their internal
        PluggingerAppInstance. The implementation must:

        1. Create a PluggingerAppBuilder with correct fractal depth parameters:
           - Use self.app._current_build_depth_for_sub_apps as _current_depth
           - Use self.app._max_build_depth_for_sub_apps as _max_depth_from_config
           - Set parent_app_plugin_context=self

        2. Configure plugins for the sub-app using builder.include() and builder.include_app()

        3. Build the app and assign it to self._internal_app

        Example:
            ```python
            def _configure_internal_app(self) -> None:
                from plugginger.api.builder import PluggingerAppBuilder

                builder = PluggingerAppBuilder(
                    app_name=f"{self._plugginger_instance_id}_internal",
                    parent_app_plugin_context=self,
                    _current_depth=self.app._current_build_depth_for_sub_apps + 1,
                    _max_depth_from_config=self.app._max_build_depth_for_sub_apps
                )

                builder.include(MyInternalPlugin)
                self._internal_app = builder.build()
            ```

        Note: This method is called during AppPlugin initialization, not during setup().
        """
        ...

    def _configure_event_bridges(self) -> None:
        """
        Configure event bridges between outer and internal apps.

        Override this method to establish event communication channels between
        the outer application and the internal sub-application. Use the provided
        bridge_* methods to configure event forwarding.

        This method is called after _configure_internal_app() during initialization.
        The default implementation does nothing (no event bridges).

        Example:
            ```python
            def _configure_event_bridges(self) -> None:
                # Forward internal events to outer app
                self.bridge_internal_event_pattern(
                    "user.*",
                    as_external_event_prefix="user_mgmt"
                )

                # Forward external events to internal app
                self.bridge_external_event_to_internal(
                    "system.shutdown",
                    to_internal_event_type="internal.shutdown"
                )
            ```
        """
        pass

    async def setup(self, plugin_specific_config: BaseModel) -> None:
        """
        Set up the AppPlugin and start its internal application.

        This method extracts configuration for the internal app and starts all
        plugins within the internal app. The internal app and event bridges
        are already configured during __init__.

        Args:
            plugin_specific_config: Configuration for this AppPlugin (Pydantic model)
        """
        self._logger.info(f"Starting setup for AppPlugin '{self.__class__.__name__}'")

        try:
            # Extract internal app configuration from plugin config
            internal_app_config_values = {}
            if hasattr(plugin_specific_config, 'internal_app_config'):
                internal_app_config_values = plugin_specific_config.internal_app_config
            elif hasattr(plugin_specific_config, 'model_dump'):
                # If the entire config is for the internal app
                config_dict = plugin_specific_config.model_dump()
                internal_app_config_values = config_dict.get('internal_app_config', {})

            # Start all plugins in the internal app
            if self._internal_app is not None:
                await self._internal_app.start_all_plugins()
                self._logger.info(
                    f"AppPlugin '{self.__class__.__name__}' setup completed - "
                    f"internal app '{self._internal_app.app_name}' started"
                )

        except Exception as e:
            self._logger.error(f"Failed to set up AppPlugin '{self.__class__.__name__}': {e}")
            raise AppPluginError(
                f"Failed to set up AppPlugin '{self.__class__.__name__}': {e}"
            ) from e

    async def teardown(self) -> None:
        """
        Tear down the AppPlugin and stop its internal application.

        This method stops all plugins in the internal app and cleans up resources.
        """
        self._logger.info(f"Starting teardown for AppPlugin '{self.__class__.__name__}'")

        try:
            if self._internal_app is not None:
                await self._internal_app.stop_all_plugins()
                self._logger.info(
                    f"AppPlugin '{self.__class__.__name__}' teardown completed - "
                    f"internal app '{self._internal_app.app_name}' stopped"
                )

        except Exception as e:
            self._logger.error(f"Failed to tear down AppPlugin '{self.__class__.__name__}': {e}")
            raise AppPluginError(
                f"Failed to tear down AppPlugin '{self.__class__.__name__}': {e}"
            ) from e

    # Event-Bridging API Methods (for subclasses in _configure_event_bridges)

    def bridge_internal_event_pattern(
        self,
        internal_event_pattern: str,
        as_external_event_prefix: Optional[str] = None,
        data_transformer: Optional[Callable[[dict[str, Any], str], dict[str, Any]]] = None
    ) -> None:
        """
        Configure bridging of internal events to the outer app.

        Events matching the internal pattern will be forwarded to the outer app
        with the specified external prefix.

        Args:
            internal_event_pattern: Pattern to match internal events (supports wildcards)
            as_external_event_prefix: Prefix for external events (default: instance_id)
            data_transformer: Optional function to transform event data

        Example:
            ```python
            # Forward all "user.*" events as "user_mgmt.*" in outer app
            self.bridge_internal_event_pattern("user.*", "user_mgmt")
            ```
        """
        # Validate parameters
        if not internal_event_pattern or not isinstance(internal_event_pattern, str):
            raise ValueError("internal_event_pattern must be a non-empty string")

        # Determine final external prefix
        final_external_prefix = as_external_event_prefix or getattr(self, '_plugginger_instance_id', 'unknown')

        # Store bridge configuration
        bridge_config: EventBridgeConfigEntryType = {
            "direction": "internal_to_external",
            "internal_pattern": internal_event_pattern,
            "external_prefix": final_external_prefix,
            "transformer": data_transformer
        }
        self._event_bridges_config.append(bridge_config)

        self._logger.debug(
            f"Configured internal->external bridge: {internal_event_pattern} -> {final_external_prefix}.*"
        )

    def bridge_external_event_to_internal(
        self,
        external_event_pattern: str,
        to_internal_event_type: str,
        data_transformer: Optional[Callable[[dict[str, Any], str], dict[str, Any]]] = None
    ) -> None:
        """
        Configure bridging of external events to the internal app.

        Events matching the external pattern will be forwarded to the internal app
        with the specified internal event type.

        Args:
            external_event_pattern: Pattern to match external events (supports wildcards)
            to_internal_event_type: Event type to emit in internal app
            data_transformer: Optional function to transform event data

        Example:
            ```python
            # Forward "system.shutdown" events as "internal.shutdown" in internal app
            self.bridge_external_event_to_internal("system.shutdown", "internal.shutdown")
            ```
        """
        # Validate parameters
        if not external_event_pattern or not isinstance(external_event_pattern, str):
            raise ValueError("external_event_pattern must be a non-empty string")
        if not to_internal_event_type or not isinstance(to_internal_event_type, str):
            raise ValueError("to_internal_event_type must be a non-empty string")

        # Store bridge configuration
        bridge_config: EventBridgeConfigEntryType = {
            "direction": "external_to_internal",
            "external_pattern": external_event_pattern,
            "to_internal_type": to_internal_event_type,
            "transformer": data_transformer
        }
        self._event_bridges_config.append(bridge_config)

        self._logger.debug(
            f"Configured external->internal bridge: {external_event_pattern} -> {to_internal_event_type}"
        )

    # Runtime Event-Forwarding Methods (INTERNAL - called by dispatchers/builders)

    def _execute_internal_to_external_bridge(
        self,
        matched_internal_event_type: str,
        internal_event_data: dict[str, Any],
        bridge_config_entry: EventBridgeConfigEntryType
    ) -> None:
        """
        Execute an internal-to-external event bridge.

        This method is called by the internal app's event dispatcher when an event
        matches a configured internal->external bridge pattern.

        Args:
            matched_internal_event_type: The actual internal event type that was matched
            internal_event_data: The event data from the internal app
            bridge_config_entry: The bridge configuration that matched
        """
        try:
            # Extract bridge configuration
            internal_pattern = bridge_config_entry["internal_pattern"]
            external_prefix = bridge_config_entry["external_prefix"]
            transformer = bridge_config_entry.get("transformer")

            # Transform event data if transformer is provided
            final_data_for_external_event = internal_event_data
            if transformer is not None:
                final_data_for_external_event = transformer(internal_event_data, matched_internal_event_type)

            # Construct external event type by extracting suffix from matched event
            # Example: pattern="user.*", matched="user.login.success" -> suffix="login.success"
            if internal_pattern.endswith("*"):
                pattern_prefix = internal_pattern[:-1]  # Remove the "*"
                if matched_internal_event_type.startswith(pattern_prefix):
                    suffix = matched_internal_event_type[len(pattern_prefix):]
                    external_event_type = f"{external_prefix}.{suffix}" if suffix else external_prefix
                else:
                    # Fallback if pattern doesn't match as expected
                    external_event_type = f"{external_prefix}.{matched_internal_event_type}"
            else:
                # Exact match pattern
                external_event_type = f"{external_prefix}.{matched_internal_event_type}"

            # Emit event in outer app (fire-and-forget)
            asyncio.create_task(
                self.app.emit_event(external_event_type, final_data_for_external_event)
            )

            self._logger.debug(
                f"Forwarded internal event '{matched_internal_event_type}' -> "
                f"external event '{external_event_type}'"
            )

        except Exception as e:
            self._logger.error(
                f"Failed to execute internal->external bridge for event '{matched_internal_event_type}': {e}"
            )

    async def _execute_external_to_internal_bridge(
        self,
        matched_external_event_type: str,
        external_event_data: dict[str, Any],
        bridge_config_entry: EventBridgeConfigEntryType
    ) -> None:
        """
        Execute an external-to-internal event bridge.

        This method is called by the outer app's event dispatcher when an event
        matches a configured external->internal bridge pattern.

        Args:
            matched_external_event_type: The actual external event type that was matched
            external_event_data: The event data from the external app
            bridge_config_entry: The bridge configuration that matched
        """
        try:
            # Extract bridge configuration
            to_internal_event_type = bridge_config_entry["to_internal_type"]
            transformer = bridge_config_entry.get("transformer")

            # Transform event data if transformer is provided
            final_data_for_internal_event = external_event_data
            if transformer is not None:
                final_data_for_internal_event = transformer(external_event_data, matched_external_event_type)

            # Emit event in internal app
            if self._internal_app is not None:
                await self._internal_app.emit_event(to_internal_event_type, final_data_for_internal_event)

                self._logger.debug(
                    f"Forwarded external event '{matched_external_event_type}' -> "
                    f"internal event '{to_internal_event_type}'"
                )

        except Exception as e:
            self._logger.error(
                f"Failed to execute external->internal bridge for event '{matched_external_event_type}': {e}"
            )
