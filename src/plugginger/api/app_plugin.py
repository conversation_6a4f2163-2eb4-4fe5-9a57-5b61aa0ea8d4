# src/plugginger/api/app_plugin.py

"""
AppPlugin base class for fractal application composition.

This module provides the AppPluginBase class that enables creating plugins
that encapsulate entire PluggingerAppInstance sub-applications, allowing
for hierarchical and modular application architectures.
"""

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any

from plugginger.api.plugin import PluginBase
from plugginger.core.exceptions import AppPluginError


class AppPluginBase(PluginBase, ABC):
    """
    Base class for creating App-Plugins that enable fractal application composition.

    An App-Plugin encapsulates a complete PluggingerAppInstance (sub-app) and exposes
    its functionality to an outer PluggingerAppInstance as if it were a regular plugin.
    This enables building complex, hierarchical systems from smaller, reusable modules.

    Subclasses must:
    1. Inherit from AppPluginBase
    2. Be decorated with @plugin(...) to define metadata for the outer app
    3. Implement _configure_internal_app() to set up the internal sub-app
    4. Optionally implement _bridge_events() for event communication between apps

    Example:
        ```python
        @plugin(name="user_management", version="1.0.0")
        class UserManagementAppPlugin(AppPluginBase):

            def _configure_internal_app(self) -> None:
                from plugginger.api.builder import PluggingerAppBuilder

                builder = PluggingerAppBuilder()
                builder.include_plugin(UserServicePlugin)
                builder.include_plugin(AuthenticationPlugin)

                self._internal_app = builder.build()

            def _bridge_events(self) -> None:
                # Forward events between outer and inner apps
                self.app.on_event("user.login", self._forward_to_internal)
                self._internal_app.on_event("auth.success", self._forward_to_outer)
        ```
    """

    def __init__(self, **injected_dependencies: Any) -> None:
        """
        Initialize the AppPlugin instance.

        Args:
            **injected_dependencies: Dependencies injected by the DI container
        """
        super().__init__(**injected_dependencies)
        self._internal_app: Any = None  # Will be set by _configure_internal_app
        self._is_internal_app_started = False

    @abstractmethod
    def _configure_internal_app(self) -> None:
        """
        Configure and build the internal sub-application.

        This method must be implemented by subclasses to set up their internal
        PluggingerAppInstance. The implementation should:
        1. Create a PluggingerAppBuilder
        2. Configure plugins, services, and settings for the sub-app
        3. Build the app and assign it to self._internal_app

        This method is called during the AppPlugin's setup phase.
        """
        ...

    def _bridge_events(self) -> None:
        """
        Set up event bridging between outer and internal apps.

        Override this method to establish communication channels between
        the outer application and the internal sub-application through events.
        This is optional but often useful for coordinating between app layers.
        """
        pass

    async def setup(self, config: Any) -> None:
        """
        Set up the AppPlugin and its internal application.

        This method:
        1. Configures the internal app via _configure_internal_app()
        2. Sets up event bridging via _bridge_events()
        3. Starts all plugins in the internal app

        Args:
            config: Configuration for this AppPlugin
        """
        try:
            # Configure the internal sub-application
            self._configure_internal_app()

            if self._internal_app is None:
                raise AppPluginError(
                    f"AppPlugin '{self.__class__.__name__}' failed to configure internal app. "
                    f"_configure_internal_app() must set self._internal_app"
                )

            # Set up event bridging between apps
            self._bridge_events()

            # Start all plugins in the internal app
            if hasattr(self._internal_app, 'start_all_plugins'):
                await self._internal_app.start_all_plugins()
                self._is_internal_app_started = True

        except Exception as e:
            raise AppPluginError(
                f"Failed to set up AppPlugin '{self.__class__.__name__}': {e}"
            ) from e

    async def teardown(self) -> None:
        """
        Tear down the AppPlugin and its internal application.

        This method stops all plugins in the internal app and cleans up resources.
        """
        try:
            if self._is_internal_app_started and self._internal_app:
                if hasattr(self._internal_app, 'stop_all_plugins'):
                    await self._internal_app.stop_all_plugins()
                self._is_internal_app_started = False

        except Exception as e:
            raise AppPluginError(
                f"Failed to tear down AppPlugin '{self.__class__.__name__}': {e}"
            ) from e

    @property
    def internal_app(self) -> Any:
        """
        Access to the internal sub-application.

        Returns:
            The internal PluggingerAppInstance managed by this AppPlugin

        Raises:
            AppPluginError: If the internal app hasn't been configured yet
        """
        if self._internal_app is None:
            raise AppPluginError(
                "Internal app not configured yet. Call setup() first or ensure "
                "_configure_internal_app() sets self._internal_app"
            )
        return self._internal_app

    def _forward_to_internal(self, event_data: dict[str, Any], event_type: str) -> None:
        """
        Helper method to forward events from outer app to internal app.

        Args:
            event_data: Event payload
            event_type: Type of event to emit in internal app
        """
        if self._internal_app and hasattr(self._internal_app, 'emit_event'):
            # Note: This would need to be async in a real implementation
            # For now, this is a placeholder
            pass

    def _forward_to_outer(self, event_data: dict[str, Any], event_type: str) -> None:
        """
        Helper method to forward events from internal app to outer app.

        Args:
            event_data: Event payload
            event_type: Type of event to emit in outer app
        """
        if hasattr(self.app, 'emit_event'):
            # Note: This would need to be async in a real implementation
            # For now, this is a placeholder
            pass
