# src/plugginger/api/depends.py

"""
Dependency injection utilities using the DI container.

This module provides the Depends class and utilities for dependency injection
in plugin methods, without circular import dependencies.
"""

from __future__ import annotations

from typing import Any, TypeVar

from plugginger.core.exceptions import DependencyError, MissingDependencyError
from plugginger.core.types import ServiceName
from plugginger.implementations.container import get_container

T = TypeVar("T")


class Depends:
    """
    Dependency injection marker for plugin method parameters.

    This class is used to mark parameters that should be injected by the
    dependency injection system. It can inject services, configuration,
    or other registered dependencies.

    Examples:
        ```python
        @plugin(name="user_service")
        class UserServicePlugin(PluginBase):
            @service(name="get_user")
            async def get_user(
                self,
                user_id: int,
                db: DatabaseService = Depends("database.connection"),
                config: dict = Depends("app.config")
            ) -> dict:
                # Use injected dependencies
                return await db.fetch_user(user_id)
        ```
    """

    def __init__(
        self,
        dependency: str | type[T] | None = None,
        *,
        optional: bool = False,
        default: Any = None,
    ) -> None:
        """
        Initialize a dependency injection marker.

        Args:
            dependency: The dependency to inject. Can be:
                - str: Service name (e.g., "database.connection")
                - type: Interface type to resolve from DI container
                - None: Use parameter name as service name
            optional: Whether the dependency is optional
            default: Default value if dependency is optional and not found
        """
        self.dependency = dependency
        self.optional = optional
        self.default = default

    def resolve(self, param_name: str) -> Any:
        """
        Resolve the dependency value.

        This method resolves dependencies based on the type of self.dependency:
        - Type: Resolved directly from the DI container
        - String: Resolved as service name or special configuration key
        - None: Uses parameter name as dependency key (currently not implemented)

        Args:
            param_name: Name of the parameter being injected

        Returns:
            The resolved dependency value

        Raises:
            MissingDependencyError: If required dependency is not found
            DependencyError: If dependency type is invalid or resolution fails
        """
        from plugginger.core.exceptions import DependencyResolutionError

        container = get_container()

        # Case 1: Type-based dependency resolution
        if isinstance(self.dependency, type):
            try:
                return container.get(self.dependency)
            except (ValueError, DependencyResolutionError) as e:
                if self.optional:
                    return self.default
                raise MissingDependencyError(
                    f"Required dependency type '{self.dependency.__name__}' not found in DI container"
                ) from e

        # Case 2: String-based dependency resolution
        elif isinstance(self.dependency, str):
            return self._resolve_string_dependency(self.dependency, container)

        # Case 3: None dependency - use parameter name
        elif self.dependency is None:
            # For MVP: Not implemented yet
            raise DependencyError(
                f"Parameter '{param_name}': dependency=None is not yet implemented. "
                f"Please specify an explicit dependency type or string key."
            )

        # Case 4: Invalid dependency type
        else:
            raise DependencyError(
                f"Invalid dependency type: {type(self.dependency)}. "
                f"Must be str, type, or None, got {type(self.dependency).__name__}"
            )

    def _resolve_string_dependency(self, dependency_key: str, container: Any) -> Any:
        """
        Resolve a string-based dependency.

        This method handles special configuration keys and service names.
        Special keys are resolved directly, while service names require
        the ServiceDispatcher (not fully implemented in this step).

        Args:
            dependency_key: The string dependency key to resolve
            container: The DI container instance

        Returns:
            The resolved dependency value

        Raises:
            MissingDependencyError: If dependency cannot be resolved
            DependencyError: If resolution method is not implemented
        """
        # Handle special configuration keys
        if dependency_key == "app.config":
            # For now, app.config is not implemented as there's no GlobalAppConfig class
            # This would need to be implemented when a proper config system is added
            if self.optional:
                return self.default
            raise DependencyError(
                "Special key 'app.config' is not yet implemented. "
                "A global configuration system needs to be implemented first."
            )

        elif dependency_key == "app.instance":
            try:
                # Try to get PluggingerAppInstance from container
                from plugginger.api.app import PluggingerAppInstance

                return container.get(PluggingerAppInstance)
            except (ValueError, ImportError) as e:
                if self.optional:
                    return self.default
                raise MissingDependencyError(
                    "Special key 'app.instance' could not be resolved. "
                    "PluggingerAppInstance not registered in DI container."
                ) from e

        elif dependency_key.startswith("plugin.config:"):
            # Extract plugin name from "plugin.config:plugin_name"
            plugin_name = dependency_key[14:]  # Remove "plugin.config:" prefix
            if self.optional:
                return self.default
            raise DependencyError(
                f"Plugin-specific config resolution for '{plugin_name}' is not yet implemented. "
                f"This requires context about the current plugin instance."
            )

        elif dependency_key == "plugin.instance_id":
            if self.optional:
                return self.default
            raise DependencyError(
                "Plugin instance ID resolution is not yet implemented. "
                "This requires context about the current plugin instance."
            )

        elif dependency_key == "plugin.config":
            if self.optional:
                return self.default
            raise DependencyError(
                "Current plugin config resolution is not yet implemented. "
                "This requires context about the current plugin instance."
            )

        # Handle service names (not fully implemented in this step)
        else:
            # This is likely a service name like "plugin_name.service_method"
            if self.optional:
                return self.default
            raise DependencyError(
                f"String-based service name resolution ('{dependency_key}') via Depends is not yet implemented. "
                f"Consider using type-based dependency injection instead, or use special keys like 'app.config'."
            )

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"Depends({self.dependency!r}, optional={self.optional}, default={self.default!r})"


def _resolve_service_dependency(service_name: str) -> Any:
    """
    Resolve a service dependency by name.

    This is a placeholder implementation that will be enhanced
    when we integrate with the service dispatcher.

    Args:
        service_name: Name of the service to resolve

    Returns:
        The service instance

    Raises:
        MissingDependencyError: If service is not found
    """
    # For now, return a placeholder
    # TODO: Integrate with actual service dispatcher
    raise MissingDependencyError(f"Service '{service_name}' not implemented yet")


def inject_dependencies(func: Any, **available_dependencies: Any) -> dict[str, Any]:
    """
    Analyze a function and prepare dependency injection values.

    This function inspects a method's parameters and resolves any
    Depends() markers to actual dependency values.

    Args:
        func: The function to analyze
        **available_dependencies: Available dependencies to inject

    Returns:
        Dictionary mapping parameter names to resolved values

    Raises:
        MissingDependencyError: If required dependencies are not available
    """
    import inspect

    injected_values: dict[str, Any] = {}
    sig = inspect.signature(func)

    for param_name, param in sig.parameters.items():
        # Skip 'self' parameter
        if param_name == "self":
            continue

        # Check if parameter has a Depends default
        if isinstance(param.default, Depends):
            depends_marker = param.default

            # Try to resolve from available dependencies first
            if param_name in available_dependencies:
                injected_values[param_name] = available_dependencies[param_name]
            else:
                # Resolve using the Depends marker
                injected_values[param_name] = depends_marker.resolve(param_name)
        elif param_name in available_dependencies:
            # Inject available dependency even without Depends marker
            injected_values[param_name] = available_dependencies[param_name]

    return injected_values


def create_dependency_proxy(service_name: ServiceName) -> Any:
    """
    Create a proxy object for a service dependency.

    This creates a proxy that will resolve the actual service
    when methods are called on it.

    Args:
        service_name: Name of the service to proxy

    Returns:
        A proxy object for the service
    """
    return ServiceProxy(service_name)


class ServiceProxy:
    """
    Proxy object for service dependencies.

    This proxy delays service resolution until methods are actually called,
    allowing for more flexible dependency injection.
    """

    def __init__(self, service_name: ServiceName) -> None:
        """
        Initialize the service proxy.

        Args:
            service_name: Name of the service to proxy
        """
        self._service_name = service_name
        self._resolved_service: Any = None

    def _resolve_service(self) -> Any:
        """Resolve the actual service instance."""
        if self._resolved_service is None:
            # TODO: Integrate with actual service dispatcher
            raise MissingDependencyError(f"Service '{self._service_name}' not available")
        return self._resolved_service

    def __getattr__(self, name: str) -> Any:
        """Proxy attribute access to the resolved service."""
        service = self._resolve_service()
        return getattr(service, name)

    def __call__(self, *args: Any, **kwargs: Any) -> Any:
        """Proxy method calls to the resolved service."""
        service = self._resolve_service()
        return service(*args, **kwargs)

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"ServiceProxy({self._service_name!r})"


# Convenience functions for common dependency types


def service_dependency(service_name: str, optional: bool = False) -> Depends:
    """
    Create a service dependency marker.

    Args:
        service_name: Name of the service to inject
        optional: Whether the service is optional

    Returns:
        Depends marker for the service
    """
    return Depends(service_name, optional=optional)


def config_dependency(config_key: str = "app.config", optional: bool = False) -> Depends:
    """
    Create a configuration dependency marker.

    Args:
        config_key: Configuration key to inject
        optional: Whether the config is optional

    Returns:
        Depends marker for the configuration
    """
    return Depends(config_key, optional=optional)


def interface_dependency(interface_type: type[T], optional: bool = False) -> Depends:
    """
    Create an interface dependency marker.

    Args:
        interface_type: Interface type to resolve from DI container
        optional: Whether the interface is optional

    Returns:
        Depends marker for the interface
    """
    return Depends(interface_type, optional=optional)
