# src/plugginger/api/events.py

"""
Event decorator and utilities using dependency injection.

This module provides the @on_event decorator for marking plugin methods
as event listeners, without circular import dependencies.
"""

from __future__ import annotations

import inspect
from collections.abc import Awaitable, Callable
from typing import Any, TypeVar, cast

from plugginger.core.constants import EVENT_METADATA_KEY
from plugginger.core.exceptions import EventDefinitionError
from plugginger.core.types import EventHandlerType, EventPatternInput

F = TypeVar("F", bound=Callable[..., Awaitable[None]])


def on_event(
    patterns: EventPatternInput,
    timeout_seconds: float | None = None,
    description: str | None = None,
    priority: int = 0,
) -> Callable[[F], F]:
    """
    Decorator to mark a plugin method as an event listener.

    This decorator adds metadata to the method that the framework uses
    for event listener registration and dispatch.

    Args:
        patterns: Event pattern(s) to listen for (string or list of strings)
        timeout_seconds: Optional timeout for event listener execution
        description: Optional description of the event listener
        priority: Priority for listener execution (higher = earlier, default: 0)

    Returns:
        The decorated method with event listener metadata

    Raises:
        EventDefinitionError: If the method is not valid for event decoration

    Example:
        ```python
        @plugin(name="user_manager")
        class UserManagerPlugin(PluginBase):
            @on_event("user.created", timeout_seconds=5.0)
            async def on_user_created(self, event_data: dict) -> None:
                # Handle user creation event
                user_id = event_data["user_id"]
                await self.send_welcome_email(user_id)

            @on_event(["user.*", "admin.user.*"], priority=10)
            async def on_any_user_event(self, event_data: dict, event_type: str) -> None:
                # Handle any user-related event with high priority
                await self.log_user_activity(event_type, event_data)
        ```
    """

    def decorator(func: F) -> F:
        # Validate that the function is async
        if not inspect.iscoroutinefunction(func):
            raise EventDefinitionError(
                f"Event listener '{func.__name__}' must be async (use 'async def')"
            )

        # Validate function signature
        sig = inspect.signature(func)
        params = list(sig.parameters.values())

        # Must have at least 'self' parameter
        if not params or params[0].name != "self":
            raise EventDefinitionError(
                f"Event listener '{func.__name__}' must be an instance method (first parameter must be 'self')"
            )

        # Validate timeout FIRST (before return annotation)
        if timeout_seconds is not None and (
            not isinstance(timeout_seconds, int | float) or timeout_seconds <= 0
        ):
            raise EventDefinitionError(
                f"Event listener timeout must be a positive number, got: {timeout_seconds!r}"
            )

        # Validate priority SECOND (before return annotation)
        if not isinstance(priority, int):
            raise EventDefinitionError(
                f"Event listener priority must be an integer, got: {priority!r}"
            )

        # Normalize patterns to list
        pattern_list = _normalize_event_patterns(patterns)

        # Validate patterns
        for pattern in pattern_list:
            if not pattern or not isinstance(pattern, str):
                raise EventDefinitionError(
                    f"Event pattern must be a non-empty string, got: {pattern!r}"
                )

        # Validate return annotation LAST (should be None or empty)
        # Allow any return annotation that indicates None
        if (
            sig.return_annotation != inspect.Signature.empty
            and sig.return_annotation is not None
            and sig.return_annotation is not type(None)
            and str(sig.return_annotation) != "None"
        ):
            raise EventDefinitionError(
                f"Event listener '{func.__name__}' must return None, got: {sig.return_annotation}"
            )

        # Create event listener metadata
        metadata = {
            "patterns": pattern_list,
            "timeout_seconds": timeout_seconds,
            "description": description,
            "priority": priority,
            "method_name": func.__name__,
            "signature": str(sig),
            "parameters": [
                {
                    "name": param.name,
                    "annotation": param.annotation,
                    "default": param.default if param.default != inspect.Parameter.empty else None,
                    "kind": param.kind.name,
                }
                for param in params[1:]  # Skip 'self'
            ],
        }

        # Attach metadata to the function
        setattr(func, EVENT_METADATA_KEY, metadata)

        return cast(F, func)

    return decorator


def _normalize_event_patterns(patterns: EventPatternInput) -> list[str]:
    """
    Normalize event patterns input to a list of strings.

    Args:
        patterns: Event pattern(s) - string or list of strings

    Returns:
        List of event pattern strings

    Raises:
        EventDefinitionError: If patterns are invalid
    """
    if isinstance(patterns, str):
        return [patterns]
    elif isinstance(patterns, list | tuple):
        pattern_list = list(patterns)
        for pattern in pattern_list:
            if not isinstance(pattern, str):
                raise EventDefinitionError(f"All event patterns must be strings, got: {pattern!r}")
        return pattern_list
    else:
        raise EventDefinitionError(
            f"Event patterns must be a string or list of strings, got: {type(patterns)}"
        )


def get_event_listener_metadata(method: Callable[..., Any]) -> dict[str, Any]:
    """
    Get event listener metadata from a decorated method.

    Args:
        method: The method to inspect

    Returns:
        Dictionary containing event listener metadata

    Raises:
        EventDefinitionError: If the method is not a valid event listener
    """
    if not hasattr(method, EVENT_METADATA_KEY):
        raise EventDefinitionError(
            f"Method '{method.__name__}' is not a valid event listener "
            f"(missing @on_event decorator)"
        )

    return cast(dict[str, Any], getattr(method, EVENT_METADATA_KEY))


def is_event_listener_method(method: Callable[..., Any]) -> bool:
    """
    Check if a method is a valid event listener method.

    Args:
        method: The method to check

    Returns:
        True if the method is a valid event listener, False otherwise
    """
    return hasattr(method, EVENT_METADATA_KEY) and inspect.iscoroutinefunction(method)


def extract_event_listeners(plugin_instance: Any) -> dict[str, EventHandlerType]:
    """
    Extract all event listener methods from a plugin instance.

    Args:
        plugin_instance: The plugin instance to inspect

    Returns:
        Dictionary mapping method names to bound event handler methods
    """
    listeners: dict[str, EventHandlerType] = {}

    # Inspect the class methods to avoid calling properties
    plugin_class = type(plugin_instance)

    for attr_name in dir(plugin_class):
        # Skip private/magic methods
        if attr_name.startswith("_"):
            continue

        # Get the unbound method from the class
        class_attr = getattr(plugin_class, attr_name)

        # Check if it's an event listener method (check the unbound method)
        if is_event_listener_method(class_attr):
            # Get the bound method from the instance
            bound_method = getattr(plugin_instance, attr_name)
            listeners[attr_name] = bound_method

    return listeners


def get_listener_patterns(
    plugin_instance: Any,
) -> list[tuple[str, EventHandlerType, dict[str, Any]]]:
    """
    Get all event patterns and their corresponding listeners from a plugin instance.

    Args:
        plugin_instance: The plugin instance to inspect

    Returns:
        List of tuples containing (pattern, handler, metadata) for each pattern
    """
    pattern_handlers = []

    # Get all event listeners
    listeners = extract_event_listeners(plugin_instance)

    for method_name, handler in listeners.items():
        # Get metadata from the class method (unbound)
        class_method = getattr(type(plugin_instance), method_name)
        metadata = get_event_listener_metadata(class_method)

        # Add each pattern with its handler and metadata
        for pattern in metadata["patterns"]:
            pattern_handlers.append((pattern, handler, metadata))

    # Sort by priority (higher priority first)
    pattern_handlers.sort(key=lambda x: x[2]["priority"], reverse=True)

    return pattern_handlers


def validate_event_listener_signature(method: Callable[..., Any]) -> None:
    """
    Validate that an event listener method has a proper signature.

    Args:
        method: The method to validate

    Raises:
        EventDefinitionError: If the method signature is invalid
    """
    _validate_async_requirement(method)
    sig = inspect.signature(method)
    params = list(sig.parameters.values())

    _validate_basic_parameters(method, params)
    _validate_parameter_types(method, params[1:])  # Skip 'self'
    _validate_return_annotation(method, sig)


def _validate_async_requirement(method: Callable[..., Any]) -> None:
    """Validate that the method is async."""
    if not inspect.iscoroutinefunction(method):
        raise EventDefinitionError(f"Event listener '{method.__name__}' must be async")


def _validate_basic_parameters(method: Callable[..., Any], params: list[inspect.Parameter]) -> None:
    """Validate basic parameter requirements."""
    if not params:
        raise EventDefinitionError(
            f"Event listener '{method.__name__}' must have at least one parameter (self)"
        )

    if params[0].name != "self":
        raise EventDefinitionError(
            f"Event listener '{method.__name__}' first parameter must be 'self'"
        )

    # Check parameter count (self + event_data + optional event_type)
    effective_params = params[1:]  # Skip 'self'
    if len(effective_params) < 1 or len(effective_params) > 2:
        raise EventDefinitionError(
            f"Event listener '{method.__name__}' must have 1 or 2 parameters after 'self' "
            f"(event_data, optional event_type), got {len(effective_params)}"
        )


def _validate_parameter_types(
    method: Callable[..., Any], effective_params: list[inspect.Parameter]
) -> None:
    """Validate parameter types are supported."""
    for param in effective_params:
        if param.kind == inspect.Parameter.VAR_POSITIONAL:
            raise EventDefinitionError(
                f"Event listener '{method.__name__}' cannot use *args parameters"
            )
        if param.kind == inspect.Parameter.VAR_KEYWORD:
            raise EventDefinitionError(
                f"Event listener '{method.__name__}' cannot use **kwargs parameters"
            )


def _validate_return_annotation(method: Callable[..., Any], sig: inspect.Signature) -> None:
    """Validate return annotation is None."""
    # Allow any return annotation that indicates None
    if (
        sig.return_annotation != inspect.Signature.empty
        and sig.return_annotation is not None
        and sig.return_annotation is not type(None)
        and str(sig.return_annotation) != "None"
    ):
        raise EventDefinitionError(f"Event listener '{method.__name__}' must return None")
