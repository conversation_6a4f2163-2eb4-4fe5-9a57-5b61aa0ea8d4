# src/plugginger/api/service.py

"""
Service decorator and utilities using dependency injection.

This module provides the @service decorator for marking plugin methods
as callable services, without circular import dependencies.
"""

from __future__ import annotations

import inspect
from collections.abc import Awaitable, Callable
from typing import Any, TypeVar, cast

from plugginger.core.constants import SERVICE_METADATA_KEY
from plugginger.core.exceptions import ServiceDefinitionError
from plugginger.core.types import (
    ServiceMethodType,
)

F = TypeVar("F", bound=Callable[..., Awaitable[Any]])


def service(
    name: str | None = None, timeout_seconds: float | None = None, description: str | None = None
) -> Callable[[F], F]:
    """
    Decorator to mark a plugin method as a callable service.

    This decorator adds metadata to the method that the framework uses
    for service registration and discovery.

    Args:
        name: Optional custom name for the service (defaults to method name)
        timeout_seconds: Optional timeout for service calls
        description: Optional description of the service

    Returns:
        The decorated method with service metadata

    Raises:
        ServiceDefinitionError: If the method is not valid for service decoration

    Example:
        ```python
        @plugin(name="database")
        class DatabasePlugin(PluginBase):
            @service(name="get_user", timeout_seconds=5.0)
            async def get_user_by_id(self, user_id: int) -> dict:
                # Service implementation
                return {"id": user_id, "name": "John"}
        ```
    """

    def decorator(func: F) -> F:
        # Validate that the function is async
        if not inspect.iscoroutinefunction(func):
            raise ServiceDefinitionError(
                f"Service method '{func.__name__}' must be async (use 'async def')"
            )

        # Validate function signature
        sig = inspect.signature(func)
        params = list(sig.parameters.values())

        # Must have at least 'self' parameter
        if not params or params[0].name != "self":
            raise ServiceDefinitionError(
                f"Service method '{func.__name__}' must be an instance method (first parameter must be 'self')"
            )

        # Determine service name
        service_name = name or func.__name__

        # Validate service name
        if not service_name or not isinstance(service_name, str):
            raise ServiceDefinitionError(
                f"Service name must be a non-empty string, got: {service_name!r}"
            )

        # Validate timeout
        if timeout_seconds is not None and (
            not isinstance(timeout_seconds, int | float) or timeout_seconds <= 0
        ):
            raise ServiceDefinitionError(
                f"Service timeout must be a positive number, got: {timeout_seconds!r}"
            )

        # Create service metadata
        metadata = {
            "name": service_name,
            "timeout_seconds": timeout_seconds,
            "description": description,
            "method_name": func.__name__,
            "signature": str(sig),
            "parameters": [
                {
                    "name": param.name,
                    "annotation": param.annotation,
                    "default": param.default if param.default != inspect.Parameter.empty else None,
                    "kind": param.kind.name,
                }
                for param in params[1:]  # Skip 'self'
            ],
        }

        # Attach metadata to the function
        setattr(func, SERVICE_METADATA_KEY, metadata)

        return cast(F, func)

    return decorator


def get_service_metadata(method: Callable[..., Any]) -> dict[str, Any]:
    """
    Get service metadata from a decorated method.

    Args:
        method: The method to inspect

    Returns:
        Dictionary containing service metadata

    Raises:
        ServiceDefinitionError: If the method is not a valid service
    """
    if not hasattr(method, SERVICE_METADATA_KEY):
        raise ServiceDefinitionError(
            f"Method '{method.__name__}' is not a valid service (missing @service decorator)"
        )

    return cast(dict[str, Any], getattr(method, SERVICE_METADATA_KEY))


def is_service_method(method: Callable[..., Any]) -> bool:
    """
    Check if a method is a valid service method.

    Args:
        method: The method to check

    Returns:
        True if the method is a valid service, False otherwise
    """
    return hasattr(method, SERVICE_METADATA_KEY) and inspect.iscoroutinefunction(method)


def extract_service_methods(plugin_instance: Any) -> dict[str, ServiceMethodType[..., Any]]:
    """
    Extract all service methods from a plugin instance.

    Args:
        plugin_instance: The plugin instance to inspect

    Returns:
        Dictionary mapping service names to bound methods
    """
    services: dict[str, ServiceMethodType[..., Any]] = {}

    # Inspect the class methods to avoid calling properties
    plugin_class = type(plugin_instance)

    for attr_name in dir(plugin_class):
        # Skip private/magic methods
        if attr_name.startswith("_"):
            continue

        # Get the unbound method from the class
        class_attr = getattr(plugin_class, attr_name)

        # Check if it's a service method (check the unbound method)
        if is_service_method(class_attr):
            # Get the bound method from the instance
            bound_method = getattr(plugin_instance, attr_name)
            metadata = get_service_metadata(class_attr)
            service_name = metadata["name"]
            services[service_name] = bound_method

    return services


def validate_service_method_signature(method: Callable[..., Any]) -> None:
    """
    Validate that a service method has a proper signature.

    Args:
        method: The method to validate

    Raises:
        ServiceDefinitionError: If the method signature is invalid
    """
    if not inspect.iscoroutinefunction(method):
        raise ServiceDefinitionError(f"Service method '{method.__name__}' must be async")

    sig = inspect.signature(method)
    params = list(sig.parameters.values())

    if not params:
        raise ServiceDefinitionError(
            f"Service method '{method.__name__}' must have at least one parameter (self)"
        )

    if params[0].name != "self":
        raise ServiceDefinitionError(
            f"Service method '{method.__name__}' first parameter must be 'self'"
        )

    # Check for unsupported parameter types
    for param in params[1:]:  # Skip 'self'
        if param.kind == inspect.Parameter.VAR_POSITIONAL:
            raise ServiceDefinitionError(
                f"Service method '{method.__name__}' cannot use *args parameters"
            )
        if param.kind == inspect.Parameter.VAR_KEYWORD:
            raise ServiceDefinitionError(
                f"Service method '{method.__name__}' cannot use **kwargs parameters"
            )


def get_service_call_signature(method: Callable[..., Any]) -> inspect.Signature:
    """
    Get the call signature for a service method (excluding 'self').

    Args:
        method: The service method

    Returns:
        Signature object for calling the service
    """
    sig = inspect.signature(method)
    params = list(sig.parameters.values())[1:]  # Skip 'self'

    return inspect.Signature(parameters=params, return_annotation=sig.return_annotation)
