# src/plugginger/api/app.py

"""
Application instance API using dependency injection.

This module provides the PluggingerAppInstance class that serves as the main
interface for interacting with the plugin framework at runtime.
"""

from __future__ import annotations

import asyncio
from typing import Any, TypeVar

from plugginger.core.config import DEFAULT_APP_NAME
from plugginger.core.exceptions import (
    BackgroundTaskError,
    EventDefinitionError,
)
from plugginger.core.types import PluginInstanceId, ServiceName
from plugginger.implementations.container import get_container
from plugginger.interfaces.events import EventDispatcher
from plugginger.interfaces.services import ServiceDispatcher

T = TypeVar("T")


class PluggingerAppInstance:
    """
    Main application instance for the Plugginger framework.

    This class provides the runtime interface for interacting with plugins,
    services, events, and background tasks. It uses dependency injection
    to access all framework components.

    Example:
        ```python
        # Get app instance from DI container
        app = get_container().get(PluggingerAppInstance)

        # Call a service
        result = await app.call_service("user.get_by_id", user_id=123)

        # Emit an event
        await app.emit_event("user.created", {"user_id": 123, "name": "<PERSON>"})

        # Create a managed background task
        task = app.create_managed_task(background_worker())
        ```
    """

    def __init__(
        self,
        service_dispatcher: ServiceDispatcher,
        event_dispatcher: EventDispatcher,
        app_name: str = DEFAULT_APP_NAME,
    ) -> None:
        """
        Initialize the application instance.

        Args:
            service_dispatcher: Service dispatcher for calling services
            event_dispatcher: Event dispatcher for emitting events
            app_name: Name of the application
        """
        self._service_dispatcher = service_dispatcher
        self._event_dispatcher = event_dispatcher
        self._app_name = app_name
        self._managed_tasks: dict[str, asyncio.Task[Any]] = {}
        self._task_counter = 0
        self._is_shutdown = False

    async def call_service(self, service_name: ServiceName, *args: Any, **kwargs: Any) -> Any:
        """
        Call a registered service by name.

        Args:
            service_name: Name of the service to call
            *args: Positional arguments to pass to the service
            **kwargs: Keyword arguments to pass to the service

        Returns:
            The result returned by the service

        Raises:
            ServiceNotFoundError: If the service is not found
            ServiceExecutionError: If the service execution fails
        """
        if self._is_shutdown:
            raise RuntimeError("Application has been shut down")

        return await self._service_dispatcher.call_service(service_name, *args, **kwargs)

    async def emit_event(
        self, event_type: str, event_data: dict[str, Any], *, wait_for_listeners: bool = True
    ) -> None:
        """
        Emit an event to all registered listeners.

        Args:
            event_type: Type/pattern of the event
            event_data: Data to send with the event
            wait_for_listeners: Whether to wait for all listeners to complete

        Raises:
            EventDefinitionError: If event parameters are invalid
        """
        if self._is_shutdown:
            raise RuntimeError("Application has been shut down")

        if not event_type or not isinstance(event_type, str):
            raise EventDefinitionError(
                f"Event type must be a non-empty string, got: {event_type!r}"
            )

        await self._event_dispatcher.emit_event(event_type, event_data)

    def create_managed_task(
        self, coro: Any, *, name: str | None = None, plugin_id: PluginInstanceId | None = None
    ) -> asyncio.Task[Any]:
        """
        Create a managed background task.

        Managed tasks are automatically tracked and cleaned up when the
        application shuts down or when the owning plugin is torn down.

        Args:
            coro: Coroutine to run as a background task
            name: Optional name for the task (for debugging)
            plugin_id: Optional plugin ID that owns this task

        Returns:
            The created asyncio.Task

        Raises:
            BackgroundTaskError: If task creation fails
        """
        if self._is_shutdown:
            raise RuntimeError("Application has been shut down")

        if not asyncio.iscoroutine(coro):
            raise BackgroundTaskError(f"Expected coroutine, got: {type(coro)}")

        # Generate task ID
        self._task_counter += 1
        task_id = f"task_{self._task_counter}"
        if name:
            task_id = f"{name}_{self._task_counter}"

        # Create the task
        try:
            task = asyncio.create_task(coro, name=task_id)
        except Exception as e:
            raise BackgroundTaskError(f"Failed to create task: {e}") from e

        # Track the task
        self._managed_tasks[task_id] = task

        # Add cleanup callback
        task.add_done_callback(lambda t: self._cleanup_task(task_id))

        return task

    def get_managed_tasks(
        self, plugin_id: PluginInstanceId | None = None
    ) -> list[asyncio.Task[Any]]:
        """
        Get all managed tasks, optionally filtered by plugin.

        Args:
            plugin_id: Optional plugin ID to filter tasks

        Returns:
            List of managed tasks
        """
        # For now, return all tasks (plugin filtering will be added later)
        return list(self._managed_tasks.values())

    async def cancel_managed_tasks(self, plugin_id: PluginInstanceId | None = None) -> None:
        """
        Cancel managed tasks, optionally filtered by plugin.

        Args:
            plugin_id: Optional plugin ID to filter tasks
        """
        tasks_to_cancel = self.get_managed_tasks(plugin_id)

        if not tasks_to_cancel:
            return

        # Cancel all tasks
        for task in tasks_to_cancel:
            if not task.done():
                task.cancel()

        # Wait for cancellation to complete
        if tasks_to_cancel:
            await asyncio.gather(*tasks_to_cancel, return_exceptions=True)

    def _cleanup_task(self, task_id: str) -> None:
        """
        Clean up a completed task.

        Args:
            task_id: ID of the task to clean up
        """
        self._managed_tasks.pop(task_id, None)

    async def shutdown(self) -> None:
        """
        Shutdown the application instance.

        This cancels all managed tasks and marks the instance as shut down.
        """
        if self._is_shutdown:
            return

        self._is_shutdown = True

        # Cancel all managed tasks
        await self.cancel_managed_tasks()

    @property
    def app_name(self) -> str:
        """Get the application name."""
        return self._app_name

    @property
    def is_shutdown(self) -> bool:
        """Check if the application has been shut down."""
        return self._is_shutdown

    @property
    def task_count(self) -> int:
        """Get the number of active managed tasks."""
        return len(self._managed_tasks)

    def list_services(self) -> list[str]:
        """
        List all available services.

        Returns:
            List of service names in format "plugin_name.service_name"
        """
        return self._service_dispatcher.list_services()

    def list_event_patterns(self) -> list[str]:
        """
        List all registered event patterns.

        Returns:
            List of event patterns that have listeners
        """
        return self._event_dispatcher.list_patterns()

    def __repr__(self) -> str:
        """String representation for debugging."""
        return (
            f"PluggingerAppInstance(tasks={len(self._managed_tasks)}, shutdown={self._is_shutdown})"
        )


def get_app_instance() -> PluggingerAppInstance:
    """
    Get the current application instance from the DI container.

    Returns:
        The current PluggingerAppInstance

    Raises:
        RuntimeError: If no app instance is registered in the DI container
    """
    container = get_container()
    try:
        return container.get(PluggingerAppInstance)
    except ValueError as e:
        raise RuntimeError("No PluggingerAppInstance found in DI container") from e


# Convenience functions for common operations


async def call_service(service_name: ServiceName, *args: Any, **kwargs: Any) -> Any:
    """
    Convenience function to call a service.

    Args:
        service_name: Name of the service to call
        *args: Positional arguments to pass to the service
        **kwargs: Keyword arguments to pass to the service

    Returns:
        The result returned by the service
    """
    app = get_app_instance()
    return await app.call_service(service_name, *args, **kwargs)


async def emit_event(
    event_type: str, event_data: dict[str, Any], *, wait_for_listeners: bool = True
) -> None:
    """
    Convenience function to emit an event.

    Args:
        event_type: Type/pattern of the event
        event_data: Data to send with the event
        wait_for_listeners: Whether to wait for all listeners to complete
    """
    app = get_app_instance()
    await app.emit_event(event_type, event_data, wait_for_listeners=wait_for_listeners)


def create_managed_task(
    coro: Any, *, name: str | None = None, plugin_id: PluginInstanceId | None = None
) -> asyncio.Task[Any]:
    """
    Convenience function to create a managed background task.

    Args:
        coro: Coroutine to run as a background task
        name: Optional name for the task
        plugin_id: Optional plugin ID that owns this task

    Returns:
        The created asyncio.Task
    """
    app = get_app_instance()
    return app.create_managed_task(coro, name=name, plugin_id=plugin_id)
