"""Simple builder for testing without complex DI."""

from typing import TypeVar, cast

from plugginger.api.app import PluggingerAppInstance
from plugginger.api.plugin import PluginBase, get_plugin_metadata, is_plugin_class
from plugginger.core.config import EventListenerFaultPolicy
from plugginger.core.constants import DEFAULT_APP_NAME
from plugginger.core.exceptions import ConfigurationError, PluginRegistrationError
from plugginger.core.types import PluginInstanceId
from plugginger.implementations.events import SimpleEventDispatcher, SimpleEventFaultHandler
from plugginger.implementations.services import SimpleServiceDispatcher

T = TypeVar("T", bound=PluginBase)


class SimplePluggingerAppBuilder:
    """Simple builder for testing without complex DI."""

    def __init__(self) -> None:
        """Initialize the simple builder."""
        self._app_name = DEFAULT_APP_NAME
        self._plugin_classes: list[type[PluginBase]] = []
        self._plugin_instances: dict[str, PluginBase] = {}
        self._service_dispatcher = SimpleServiceDispatcher()

        # Create fault handler and event dispatcher
        fault_handler = SimpleEventFaultHandler(policy=EventListenerFaultPolicy.ISOLATE_AND_LOG)
        self._event_dispatcher = SimpleEventDispatcher(fault_handler=fault_handler)
        self._is_built = False

    def with_name(self, name: str) -> "SimplePluggingerAppBuilder":
        """Set the application name."""
        if self._is_built:
            raise ConfigurationError("Cannot modify builder after build() has been called")

        if not name or not isinstance(name, str):
            raise ConfigurationError(f"App name must be a non-empty string, got: {name!r}")

        self._app_name = name
        return self

    def register_plugin(self, plugin_class: type[T]) -> "SimplePluggingerAppBuilder":
        """Register a plugin class."""
        if self._is_built:
            raise ConfigurationError("Cannot modify builder after build() has been called")

        # Validate plugin class
        if not is_plugin_class(plugin_class):
            raise PluginRegistrationError(
                f"Class '{plugin_class.__name__}' is not a valid plugin class. "
                f"Make sure it inherits from PluginBase and has the @plugin decorator."
            )

        # Check for duplicate registration
        if plugin_class in self._plugin_classes:
            raise PluginRegistrationError(
                f"Plugin class '{plugin_class.__name__}' is already registered"
            )

        self._plugin_classes.append(plugin_class)
        return self

    def build(self) -> PluggingerAppInstance:
        """Build the application instance."""
        if self._is_built:
            raise ConfigurationError("build() can only be called once per builder instance")

        try:
            # Mark as built to prevent further modifications
            self._is_built = True

            # Instantiate plugins
            self._instantiate_plugins()

            # Register services and events
            self._register_services_and_events()

            # Create and return app instance
            return self._create_app_instance()

        except Exception as e:
            # Reset built state on failure
            self._is_built = False
            if isinstance(e, ConfigurationError | PluginRegistrationError):
                raise
            raise ConfigurationError(f"Failed to build application: {e}") from e

    def _instantiate_plugins(self) -> None:
        """Instantiate all registered plugin classes."""
        for plugin_class in self._plugin_classes:
            try:
                # Get plugin metadata
                metadata = get_plugin_metadata(plugin_class)
                plugin_name = metadata["name"]

                # Generate unique instance ID
                instance_id = self._generate_instance_id(plugin_name)

                # Create plugin instance with app reference
                # For simple builder, we create a minimal app instance
                from plugginger.api.app import PluggingerAppInstance

                temp_app = PluggingerAppInstance(
                    app_name=self._app_name,
                )
                plugin_instance = plugin_class(temp_app)
                plugin_instance._plugginger_instance_id = instance_id

                # Store the instance
                self._plugin_instances[instance_id] = plugin_instance

            except Exception as e:
                raise PluginRegistrationError(
                    f"Failed to instantiate plugin '{plugin_class.__name__}': {e}"
                ) from e

    def _register_services_and_events(self) -> None:
        """Register services and events from all plugin instances."""
        for instance_id, plugin_instance in self._plugin_instances.items():
            try:
                # Register services
                self._register_plugin_services(instance_id, plugin_instance)

                # Register event listeners
                self._register_plugin_events(instance_id, plugin_instance)

            except Exception as e:
                raise PluginRegistrationError(
                    f"Failed to register services/events for plugin '{instance_id}': {e}"
                ) from e

    def _register_plugin_services(self, instance_id: str, plugin_instance: PluginBase) -> None:
        """Register services from a plugin instance."""
        from plugginger.api.service import extract_service_methods

        services = extract_service_methods(plugin_instance)
        for service_name, service_method in services.items():
            # Create fully qualified service name with plugin prefix
            qualified_service_name = f"{instance_id}.{service_name}"
            self._service_dispatcher.add_service(qualified_service_name, service_method)

    def _register_plugin_events(self, instance_id: str, plugin_instance: PluginBase) -> None:
        """Register event listeners from a plugin instance."""
        from plugginger.api.events import get_listener_patterns

        patterns = get_listener_patterns(plugin_instance)
        for pattern, handler, _metadata in patterns:
            self._event_dispatcher.add_listener(pattern, handler)

    def _create_app_instance(self) -> PluggingerAppInstance:
        """Create the final application instance."""
        return PluggingerAppInstance(
            app_name=self._app_name,
        )

    def _generate_instance_id(self, plugin_name: str) -> PluginInstanceId:
        """Generate a unique instance ID for a plugin."""
        # Simple implementation - could be enhanced with UUIDs or counters
        existing_count = sum(
            1 for id in self._plugin_instances.keys() if id.startswith(plugin_name)
        )
        if existing_count == 0:
            return plugin_name
        else:
            return f"{plugin_name}_{existing_count + 1}"

    @property
    def app_name(self) -> str:
        """Get the configured application name."""
        return self._app_name

    @property
    def plugin_count(self) -> int:
        """Get the number of registered plugin classes."""
        return len(self._plugin_classes)

    @property
    def is_built(self) -> bool:
        """Check if the application has been built."""
        return self._is_built


# Alias for compatibility
PluggingerAppBuilder = SimplePluggingerAppBuilder
