#!/usr/bin/env python3

import asyncio
import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_cli_tools():
    print("=== CLI TOOLS TEST ===")

    try:
        # Test stub generator
        print("Testing stub generator...")
        print("✓ Stub generator imported successfully")

        # Test CLI utilities
        print("Testing CLI utilities...")
        from plugginger.cli.utils import validate_factory_path
        print("✓ CLI utilities imported successfully")

        # Test CLI commands
        print("Testing CLI commands...")
        print("✓ CLI commands imported successfully")

        # Test testing utilities
        print("Testing testing utilities...")
        from plugginger.testing.collectors import EventCollector, ServiceCallCollector
        from plugginger.testing.mock_app import create_mock_app
        print("✓ Testing utilities imported successfully")

        # Test mock app functionality
        print("Testing mock app functionality...")
        mock_app = create_mock_app("TestApp")
        print(f"Mock app created: {mock_app.app_name}")

        # Test service registration and calling
        async def test_service():
            return "test_result"

        mock_app.service_dispatcher.add_service("test_service", test_service)
        result = await mock_app.call_service("test_service")
        print(f"Service call result: {result}")

        # Test event emission
        await mock_app.emit_event("test.event", {"message": "test"})
        print("✓ Event emitted successfully")

        # Test managed tasks
        async def test_task():
            await asyncio.sleep(0.1)
            return "task_done"

        task = mock_app.create_managed_task(test_task())
        await task
        print("✓ Managed task completed")

        # Test collectors
        event_collector = EventCollector()
        service_collector = ServiceCallCollector()

        await event_collector.collect_event({"test": "data"}, "test.event")
        await service_collector.collect_call("test_service", (), {}, "result")

        print(f"Event collector has {event_collector.event_count} events")
        print(f"Service collector has {service_collector.call_count} calls")

        # Test factory path validation
        valid_paths = [
            "myapp.main:create_app",
            "package.subpackage.module:factory_function"
        ]

        invalid_paths = [
            "invalid_path",
            "module:",
            ":function",
            "module.123invalid:function"
        ]

        for path in valid_paths:
            if not validate_factory_path(path):
                print(f"✗ Valid path rejected: {path}")
            else:
                print(f"✓ Valid path accepted: {path}")

        for path in invalid_paths:
            if validate_factory_path(path):
                print(f"✗ Invalid path accepted: {path}")
            else:
                print(f"✓ Invalid path rejected: {path}")

        # Cleanup
        await mock_app.shutdown()
        print("✓ Mock app shutdown completed")

        print("\n✅ ALL CLI TOOLS TESTS PASSED!")

    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_cli_tools())
