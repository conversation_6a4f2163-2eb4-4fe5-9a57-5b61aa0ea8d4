#!/usr/bin/env python3
"""Test the refactored event dispatcher to ensure it works correctly."""

import sys
import os
import asyncio
from typing import Any

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_component_imports():
    """Test that all new components can be imported."""
    print("Testing component imports...")
    
    try:
        from plugginger._internal.runtime.dispatcher import (
            EventPatternMatcher,
            ListenerTaskManager,
            EventDispatcher,
        )
        print("✅ All event dispatcher components imported successfully")
        return True
        
    except Exception as e:
        print(f"❌ Component imports failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pattern_matcher():
    """Test the EventPatternMatcher functionality."""
    print("\nTesting EventPatternMatcher...")
    
    try:
        from plugginger._internal.runtime.dispatcher import EventPatternMatcher
        
        # Create test listeners
        async def listener1(event_data: dict[str, Any]) -> None:
            pass
        
        async def listener2(event_data: dict[str, Any]) -> None:
            pass
        
        # Create listeners map
        listeners_map = {
            "user.*": [listener1],
            "user.created": [listener2],
            "system.*": [listener1],
        }
        
        # Create pattern matcher
        matcher = EventPatternMatcher(listeners_map)
        
        # Test pattern matching
        matches = matcher.find_matching_listeners("user.created")
        assert len(matches) == 2  # Should match both "user.*" and "user.created"
        
        matches = matcher.find_matching_listeners("user.updated")
        assert len(matches) == 1  # Should match only "user.*"
        
        matches = matcher.find_matching_listeners("system.startup")
        assert len(matches) == 1  # Should match only "system.*"
        
        matches = matcher.find_matching_listeners("other.event")
        assert len(matches) == 0  # Should match nothing
        
        # Test caching
        cached_matches = matcher.find_matching_listeners("user.created")
        assert len(cached_matches) == 2  # Should return cached result
        
        # Test cache invalidation
        matcher.invalidate_cache()
        matches_after_invalidation = matcher.find_matching_listeners("user.created")
        assert len(matches_after_invalidation) == 2
        
        print("✅ EventPatternMatcher works correctly")
        return True
        
    except Exception as e:
        print(f"❌ EventPatternMatcher test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_manager():
    """Test the ListenerTaskManager functionality."""
    print("\nTesting ListenerTaskManager...")
    
    try:
        from plugginger._internal.runtime.dispatcher import ListenerTaskManager
        from plugginger._internal.runtime.fault_policy import FaultPolicyHandler
        
        # Create mock logger
        def mock_logger(msg: str) -> None:
            pass
        
        # Create fault policy handler
        fault_handler = FaultPolicyHandler(mock_logger)
        
        # Create task manager
        task_manager = ListenerTaskManager(fault_handler, mock_logger, 5.0)
        
        # Test initial state
        assert task_manager.get_active_task_count() == 0
        
        # Create test listener
        async def test_listener(event_data: dict[str, Any]) -> None:
            await asyncio.sleep(0.1)  # Simulate work
        
        # Test task execution
        async def run_test():
            matched_listeners = [("test.*", test_listener)]
            tasks = await task_manager.execute_listeners(
                matched_listeners, "test.event", {"data": "test"}
            )
            
            assert len(tasks) == 1
            assert task_manager.get_active_task_count() == 1
            
            # Wait for task to complete
            await asyncio.gather(*tasks)
            
            # Clean up completed tasks
            task_manager.cleanup_completed_tasks()
            assert task_manager.get_active_task_count() == 0
        
        # Run the async test
        asyncio.run(run_test())
        
        print("✅ ListenerTaskManager works correctly")
        return True
        
    except Exception as e:
        print(f"❌ ListenerTaskManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_event_dispatcher_integration():
    """Test the refactored EventDispatcher integration."""
    print("\nTesting EventDispatcher integration...")
    
    try:
        from plugginger._internal.runtime.dispatcher import EventDispatcher
        from plugginger._internal.runtime.fault_policy import FaultPolicyHandler
        
        # Create mock logger
        def mock_logger(msg: str) -> None:
            pass
        
        # Create fault policy handler
        fault_handler = FaultPolicyHandler(mock_logger)
        
        # Create event dispatcher
        dispatcher = EventDispatcher(
            fault_handler=fault_handler,
            logger=mock_logger,
            default_listener_timeout=5.0,
            max_concurrent_listener_tasks=10
        )
        
        # Test listener registration
        events_received = []
        
        async def test_listener(event_data: dict[str, Any]) -> None:
            events_received.append(event_data)
        
        dispatcher.add_listener("test.*", test_listener)
        
        # Test event emission
        async def run_test():
            await dispatcher.emit_event("test.event", {"message": "hello"})
            
            # Give tasks time to complete
            await asyncio.sleep(0.2)
            
            # Check that event was received
            assert len(events_received) == 1
            assert events_received[0]["message"] == "hello"
        
        # Run the async test
        asyncio.run(run_test())
        
        print("✅ EventDispatcher integration works correctly")
        return True
        
    except Exception as e:
        print(f"❌ EventDispatcher integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complexity_reduction():
    """Verify that the complexity has been reduced."""
    print("\nAnalyzing complexity reduction...")
    
    try:
        # Read the refactored implementation
        with open('src/plugginger/_internal/runtime/dispatcher.py', 'r') as f:
            content = f.read()
        
        # Find the emit_event method
        emit_start = content.find('async def emit_event(')
        if emit_start == -1:
            print("❌ Could not find emit_event method")
            return False
        
        # Find the end of the method (next method or end of class)
        next_method = content.find('\n    async def _handle_backpressure', emit_start)
        if next_method == -1:
            next_method = content.find('\n    async def shutdown', emit_start)
        if next_method == -1:
            next_method = len(content)
        
        emit_content = content[emit_start:next_method]
        
        # Count decision points (rough complexity estimate)
        if_count = emit_content.count('if ')
        elif_count = emit_content.count('elif ')
        for_count = emit_content.count('for ')
        while_count = emit_content.count('while ')
        try_count = emit_content.count('try:')
        await_count = emit_content.count('await ')
        
        # Estimate complexity (simplified)
        estimated_complexity = 1 + if_count + elif_count + for_count + while_count + try_count
        
        print(f"emit_event method complexity analysis:")
        print(f"  - if statements: {if_count}")
        print(f"  - elif statements: {elif_count}")
        print(f"  - for loops: {for_count}")
        print(f"  - while loops: {while_count}")
        print(f"  - try blocks: {try_count}")
        print(f"  - await calls: {await_count}")
        print(f"  - Estimated complexity: {estimated_complexity}")
        
        # Check that complexity is significantly reduced from original (12)
        if estimated_complexity <= 8:
            print("✅ emit_event complexity significantly reduced")
            return True
        else:
            print(f"❌ emit_event complexity still high: {estimated_complexity}")
            return False
        
    except Exception as e:
        print(f"❌ Complexity analysis failed: {e}")
        return False

def test_pattern_caching():
    """Test that pattern caching works correctly."""
    print("\nTesting pattern caching...")
    
    try:
        from plugginger._internal.runtime.dispatcher import EventPatternMatcher
        
        # Create test listeners
        async def listener1(event_data: dict[str, Any]) -> None:
            pass
        
        listeners_map = {
            "user.*": [listener1],
        }
        
        matcher = EventPatternMatcher(listeners_map)
        
        # First call should populate cache
        matches1 = matcher.find_matching_listeners("user.created")
        assert len(matches1) == 1
        
        # Second call should use cache
        matches2 = matcher.find_matching_listeners("user.created")
        assert len(matches2) == 1
        assert matches1 == matches2
        
        # Verify cache is working by checking internal state
        assert "user.created" in matcher._pattern_cache
        
        # Test cache invalidation
        matcher.invalidate_cache_for_pattern("user.*")
        assert len(matcher._pattern_cache) == 0
        
        print("✅ Pattern caching works correctly")
        return True
        
    except Exception as e:
        print(f"❌ Pattern caching test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=== TESTING REFACTORED EVENT DISPATCHER ===\n")
    
    tests = [
        test_component_imports,
        test_pattern_matcher,
        test_task_manager,
        test_event_dispatcher_integration,
        test_complexity_reduction,
        test_pattern_caching,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        if test():
            passed += 1
        else:
            failed += 1
        print()  # Add spacing
    
    print(f"=== RESULTS ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("🎉 All tests passed! Event dispatcher refactoring successful!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
