#!/usr/bin/env python3
"""Test the refactored DI container to ensure it works correctly."""

import sys
import os
from typing import Protocol

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_component_imports():
    """Test that all new components can be imported."""
    print("Testing component imports...")
    
    try:
        from plugginger.implementations.container import (
            ParameterInfo,
            ParameterAnalyzer,
            DependencyResolver,
            DIContainer,
        )
        print("✅ All DI container components imported successfully")
        return True
        
    except Exception as e:
        print(f"❌ Component imports failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_info():
    """Test the ParameterInfo dataclass."""
    print("\nTesting ParameterInfo...")
    
    try:
        from plugginger.implementations.container import ParameterInfo
        import inspect
        
        # Create test parameter info
        param_info = ParameterInfo(
            name="test_param",
            annotation=str,
            default=inspect.Parameter.empty,
            is_required=True,
            param_type=str
        )
        
        assert param_info.name == "test_param"
        assert param_info.param_type == str
        assert param_info.is_required == True
        
        print("✅ ParameterInfo works correctly")
        return True
        
    except Exception as e:
        print(f"❌ ParameterInfo test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_analyzer():
    """Test the ParameterAnalyzer functionality."""
    print("\nTesting ParameterAnalyzer...")
    
    try:
        from plugginger.implementations.container import ParameterAnalyzer
        
        # Create mock logger
        def mock_logger(msg: str) -> None:
            pass
        
        analyzer = ParameterAnalyzer(mock_logger)
        
        # Test class with dependencies
        class TestService:
            def __init__(self, dependency: str, optional_dep: int = 42) -> None:
                self.dependency = dependency
                self.optional_dep = optional_dep
        
        # Analyze constructor
        params = analyzer.analyze_constructor(TestService)
        
        assert len(params) == 2
        assert params[0].name == "dependency"
        assert params[0].param_type == str
        assert params[0].is_required == True
        
        assert params[1].name == "optional_dep"
        assert params[1].param_type == int
        assert params[1].is_required == False
        
        print("✅ ParameterAnalyzer works correctly")
        return True
        
    except Exception as e:
        print(f"❌ ParameterAnalyzer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dependency_resolver():
    """Test the DependencyResolver functionality."""
    print("\nTesting DependencyResolver...")
    
    try:
        from plugginger.implementations.container import (
            DependencyResolver,
            DIContainer,
            ParameterInfo
        )
        import inspect
        
        # Create container and register dependencies
        container = DIContainer()
        container.register_instance(str, "test_string")
        container.register_instance(int, 123)
        
        # Create resolver
        resolver = DependencyResolver(container)
        
        # Create test parameters
        params = [
            ParameterInfo(
                name="str_param",
                annotation=str,
                default=inspect.Parameter.empty,
                is_required=True,
                param_type=str
            ),
            ParameterInfo(
                name="int_param",
                annotation=int,
                default=42,
                is_required=False,
                param_type=int
            )
        ]
        
        # Resolve dependencies
        resolved = resolver.resolve_dependencies(params)
        
        assert "str_param" in resolved
        assert resolved["str_param"] == "test_string"
        assert "int_param" in resolved
        assert resolved["int_param"] == 123
        
        print("✅ DependencyResolver works correctly")
        return True
        
    except Exception as e:
        print(f"❌ DependencyResolver test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_di_container_integration():
    """Test the refactored DIContainer integration."""
    print("\nTesting DIContainer integration...")
    
    try:
        from plugginger.implementations.container import DIContainer
        
        # Create container
        container = DIContainer()
        
        # Define test interfaces and implementations
        class ITestService(Protocol):
            def get_value(self) -> str: ...
        
        class TestDependency:
            def get_data(self) -> str:
                return "dependency_data"
        
        class TestService:
            def __init__(self, dependency: TestDependency) -> None:
                self.dependency = dependency
            
            def get_value(self) -> str:
                return f"service_with_{self.dependency.get_data()}"
        
        # Register dependencies
        container.register(TestDependency, TestDependency)
        container.register(ITestService, TestService)
        
        # Get service (should auto-inject dependency)
        service = container.get(ITestService)
        
        assert service.get_value() == "service_with_dependency_data"
        print("✅ DIContainer integration works correctly")
        return True
        
    except Exception as e:
        print(f"❌ DIContainer integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complexity_reduction():
    """Verify that the complexity has been reduced."""
    print("\nTesting complexity reduction...")
    
    try:
        # Read the refactored implementation
        with open('src/plugginger/implementations/container.py', 'r') as f:
            content = f.read()
        
        # Find the _create_instance method
        create_start = content.find('def _create_instance(')
        if create_start == -1:
            print("❌ Could not find _create_instance method")
            return False
        
        # Find the end of the method
        next_method = content.find('\n    def has(', create_start)
        if next_method == -1:
            next_method = len(content)
        
        create_content = content[create_start:next_method]
        
        # Count decision points (rough complexity estimate)
        if_count = create_content.count('if ')
        elif_count = create_content.count('elif ')
        for_count = create_content.count('for ')
        while_count = create_content.count('while ')
        try_count = create_content.count('try:')
        except_count = create_content.count('except ')
        
        estimated_complexity = 1 + if_count + elif_count + for_count + while_count + try_count
        
        print(f"_create_instance method complexity analysis:")
        print(f"  - if statements: {if_count}")
        print(f"  - elif statements: {elif_count}")
        print(f"  - for loops: {for_count}")
        print(f"  - while loops: {while_count}")
        print(f"  - try blocks: {try_count}")
        print(f"  - except blocks: {except_count}")
        print(f"  - Estimated complexity: {estimated_complexity}")
        
        # Check that complexity is significantly reduced from original (12)
        if estimated_complexity <= 8:
            print("✅ _create_instance complexity significantly reduced")
            return True
        else:
            print(f"❌ _create_instance complexity still high: {estimated_complexity}")
            return False
        
    except Exception as e:
        print(f"❌ Complexity analysis failed: {e}")
        return False

def test_separation_of_concerns():
    """Test that concerns are properly separated."""
    print("\nTesting separation of concerns...")
    
    try:
        container_file = 'src/plugginger/implementations/container.py'
        with open(container_file, 'r') as f:
            content = f.read()
        
        # Check that ParameterAnalyzer exists
        assert 'class ParameterAnalyzer:' in content
        print("✅ ParameterAnalyzer class exists")
        
        # Check that DependencyResolver exists
        assert 'class DependencyResolver:' in content
        print("✅ DependencyResolver class exists")
        
        # Check that _create_instance delegates to components
        create_start = content.find('def _create_instance(')
        create_end = content.find('\n    def has(', create_start)
        create_content = content[create_start:create_end]
        
        assert 'self._parameter_analyzer.analyze_constructor' in create_content
        print("✅ _create_instance delegates to parameter analyzer")
        
        assert 'self._dependency_resolver.resolve_dependencies' in create_content
        print("✅ _create_instance delegates to dependency resolver")
        
        # Check that complex logic is not in _create_instance
        assert 'inspect.signature' not in create_content
        print("✅ Parameter analysis logic moved out of _create_instance")
        
        assert 'get_type_hints' not in create_content
        print("✅ Type hint logic moved out of _create_instance")
        
        return True
        
    except Exception as e:
        print(f"❌ Separation of concerns test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=== TESTING REFACTORED DI CONTAINER ===\n")
    
    tests = [
        test_component_imports,
        test_parameter_info,
        test_parameter_analyzer,
        test_dependency_resolver,
        test_di_container_integration,
        test_complexity_reduction,
        test_separation_of_concerns,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        if test():
            passed += 1
        else:
            failed += 1
        print()  # Add spacing
    
    print(f"=== RESULTS ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("🎉 All tests passed! DI container refactoring successful!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
