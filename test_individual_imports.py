#!/usr/bin/env python3
"""Test individual module imports to find circular import issues."""

import sys

def test_import(module_name, item_name=None):
    """Test importing a specific module or item."""
    try:
        if item_name:
            exec(f"from {module_name} import {item_name}")
            print(f"✅ {module_name}.{item_name}")
        else:
            exec(f"import {module_name}")
            print(f"✅ {module_name}")
        return True
    except Exception as e:
        print(f"❌ {module_name}{('.' + item_name) if item_name else ''}: {e}")
        return False

def main():
    """Test imports systematically."""
    print("=== INDIVIDUAL IMPORT TEST ===\n")
    
    # Test core modules first
    print("--- Core Modules ---")
    test_import("plugginger.core.types")
    test_import("plugginger.core.exceptions")
    test_import("plugginger.core.config")
    test_import("plugginger.core.constants")
    
    # Test internal modules
    print("\n--- Internal Modules ---")
    test_import("plugginger._internal.graph")
    test_import("plugginger._internal.proxy")
    test_import("plugginger._internal.validation")
    
    # Test implementations
    print("\n--- Implementation Modules ---")
    test_import("plugginger.implementations.container")
    test_import("plugginger.implementations.services")
    test_import("plugginger.implementations.events")
    
    # Test interfaces
    print("\n--- Interface Modules ---")
    test_import("plugginger.interfaces.services")
    test_import("plugginger.interfaces.events")
    
    # Test config
    print("\n--- Config Modules ---")
    test_import("plugginger.config.models")
    
    # Test API modules one by one
    print("\n--- API Modules (Individual) ---")
    test_import("plugginger.api.depends")
    test_import("plugginger.api.plugin")
    test_import("plugginger.api.service")
    test_import("plugginger.api.events")
    test_import("plugginger.api.background")
    test_import("plugginger.api.app_plugin")
    test_import("plugginger.api.builder")
    test_import("plugginger.api.app")
    
    # Test API package
    print("\n--- API Package ---")
    test_import("plugginger.api")
    
    # Test main package
    print("\n--- Main Package ---")
    test_import("plugginger")

if __name__ == "__main__":
    main()
