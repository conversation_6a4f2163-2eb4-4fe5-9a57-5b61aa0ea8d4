#!/usr/bin/env python3

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("Testing service decorator...")

try:
    from plugginger.api.service import service
    print("✓ Service decorator imported")
    
    from plugginger.api.plugin import PluginBase, plugin
    print("✓ Plugin base imported")
    
    # Test service decorator
    @plugin(name='test', version='1.0.0')
    class TestPlugin(PluginBase):
        def __init__(self, app, **injected_dependencies):
            super().__init__(app, **injected_dependencies)
        
        @service()
        async def test_service(self) -> str:
            return 'Hello'
    
    print("✓ Plugin with service defined")
    
    # Check service metadata
    service_method = TestPlugin.test_service
    metadata = getattr(service_method, '_plugginger_service_metadata', None)
    print(f"Service metadata: {metadata}")
    
    print("✓ All tests passed!")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
