#!/usr/bin/env python3
"""Simple test for the refactored validation without circular imports."""

import sys
import os
import inspect
from typing import Any

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_direct_validation():
    """Test validation components directly."""
    print("Testing direct validation imports...")
    
    try:
        # Import only the validation module directly
        sys.path.insert(0, os.path.join(src_path, 'plugginger', '_internal'))
        import validation
        
        # Test ValidationConfig
        config = validation.ValidationConfig()
        assert config.require_async == True
        assert config.context_name == "method"
        print("✅ ValidationConfig works")
        
        # Test ValidationProfiles
        service_profile = validation.ValidationProfiles.SERVICE_METHOD
        assert service_profile.context_name == "service method"
        print("✅ ValidationProfiles work")
        
        # Test validation function
        async def valid_service(self, param: str) -> str:
            return param
        
        # Should not raise
        validation.validate_method_signature(valid_service, service_profile)
        print("✅ validate_method_signature works")
        
        # Test invalid method
        def invalid_sync(self, param: str) -> str:
            return param
        
        try:
            validation.validate_method_signature(invalid_sync, service_profile)
            assert False, "Should have raised"
        except validation.ValidationError as e:
            assert "must be async" in str(e)
            print("✅ Validation correctly rejects invalid methods")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complexity_measurement():
    """Test that complexity has been reduced."""
    print("\nTesting complexity reduction...")
    
    try:
        # Read the validation file
        validation_file = os.path.join(src_path, 'plugginger', '_internal', 'validation.py')
        with open(validation_file, 'r') as f:
            content = f.read()
        
        # Find validate_method_signature function
        func_start = content.find('def validate_method_signature(')
        if func_start == -1:
            print("❌ Could not find validate_method_signature function")
            return False
        
        # Find the end of the function
        next_func = content.find('\ndef _validate_parameter_count', func_start)
        if next_func == -1:
            next_func = content.find('\n\n# Convenience functions', func_start)
        if next_func == -1:
            next_func = len(content)
        
        func_content = content[func_start:next_func]
        
        # Count decision points
        if_count = func_content.count('if ')
        elif_count = func_content.count('elif ')
        for_count = func_content.count('for ')
        try_count = func_content.count('try:')
        
        # Exclude helper function calls from complexity
        helper_calls = func_content.count('_validate_')
        
        estimated_complexity = 1 + if_count + elif_count + for_count + try_count - helper_calls
        
        print(f"validate_method_signature complexity analysis:")
        print(f"  - if statements: {if_count}")
        print(f"  - elif statements: {elif_count}")
        print(f"  - for loops: {for_count}")
        print(f"  - try blocks: {try_count}")
        print(f"  - helper calls: {helper_calls}")
        print(f"  - Estimated complexity: {estimated_complexity}")
        
        # Check that complexity is reduced from original (14)
        if estimated_complexity <= 8:
            print("✅ Function complexity significantly reduced")
            return True
        else:
            print(f"❌ Function complexity still high: {estimated_complexity}")
            return False
        
    except Exception as e:
        print(f"❌ Complexity measurement failed: {e}")
        return False

def test_helper_functions():
    """Test that helper functions work correctly."""
    print("\nTesting helper functions...")
    
    try:
        sys.path.insert(0, os.path.join(src_path, 'plugginger', '_internal'))
        import validation
        
        # Test parameter count validation
        async def test_method(self) -> None:
            pass
        
        config = validation.ValidationConfig(min_params=2)
        
        try:
            validation.validate_method_signature(test_method, config)
            assert False, "Should have raised"
        except validation.ValidationError as e:
            assert "at least 2 parameters" in str(e)
            print("✅ Parameter count validation works")
        
        # Test parameter type validation
        async def test_varargs(self, *args) -> None:
            pass
        
        config = validation.ValidationConfig(allow_varargs=False)
        
        try:
            validation.validate_method_signature(test_varargs, config)
            assert False, "Should have raised"
        except validation.ValidationError as e:
            assert "*args" in str(e)
            print("✅ Parameter type validation works")
        
        # Test return annotation validation
        async def test_no_return(self):  # No return annotation
            pass
        
        config = validation.ValidationConfig(require_return_annotation=True)
        
        try:
            validation.validate_method_signature(test_no_return, config)
            assert False, "Should have raised"
        except validation.ValidationError as e:
            assert "return type annotation" in str(e)
            print("✅ Return annotation validation works")
        
        return True
        
    except Exception as e:
        print(f"❌ Helper functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration_flexibility():
    """Test that the configuration system is flexible."""
    print("\nTesting configuration flexibility...")
    
    try:
        sys.path.insert(0, os.path.join(src_path, 'plugginger', '_internal'))
        import validation
        
        # Create custom configuration
        custom_config = validation.ValidationConfig(
            require_async=False,
            require_self=False,
            min_params=0,
            context_name="custom function"
        )
        
        # Test with a regular function
        def regular_function() -> str:
            return "test"
        
        # Should not raise with custom config
        validation.validate_method_signature(regular_function, custom_config)
        print("✅ Custom configuration works")
        
        # Test that it would fail with service config
        try:
            validation.validate_method_signature(regular_function, validation.ValidationProfiles.SERVICE_METHOD)
            assert False, "Should have raised"
        except validation.ValidationError:
            print("✅ Different configs produce different results")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration flexibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=== TESTING REFACTORED VALIDATION (SIMPLE) ===\n")
    
    tests = [
        test_direct_validation,
        test_complexity_measurement,
        test_helper_functions,
        test_configuration_flexibility,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        if test():
            passed += 1
        else:
            failed += 1
        print()  # Add spacing
    
    print(f"=== RESULTS ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("🎉 All tests passed! Method signature validation refactoring successful!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
