#!/usr/bin/env python3
"""Test script to verify the framework consolidation is working."""

import os
import sys
import traceback

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_basic_imports():
    """Test basic framework imports."""
    print("Testing basic imports...")

    try:
        # Test main module import
        print("✅ Main plugginger module imported")

        # Test core components
        print("✅ PluginBase imported")

        print("✅ plugin decorator imported")

        from plugginger import Depends
        print("✅ Depends imported")

        # Test that Depends is a pure data class
        dep = Depends("test_plugin")
        print(f"✅ Depends instance created: {dep}")

        # Verify no resolve method
        if hasattr(dep, 'resolve'):
            print("❌ ERROR: Depends still has resolve method")
            return False
        else:
            print("✅ Depends is pure data class (no resolve method)")

        # Test plugin identifier
        assert dep.plugin_identifier == "test_plugin"
        print("✅ Plugin identifier works correctly")

        return True

    except Exception as e:
        print(f"❌ Import test failed: {e}")
        traceback.print_exc()
        return False

def test_builder_import():
    """Test builder import."""
    print("\nTesting builder import...")

    try:
        from plugginger import PluggingerAppBuilder
        print("✅ PluggingerAppBuilder imported")

        # Try to create a builder
        PluggingerAppBuilder("test_app")
        print("✅ Builder instance created")

        return True

    except Exception as e:
        print(f"❌ Builder test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all consolidation tests."""
    print("=== PLUGGINGER FRAMEWORK CONSOLIDATION TEST ===\n")

    tests = [
        test_basic_imports,
        test_builder_import,
    ]

    passed = 0
    failed = 0

    for test in tests:
        if test():
            passed += 1
        else:
            failed += 1

    print("\n=== RESULTS ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")

    if failed == 0:
        print("🎉 All consolidation tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
