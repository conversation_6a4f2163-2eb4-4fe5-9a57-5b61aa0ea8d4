#!/usr/bin/env python3

import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("=== SIMPLE FRACTAL TEST ===")

try:
    print("Testing AppPluginBase import...")
    from plugginger.api.app_plugin import AppPluginBase
    print("✓ AppPluginBase imported successfully")

    print("Testing event bridge types...")
    print("✓ Event bridge types imported successfully")

    print("Testing builder with AppPlugin support...")
    print("✓ Builder imported successfully")

    print("Testing plugin decorators...")
    from plugginger.api.plugin import plugin
    print("✓ Plugin decorators imported successfully")

    # Test basic AppPlugin definition
    @plugin(name='test_app_plugin', version='1.0.0')
    class TestAppPlugin(AppPluginBase):
        def _configure_internal_app(self) -> None:
            # Minimal implementation for testing
            from plugginger.api.app import PluggingerAppInstance
            from plugginger.config.models import GlobalAppConfig

            # Create a minimal internal app
            config = GlobalAppConfig(app_name="test_internal")
            self._internal_app = PluggingerAppInstance(app_name="test_internal", global_config=config)

    print("✓ AppPlugin class defined successfully")

    print("✅ ALL FRACTAL IMPORTS SUCCESSFUL!")

except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
