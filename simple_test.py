#!/usr/bin/env python3

print("Starting simple test...")

try:
    print("Testing basic Python...")
    import sys
    print(f"Python version: {sys.version}")

    print("Adding path...")
    import os
    src_path = os.path.join(os.path.dirname(__file__), 'src')
    sys.path.insert(0, src_path)
    print(f"Added path: {src_path}")

    print("Testing core types...")
    print("✓ LoggerCallable imported successfully")

    print("Testing config...")
    print("✓ EventListenerFaultPolicy imported successfully")

    print("All tests passed!")

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
