import ast
import os
import sys
from collections import defaultdict
from pathlib import Path


def find_imports(file_path):
    with open(file_path, "r", encoding="utf-8") as file:
        try:
            tree = ast.parse(file.read(), filename=file_path)
        except SyntaxError:
            return []
    imports = []
    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(alias.name.split(".")[0])
        elif isinstance(node, ast.ImportFrom):
            if node.module:
                imports.append(node.module.split(".")[0])
    return imports


def build_import_graph(project_root):
    graph = defaultdict(set)
    module_files = {}
    for root, _, files in os.walk(project_root):
        for file in files:
            if file.endswith(".py"):
                full_path = os.path.join(root, file)
                relative_path = os.path.relpath(full_path, project_root)
                module_name = (
                    relative_path.replace("/", ".")
                    .replace("\\", ".")
                    .rstrip(".py")
                    .rstrip(".")
                )
                module_files[module_name] = full_path

    for module_name, path in module_files.items():
        imports = find_imports(path)
        for imp in imports:
            if imp in module_files:
                graph[module_name].add(imp)
    return graph


def detect_cycles(graph):
    visited = set()
    stack = []
    cycles = []

    def visit(node, path):
        if node in path:
            cycle = path[path.index(node):] + [node]
            cycles.append(cycle)
            return
        if node in visited:
            return
        visited.add(node)
        path.append(node)
        for neighbor in list(graph[node]):
            visit(neighbor, path)
        path.pop()

    for node in list(graph):
        visit(node, [])

    return cycles


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python circular_import_checker.py <project_root>")
        sys.exit(1)
    project_root = sys.argv[1]
    graph = build_import_graph(project_root)
    cycles = detect_cycles(graph)
    if not cycles:
        print("No circular imports detected.")
    else:
        print("Circular imports detected:")
        for cycle in cycles:
            print(" -> ".join(cycle))
