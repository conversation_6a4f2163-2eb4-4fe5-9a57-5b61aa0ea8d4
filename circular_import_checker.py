#! /usr/bin/env python3

"""
Ein minimales, fokussiertes Tool zur Erkennung zirkulärer Imports in Python-Projekten.
Verwendet rein `modulefinder` und einen internen Dependency-Graph.
"""

import os
import sys
import ast
from collections import defaultdict, deque


def find_python_files(directory):
    for root, _, files in os.walk(directory):
        for f in files:
            if f.endswith(".py"):
                yield os.path.join(root, f)


def extract_imports(filepath):
    with open(filepath, "r", encoding="utf-8") as f:
        tree = ast.parse(f.read(), filename=filepath)

    module_name = os.path.relpath(filepath).replace(os.sep, ".")[:-3]  # remove .py
    imports = set()

    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.add(alias.name.split(".")[0])
        elif isinstance(node, ast.ImportFrom):
            if node.module:
                imports.add(node.module.split(".")[0])

    return module_name, imports


def build_dependency_graph(source_dir):
    graph = defaultdict(set)
    file_map = {}
    for filepath in find_python_files(source_dir):
        module_name, imports = extract_imports(filepath)
        graph[module_name].update(imports)
        file_map[module_name] = filepath
    return graph, file_map


def detect_cycles(graph):
    visited = set()
    stack = set()
    path = []
    cycles = []

    def visit(node):
        if node in stack:
            idx = path.index(node)
            cycles.append(path[idx:] + [node])
            return
        if node in visited:
            return

        visited.add(node)
        stack.add(node)
        path.append(node)

        for neighbor in graph[node]:
            visit(neighbor)

        stack.remove(node)
        path.pop()

    for node in graph:
        visit(node)

    return cycles


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Find circular imports in a Python project.")
    parser.add_argument("path", help="Project root to scan")
    args = parser.parse_args()

    graph, filemap = build_dependency_graph(args.path)
    cycles = detect_cycles(graph)

    if not cycles:
        print("✅ No circular imports detected.")
    else:
        print("🚨 Circular import cycles found:")
        for cycle in cycles:
            print("  -> ".join(cycle))
