#!/usr/bin/env python3

import sys
import os
import asyncio

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.api.events import on_event
from plugginger.config.models import GlobalAppConfig
from pydantic import BaseModel

# Test Plugin mit Service und Event
@plugin(name='test_plugin', version='1.0.0')
class TestPlugin(PluginBase):
    def __init__(self, app, **injected_dependencies):
        super().__init__(app, **injected_dependencies)
    
    @service()
    async def hello_service(self) -> str:
        return 'Hello from V6.0!'
    
    @on_event('test.event')
    async def handle_test_event(self, event_data: dict) -> None:
        print(f'Event received: {event_data}')
    
    async def setup(self, plugin_config: BaseModel) -> None:
        print('TestPlugin setup completed')
    
    async def teardown(self) -> None:
        print('TestPlugin teardown completed')

async def main():
    print('=== V6.0 VOLLSTÄNDIGER BUILD-TEST ===')

    try:
        builder = PluggingerAppBuilder('v6_test_app')
        builder.include(TestPlugin)

        config = GlobalAppConfig(app_name='v6_test_app')
        app = builder.build(config)

        print('✓ Build erfolgreich!')
        print(f'App Name: {app.app_name}')
        print(f'Runtime Facade: {app.get_runtime_facade() is not None}')
        print(f'Services: {app.list_services()}')
        print(f'Event Patterns: {app.list_event_patterns()}')

        # Test Plugin-Zugriff
        plugin_instance = app.get_plugin_instance('v6_test_app:test_plugin')
        print(f'Plugin gefunden: {plugin_instance is not None}')
        if plugin_instance:
            print(f'Plugin ID: {plugin_instance._plugginger_instance_id}')

        # Test Lifecycle
        print('\n=== LIFECYCLE TEST ===')
        await app.run(main_coroutine=lambda: print('✓ App läuft'))
        
    except Exception as e:
        print(f'✗ Fehler: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
