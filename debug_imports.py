#!/usr/bin/env python3

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("=== IMPORT DEBUGGING ===")

try:
    print("Testing core imports...")
    from plugginger.core.types import LoggerCallable
    print("✓ LoggerCallable imported")
    
    from plugginger.config.models import GlobalAppConfig
    print("✓ GlobalAppConfig imported")
    
    print("Testing runtime facade...")
    from plugginger._internal.runtime_facade import RuntimeFacade
    print("✓ RuntimeFacade imported")
    
    print("Testing proxy...")
    from plugginger._internal.proxy import GenericPluginProxy
    print("✓ GenericPluginProxy imported")
    
    print("Testing builder...")
    from plugginger.api.builder import PluggingerAppBuilder
    print("✓ PluggingerAppBuilder imported")
    
    print("Testing app...")
    from plugginger.api.app import PluggingerAppInstance
    print("✓ PluggingerAppInstance imported")
    
    print("Testing plugin API...")
    from plugginger.api.plugin import PluginBase, plugin
    print("✓ Plugin API imported")
    
    print("✅ ALL IMPORTS SUCCESSFUL!")
    
except Exception as e:
    print(f"✗ Import failed: {e}")
    import traceback
    traceback.print_exc()
