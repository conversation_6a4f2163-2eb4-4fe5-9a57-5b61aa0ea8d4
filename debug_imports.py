#!/usr/bin/env python3

import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("=== IMPORT DEBUGGING ===")

try:
    print("Testing core imports...")
    print("✓ LoggerCallable imported")

    print("✓ GlobalAppConfig imported")

    print("Testing runtime facade...")
    print("✓ RuntimeFacade imported")

    print("Testing proxy...")
    print("✓ GenericPluginProxy imported")

    print("Testing builder...")
    print("✓ PluggingerAppBuilder imported")

    print("Testing app...")
    print("✓ PluggingerAppInstance imported")

    print("Testing plugin API...")
    print("✓ Plugin API imported")

    print("✅ ALL IMPORTS SUCCESSFUL!")

except Exception as e:
    print(f"✗ Import failed: {e}")
    import traceback
    traceback.print_exc()
