#!/usr/bin/env python3

import asyncio
import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_minimal():
    print("=== MINIMAL V6.0 TEST ===")

    try:

        from plugginger.api.builder import PluggingerAppBuilder
        from plugginger.api.plugin import PluginBase, plugin
        from plugginger.api.service import service
        from plugginger.config.models import GlobalAppConfig

        print("✓ Imports successful")

        @plugin(name='minimal_plugin', version='1.0.0')
        class MinimalPlugin(PluginBase):
            def __init__(self, app, **injected_dependencies):
                super().__init__(app, **injected_dependencies)

            @service()
            async def hello(self) -> str:
                return 'Hello V6.0!'

        print("✓ Plugin defined")

        builder = PluggingerAppBuilder('minimal_app')
        builder.include(MinimalPlugin)

        print("✓ Builder configured")

        config = GlobalAppConfig(app_name='minimal_app')
        app = builder.build(config)

        print("✓ App built successfully!")
        print(f"Services: {app.list_services()}")

        # Test service call if available
        services = app.list_services()
        if services:
            result = await app.call_service(services[0])
            print(f"Service result: {result}")

        print("✓ Test completed successfully!")

    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_minimal())
