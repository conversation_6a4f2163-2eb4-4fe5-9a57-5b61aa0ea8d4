#!/usr/bin/env python3

import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("=== SIMPLE CLI TEST ===")

try:
    print("Testing core constants...")
    from plugginger.core.constants import SERVICE_METADATA_KEY
    print(f"✓ SERVICE_METADATA_KEY: {SERVICE_METADATA_KEY}")

    print("Testing plugin base...")
    print("✓ PluginBase imported")

    print("Testing stub generator...")
    print("✓ Stub generator imported")

    print("Testing CLI utilities...")
    print("✓ CLI utilities imported")

    print("Testing mock app...")
    from plugginger.testing.mock_app import create_mock_app
    mock_app = create_mock_app("TestApp")
    print(f"✓ Mock app created: {mock_app.app_name}")

    print("Testing collectors...")
    from plugginger.testing.collectors import EventCollector
    collector = EventCollector()
    print("✓ Event collector created")

    print("Testing helpers...")
    print("✓ Plugin test runner imported")

    print("\n✅ ALL SIMPLE CLI TESTS PASSED!")

except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
