#!/usr/bin/env python3
"""Comprehensive test of the consolidated framework."""

import sys
import os

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_all_public_api_imports() -> bool:
    """Test all public API imports."""
    print("Testing all public API imports...")

    try:
        from plugginger import (
            PluginBase, AppPluginBase, plugin, service, on_event, background_task,
            Depends, PluggingerAppBuilder, PluggingerAppInstance, GlobalAppConfig,
            PluggingerError, PluginRegistrationError, ServiceDefinitionError,
            EventDefinitionError, DependencyError, MissingDependencyError,
            CircularDependencyError, ConfigurationError
        )
        print("✅ All public API imports successful")
        return True
    except Exception as e:
        print(f"❌ Public API import failed: {e}")
        return False

def test_depends_functionality() -> bool:
    """Test Depends class functionality."""
    print("Testing Depends functionality...")

    try:
        from plugginger import Depends

        # Test basic creation
        dep1 = Depends("plugin1")
        assert dep1.dependency == "plugin1"
        assert not dep1.optional
        assert dep1.version_constraint is None

        # Test with options
        dep2 = Depends("plugin2", optional=True, version_constraint=">=1.0.0")
        assert dep2.dependency == "plugin2"
        assert dep2.optional
        assert dep2.version_constraint == ">=1.0.0"

        # Test plugin identifier
        assert dep1.plugin_identifier == "plugin1"
        assert dep2.plugin_identifier == "plugin2"

        # Verify no resolve method
        assert not hasattr(dep1, 'resolve')
        assert not hasattr(dep2, 'resolve')

        print("✅ Depends functionality works correctly")
        return True
    except Exception as e:
        print(f"❌ Depends test failed: {e}")
        return False

def test_plugin_creation() -> bool:
    """Test plugin creation with the new API."""
    print("Testing plugin creation...")

    try:
        from plugginger import PluginBase, plugin, Depends

        @plugin(name="test_plugin", version="1.0.0")
        class TestPlugin(PluginBase):
            needs = [Depends("database")]

            def __init__(self, **injected_dependencies):
                super().__init__(**injected_dependencies)

        # Check metadata
        assert hasattr(TestPlugin, '_plugginger_plugin_name')
        assert TestPlugin._plugginger_plugin_name == "test_plugin"
        assert TestPlugin._plugginger_plugin_version == "1.0.0"

        print("✅ Plugin creation works correctly")
        return True
    except Exception as e:
        print(f"❌ Plugin creation test failed: {e}")
        return False

def test_builder_functionality() -> bool:
    """Test builder functionality."""
    print("Testing builder functionality...")

    try:
        from plugginger import PluggingerAppBuilder, GlobalAppConfig

        # Create builder
        builder = PluggingerAppBuilder("test_app")

        # Check builder properties
        assert builder.app_name == "test_app"

        # Test GlobalAppConfig can be created
        config = GlobalAppConfig(
            app_name="test_app",
            max_fractal_depth=5
        )
        assert config.app_name == "test_app"

        print("✅ Builder functionality works correctly")
        return True
    except Exception as e:
        print(f"❌ Builder test failed: {e}")
        return False

def main() -> int:
    """Run all comprehensive tests."""
    print("=== COMPREHENSIVE FRAMEWORK TEST ===\n")

    tests = [
        test_all_public_api_imports,
        test_depends_functionality,
        test_plugin_creation,
        test_builder_functionality,
    ]

    passed = 0
    failed = 0

    for test in tests:
        if test():
            passed += 1
        else:
            failed += 1
        print()  # Add spacing

    print(f"=== RESULTS ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")

    if failed == 0:
        print("🎉 All comprehensive tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
