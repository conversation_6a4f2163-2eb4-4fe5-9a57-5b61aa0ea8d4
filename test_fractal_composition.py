#!/usr/bin/env python3

import sys
import os
import asyncio

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_fractal_composition():
    print("=== FRACTAL COMPOSITION TEST ===")
    
    try:
        # Test imports
        from plugginger.api.builder import PluggingerAppBuilder
        from plugginger.api.plugin import PluginBase, plugin
        from plugginger.api.app_plugin import AppPluginBase
        from plugginger.api.service import service
        from plugginger.api.events import on_event
        from plugginger.config.models import GlobalAppConfig
        from pydantic import BaseModel
        
        print("✓ All imports successful")
        
        # Define a simple internal plugin
        @plugin(name='internal_plugin', version='1.0.0')
        class InternalPlugin(PluginBase):
            def __init__(self, app, **injected_dependencies):
                super().__init__(app, **injected_dependencies)
            
            @service()
            async def internal_service(self) -> str:
                return 'Hello from internal app!'
            
            @on_event('internal.test')
            async def handle_internal_event(self, event_data: dict) -> None:
                print(f'Internal event received: {event_data}')
        
        print("✓ Internal plugin defined")
        
        # Define an AppPlugin that contains the internal plugin
        @plugin(name='test_app_plugin', version='1.0.0')
        class TestAppPlugin(AppPluginBase):
            def _configure_internal_app(self) -> None:
                # Create internal app builder with correct fractal depth
                internal_builder = PluggingerAppBuilder(
                    app_name=f"{getattr(self, '_plugginger_instance_id', 'unknown')}_internal",
                    parent_app_plugin_context=self,
                    _current_depth=self.app._current_build_depth_for_sub_apps + 1,
                    _max_depth_from_config=self.app._max_build_depth_for_sub_apps
                )
                
                # Add internal plugin
                internal_builder.include(InternalPlugin)
                
                # Build internal app
                self._internal_app = internal_builder.build()
            
            def _configure_event_bridges(self) -> None:
                # Configure event bridges
                self.bridge_internal_event_pattern("internal.*", "app_plugin")
                self.bridge_external_event_to_internal("external.test", "internal.test")
        
        print("✓ AppPlugin defined")
        
        # Build outer app with AppPlugin
        outer_builder = PluggingerAppBuilder('fractal_test_app')
        outer_builder.include_app(TestAppPlugin, as_plugin_name='test_app_plugin')
        
        print("✓ Outer builder configured")
        
        config = GlobalAppConfig(app_name='fractal_test_app', max_fractal_depth=5)
        outer_app = outer_builder.build(config)
        
        print("✓ Fractal app built successfully!")
        print(f"Outer app name: {outer_app.app_name}")
        print(f"Services: {outer_app.list_services()}")
        print(f"Event patterns: {outer_app.list_event_patterns()}")
        
        # Test AppPlugin access
        app_plugin = outer_app.get_plugin_instance('fractal_test_app:test_app_plugin')
        print(f"AppPlugin found: {app_plugin is not None}")
        if app_plugin:
            print(f"AppPlugin ID: {app_plugin._plugginger_instance_id}")
            print(f"Internal app: {app_plugin._internal_app is not None}")
            if app_plugin._internal_app:
                print(f"Internal app name: {app_plugin._internal_app.app_name}")
                print(f"Internal services: {app_plugin._internal_app.list_services()}")
        
        # Test lifecycle
        print("\n=== FRACTAL LIFECYCLE TEST ===")
        await outer_app.start_all_plugins()
        print("✓ All plugins started (including internal)")
        
        # Test event bridging
        print("\n=== EVENT BRIDGING TEST ===")
        await outer_app.emit_event('external.test', {'message': 'Test from outer app'})
        print("✓ External event emitted")
        
        await outer_app.stop_all_plugins()
        print("✓ All plugins stopped")
        
        print("\n✅ FRACTAL COMPOSITION TEST PASSED!")
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_fractal_composition())
