#!/usr/bin/env python3

import asyncio
import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_v6_finalized():
    print("=== V6.0 FINALIZED IMPLEMENTATION TEST ===")

    try:
        from pydantic import BaseModel

        from plugginger.api.builder import PluggingerAppBuilder
        from plugginger.api.events import on_event
        from plugginger.api.plugin import PluginBase, plugin
        from plugginger.api.service import service
        from plugginger.config.models import GlobalAppConfig

        print("✓ All imports successful")

        # Test Plugin mit Service und Event
        @plugin(name='finalized_test_plugin', version='1.0.0')
        class FinalizedTestPlugin(PluginBase):
            def __init__(self, app, **injected_dependencies):
                super().__init__(app, **injected_dependencies)
                print(f"FinalizedTestPlugin initialized with ID: {getattr(self, '_plugginger_instance_id', 'NOT_SET')}")

            @service()
            async def hello_service(self) -> str:
                return 'Hello from V6.0 Finalized!'

            @on_event('test.event')
            async def handle_test_event(self, event_data: dict) -> None:
                print(f'Event received: {event_data}')

            async def setup(self, plugin_config: BaseModel) -> None:
                print('FinalizedTestPlugin setup completed')

            async def teardown(self) -> None:
                print('FinalizedTestPlugin teardown completed')

        print("✓ Plugin defined")

        # Build App
        builder = PluggingerAppBuilder('finalized_test_app')
        builder.include(FinalizedTestPlugin)

        print("✓ Builder configured")

        config = GlobalAppConfig(app_name='finalized_test_app')
        app = builder.build(config)

        print("✓ App built successfully!")
        print(f"App Name: {app.app_name}")
        print(f"Runtime Facade: {app.get_runtime_facade() is not None}")
        print(f"Services: {app.list_services()}")
        print(f"Event Patterns: {app.list_event_patterns()}")

        # Test Plugin-Zugriff
        plugin_instance = app.get_plugin_instance('finalized_test_app:finalized_test_plugin')
        print(f"Plugin gefunden: {plugin_instance is not None}")
        if plugin_instance:
            print(f"Plugin ID: {plugin_instance._plugginger_instance_id}")

        # Test Service-Aufruf
        services = app.list_services()
        if services:
            print(f"Testing service: {services[0]}")
            result = await app.call_service(services[0])
            print(f"Service result: {result}")

        # Test Event-Emission
        patterns = app.list_event_patterns()
        if patterns:
            print(f"Testing event: {patterns[0]}")
            await app.emit_event('test.event', {'message': 'Test event data'})

        # Test Lifecycle
        print("\n=== LIFECYCLE TEST ===")
        await app.start_all_plugins()
        print("✓ Plugins started")

        await app.stop_all_plugins()
        print("✓ Plugins stopped")

        print("\n✅ ALL TESTS PASSED!")

    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_v6_finalized())
