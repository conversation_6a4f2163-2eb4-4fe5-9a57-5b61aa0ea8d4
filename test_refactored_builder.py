#!/usr/bin/env python3
"""Test the refactored PluggingerAppBuilder to ensure it works correctly."""

import sys
import os

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_builder_phases_import():
    """Test that all builder phase classes can be imported."""
    print("Testing builder phases imports...")
    
    try:
        from plugginger._internal.builder_phases import (
            AppConfigResolver,
            DependencyOrchestrator,
            InterfaceRegistrar,
            PluginInstantiator,
        )
        print("✅ All builder phase classes imported successfully")
        return True
        
    except Exception as e:
        print(f"❌ Builder phases import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_builder_initialization():
    """Test that the refactored builder initializes correctly."""
    print("\nTesting builder initialization...")
    
    try:
        from plugginger import PluggingerAppBuilder
        
        # Create builder
        builder = PluggingerAppBuilder("test_app")
        
        # Check that helper classes are initialized
        assert hasattr(builder, '_config_resolver')
        assert hasattr(builder, '_dependency_orchestrator')
        assert hasattr(builder, '_plugin_instantiator')
        assert hasattr(builder, '_interface_registrar')
        
        print("✅ Builder initialization with helper classes successful")
        return True
        
    except Exception as e:
        print(f"❌ Builder initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_resolver():
    """Test the AppConfigResolver functionality."""
    print("\nTesting AppConfigResolver...")
    
    try:
        from plugginger._internal.builder_phases import AppConfigResolver
        from plugginger.config.models import GlobalAppConfig
        
        resolver = AppConfigResolver()
        
        # Test with None input
        config = resolver.resolve_and_validate("test_app", 5, None)
        assert config.app_name == "test_app"
        assert config.max_fractal_depth == 5
        
        # Test with dict input
        config_dict = {"log_level": "DEBUG"}
        config = resolver.resolve_and_validate("test_app", 5, config_dict)
        assert config.app_name == "test_app"
        assert config.log_level == "DEBUG"
        
        # Test with GlobalAppConfig input
        existing_config = GlobalAppConfig(app_name="other_name", max_fractal_depth=3)
        config = resolver.resolve_and_validate("test_app", 5, existing_config)
        assert config.app_name == "test_app"  # Builder name is authoritative
        
        print("✅ AppConfigResolver functionality works correctly")
        return True
        
    except Exception as e:
        print(f"❌ AppConfigResolver test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_plugin_build():
    """Test building a simple plugin with the refactored builder."""
    print("\nTesting simple plugin build...")
    
    try:
        from plugginger import PluggingerAppBuilder, PluginBase, plugin, service
        
        @plugin(name="test_plugin", version="1.0.0")
        class TestPlugin(PluginBase):
            @service
            async def greet(self, name: str) -> str:
                return f"Hello, {name}!"
        
        # Build app
        builder = PluggingerAppBuilder("test_app")
        builder.include(TestPlugin)
        app = builder.build()
        
        # Verify app was built
        assert app.app_name == "test_app"
        print("✅ Simple plugin build successful")
        return True
        
    except Exception as e:
        print(f"❌ Simple plugin build failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complexity_reduction():
    """Verify that the build method complexity has been reduced."""
    print("\nAnalyzing build method complexity...")
    
    try:
        # Read the refactored implementation
        with open('src/plugginger/api/builder.py', 'r') as f:
            content = f.read()
        
        # Find the build method
        build_method_start = content.find('def build(')
        if build_method_start == -1:
            print("❌ Could not find build method")
            return False
        
        # Extract build method (rough approximation)
        build_method_end = content.find('\n    def ', build_method_start + 1)
        if build_method_end == -1:
            build_method_end = len(content)
        
        build_method = content[build_method_start:build_method_end]
        
        # Count decision points (rough complexity estimate)
        if_count = build_method.count('if ')
        elif_count = build_method.count('elif ')
        for_count = build_method.count('for ')
        while_count = build_method.count('while ')
        try_count = build_method.count('try:')
        
        estimated_complexity = 1 + if_count + elif_count + for_count + while_count + try_count
        
        print(f"Build method complexity analysis:")
        print(f"  - if statements: {if_count}")
        print(f"  - elif statements: {elif_count}")
        print(f"  - for loops: {for_count}")
        print(f"  - while loops: {while_count}")
        print(f"  - try blocks: {try_count}")
        print(f"  - Estimated complexity: {estimated_complexity}")
        
        # Check that complexity is significantly reduced
        if estimated_complexity <= 8:
            print("✅ Build method complexity significantly reduced")
            return True
        else:
            print(f"❌ Build method complexity still high: {estimated_complexity}")
            return False
        
    except Exception as e:
        print(f"❌ Complexity analysis failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=== TESTING REFACTORED BUILDER ===\n")
    
    tests = [
        test_builder_phases_import,
        test_builder_initialization,
        test_config_resolver,
        test_simple_plugin_build,
        test_complexity_reduction,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        if test():
            passed += 1
        else:
            failed += 1
        print()  # Add spacing
    
    print(f"=== RESULTS ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("🎉 All tests passed! Builder refactoring successful!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
