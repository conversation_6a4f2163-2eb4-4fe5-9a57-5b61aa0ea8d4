#!/usr/bin/env python3

import sys
import os
import asyncio

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.config.models import GlobalAppConfig
from pydantic import BaseModel

# Test Plugin mit Service
@plugin(name='service_test_plugin', version='1.0.0')
class ServiceTestPlugin(PluginBase):
    def __init__(self, app, **injected_dependencies):
        super().__init__(app, **injected_dependencies)
        print(f"ServiceTestPlugin initialized with ID: {getattr(self, '_plugginger_instance_id', 'NOT_SET')}")
    
    @service()
    async def test_service(self) -> str:
        return 'Service working!'
    
    async def setup(self, plugin_config: BaseModel) -> None:
        print('ServiceTestPlugin setup completed')
        # Debug: Check if service method has metadata
        service_method = getattr(self, 'test_service')
        print(f"Service method metadata: {getattr(service_method, '_plugginger_service_metadata', 'NO_METADATA')}")

async def main():
    print('=== SERVICE REGISTRATION DEBUG TEST ===')

    try:
        builder = PluggingerAppBuilder('service_test_app')
        builder.include(ServiceTestPlugin)

        config = GlobalAppConfig(app_name='service_test_app')
        app = builder.build(config)

        print('✓ Build erfolgreich!')
        print(f'Services vor Setup: {app.list_services()}')

        # Setup plugins
        await app._runtime_facade.setup_all_plugins()
        print(f'Services nach Setup: {app.list_services()}')

        # Test Plugin-Zugriff
        plugin_instance = app.get_plugin_instance('service_test_app:service_test_plugin')
        print(f'Plugin gefunden: {plugin_instance is not None}')
        if plugin_instance:
            print(f'Plugin ID: {plugin_instance._plugginger_instance_id}')
            print(f'Plugin type: {type(plugin_instance)}')

        # Test Service-Aufruf
        if app.list_services():
            service_name = app.list_services()[0]
            print(f'Teste Service: {service_name}')
            result = await app.call_service(service_name)
            print(f'Service-Ergebnis: {result}')
        else:
            print('Keine Services gefunden!')

        # Cleanup
        await app._runtime_facade.teardown_all_plugins()
        await app._runtime_facade.shutdown()
        
    except Exception as e:
        print(f'✗ Fehler: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
