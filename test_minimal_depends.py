#!/usr/bin/env python3
"""Minimal test for Depends class without any imports."""

import os
import sys

# Add src to path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

print("Testing minimal Depends...")

try:
    print("Step 1: Testing core.exceptions...")
    from plugginger.core.exceptions import DependencyError
    print("✅ DependencyError imported")

    print("Step 2: Testing Depends class...")

    # Create a minimal Depends class inline to test the concept
    class TestDepends:
        def __init__(self, dependency: str, *, optional: bool = False, version_constraint: str | None = None) -> None:
            if not isinstance(dependency, str):
                raise DependencyError(f"Dependency must be a string plugin name, got {type(dependency)}")

            self.dependency = dependency
            self.optional = optional
            self.version_constraint = version_constraint

        @property
        def plugin_identifier(self) -> str:
            return self.dependency

        def __repr__(self) -> str:
            return f"TestDepends({self.dependency!r}, optional={self.optional})"

    # Test the class
    dep = TestDepends("test_plugin")
    print(f"✅ TestDepends created: {dep}")
    print(f"✅ Plugin identifier: {dep.plugin_identifier}")

    print("All minimal tests passed!")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
