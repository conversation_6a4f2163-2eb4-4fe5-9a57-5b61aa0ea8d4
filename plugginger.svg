<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.1 (20241206.2353)
 -->
<!-- Title: G Pages: 1 -->
<svg width="2696pt" height="3025pt"
 viewBox="0.00 0.00 2695.88 3025.08" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 3021.08)">
<title>G</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="white" stroke="none" points="-4,4 -4,-3021.08 2691.88,-3021.08 2691.88,4 -4,4"/>
<!-- packaging -->
<g id="node1" class="node">
<title>packaging</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b65353" stroke="black" cx="192.44" cy="-1687.46" rx="40.08" ry="18"/>
<text text-anchor="middle" x="192.44" y="-1684.34" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging</text>
</g>
<!-- plugginger__internal_validation -->
<g id="node16" class="node">
<title>plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="398.44,-1415.38 328.44,-1415.38 328.44,-1369.13 398.44,-1369.13 398.44,-1415.38"/>
<text text-anchor="middle" x="363.44" y="-1401.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="363.44" y="-1389.13" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="363.44" y="-1376.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">validation</text>
</g>
<!-- packaging&#45;&gt;plugginger__internal_validation -->
<g id="edge1" class="edge">
<title>packaging&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M208.25,-1670.73C224.21,-1653.83 248.19,-1625.43 260.44,-1596.07"/>
</g>
<!-- packaging__structures -->
<g id="node2" class="node">
<title>packaging__structures</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b65353" stroke="black" cx="124.44" cy="-1946.61" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="124.44" y="-1949.86" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="124.44" y="-1937.11" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">_structures</text>
</g>
<!-- packaging_version -->
<g id="node5" class="node">
<title>packaging_version</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#cc3333" stroke="black" cx="124.44" cy="-1863.23" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="124.44" y="-1866.48" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="124.44" y="-1853.73" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">version</text>
</g>
<!-- packaging__structures&#45;&gt;packaging_version -->
<g id="edge2" class="edge">
<title>packaging__structures&#45;&gt;packaging_version</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M124.44,-1922.51C124.44,-1915.12 124.44,-1906.77 124.44,-1898.75"/>
<polygon fill="#b65353" stroke="black" points="127.94,-1898.9 124.44,-1888.9 120.94,-1898.9 127.94,-1898.9"/>
</g>
<!-- packaging_specifiers -->
<g id="node3" class="node">
<title>packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#ac4949" stroke="black" cx="48.44" cy="-1687.46" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="48.44" y="-1690.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="48.44" y="-1677.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">specifiers</text>
</g>
<!-- packaging_specifiers&#45;&gt;plugginger__internal_validation -->
<g id="edge3" class="edge">
<title>packaging_specifiers&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M87.63,-1673.07C146.93,-1652.32 253.35,-1613.06 260.44,-1596.07"/>
<path fill="none" stroke="black" d="M260.44,-1594.07C286.06,-1532.66 323.01,-1464.35 344.99,-1425.33"/>
<polygon fill="#ac4949" stroke="black" points="347.87,-1427.33 349.76,-1416.91 341.78,-1423.88 347.87,-1427.33"/>
</g>
<!-- packaging_utils -->
<g id="node4" class="node">
<title>packaging_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b34c4c" stroke="black" cx="124.44" cy="-1779.85" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="124.44" y="-1783.1" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="124.44" y="-1770.35" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">utils</text>
</g>
<!-- packaging_utils&#45;&gt;packaging_specifiers -->
<g id="edge4" class="edge">
<title>packaging_utils&#45;&gt;packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M106.43,-1757.43C96.58,-1745.73 84.24,-1731.04 73.47,-1718.24"/>
<polygon fill="#b34c4c" stroke="black" points="76.41,-1716.29 67.29,-1710.89 71.05,-1720.8 76.41,-1716.29"/>
</g>
<!-- packaging_version&#45;&gt;packaging_specifiers -->
<g id="edge5" class="edge">
<title>packaging_version&#45;&gt;packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M99.48,-1842.76C87.9,-1832.38 75.05,-1818.63 67.44,-1803.54 54.72,-1778.33 50.22,-1746.44 48.76,-1722.63"/>
<polygon fill="#cc3333" stroke="black" points="52.26,-1722.67 48.32,-1712.83 45.27,-1722.98 52.26,-1722.67"/>
</g>
<!-- packaging_version&#45;&gt;packaging_utils -->
<g id="edge6" class="edge">
<title>packaging_version&#45;&gt;packaging_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M124.44,-1839.13C124.44,-1831.74 124.44,-1823.4 124.44,-1815.38"/>
<polygon fill="#cc3333" stroke="black" points="127.94,-1815.53 124.44,-1805.53 120.94,-1815.53 127.94,-1815.53"/>
</g>
<!-- packaging_version&#45;&gt;plugginger__internal_validation -->
<g id="edge7" class="edge">
<title>packaging_version&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M147.73,-1842.37C175.51,-1817.14 220.77,-1770.75 241.44,-1720.17 262.54,-1668.51 228.28,-1641.67 260.44,-1596.07"/>
</g>
<!-- plugginger -->
<g id="node6" class="node">
<title>plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1064.31,-1299.83 996.56,-1299.83 996.56,-1263.83 1064.31,-1263.83 1064.31,-1299.83"/>
<text text-anchor="middle" x="1030.44" y="-1278.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger</text>
</g>
<!-- plugginger__internal -->
<g id="node7" class="node">
<title>plugginger__internal</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85f910" stroke="black" cx="2098.44" cy="-1392.26" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="2098.44" y="-1395.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2098.44" y="-1382.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal</text>
</g>
<!-- plugginger_api_app -->
<g id="node18" class="node">
<title>plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1744.44,-621.78 1674.44,-621.78 1674.44,-575.53 1744.44,-575.53 1744.44,-621.78"/>
<text text-anchor="middle" x="1709.44" y="-608.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1709.44" y="-595.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1709.44" y="-582.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_app -->
<g id="edge8" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2093.72,-1368.22C2079.83,-1295.59 2043.63,-1065.76 2101.44,-889.78"/>
<path fill="none" stroke="black" d="M2101.44,-887.78C2111.76,-802.29 2176.1,-802.59 2196.44,-718.91"/>
<path fill="none" stroke="black" d="M2196.44,-716.91C2200.14,-691.34 2198.6,-684.53 2196.44,-658.78"/>
</g>
<!-- plugginger_api_builder -->
<g id="node21" class="node">
<title>plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1582.44,-1203.55 1512.44,-1203.55 1512.44,-1157.3 1582.44,-1157.3 1582.44,-1203.55"/>
<text text-anchor="middle" x="1547.44" y="-1190.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1547.44" y="-1177.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1547.44" y="-1164.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_builder -->
<g id="edge9" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2083.28,-1369.31C2061.55,-1337.92 2023.85,-1283.85 2021.44,-1282.83"/>
</g>
<!-- plugginger__internal_graph -->
<g id="node8" class="node">
<title>plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75bc2f" stroke="black" cx="832.44" cy="-1502.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="832.44" y="-1512.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="832.44" y="-1499.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="832.44" y="-1486.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">graph</text>
</g>
<!-- plugginger__internal_graph&#45;&gt;plugginger__internal_validation -->
<g id="edge10" class="edge">
<title>plugginger__internal_graph&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M782.47,-1502C701.22,-1500.72 537,-1490.59 412.44,-1433.98 405.96,-1431.03 399.65,-1427 393.82,-1422.61"/>
<polygon fill="#75bc2f" stroke="black" points="396.25,-1420.07 386.29,-1416.47 391.83,-1425.5 396.25,-1420.07"/>
</g>
<!-- plugginger__internal_graph&#45;&gt;plugginger_api_builder -->
<g id="edge11" class="edge">
<title>plugginger__internal_graph&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M870.89,-1481.83C878.95,-1477.81 887.44,-1473.68 895.44,-1469.98 932.67,-1452.75 946.24,-1456.65 980.44,-1433.98 1024.82,-1404.55 1024.43,-1383.27 1066.44,-1350.54 1134.03,-1297.86 1150.07,-1278.88 1230.44,-1249.13 1327.47,-1213.21 1360.25,-1238.96 1460.44,-1213.13 1473.94,-1209.65 1488.27,-1204.89 1501.33,-1200.11"/>
<polygon fill="#75bc2f" stroke="black" points="1502.4,-1203.45 1510.53,-1196.66 1499.94,-1196.9 1502.4,-1203.45"/>
</g>
<!-- plugginger__internal_proxy -->
<g id="node9" class="node">
<title>plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1567.44,-457.28 1497.44,-457.28 1497.44,-411.03 1567.44,-411.03 1567.44,-457.28"/>
<text text-anchor="middle" x="1532.44" y="-443.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1532.44" y="-431.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1532.44" y="-418.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">proxy</text>
</g>
<!-- plugginger__internal_proxy&#45;&gt;plugginger_api_builder -->
<g id="edge12" class="edge">
<title>plugginger__internal_proxy&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1537.18,-457.63C1552.27,-531.57 1597.5,-774.37 1587.07,-979.97"/>
<polygon fill="blue" stroke="black" points="1583.59,-979.59 1586.52,-989.77 1590.57,-979.98 1583.59,-979.59"/>
<path fill="none" stroke="black" d="M1586.44,-992.28C1586.44,-1052.85 1566.62,-1122.17 1555.24,-1156.98"/>
</g>
<!-- plugginger__internal_runtime -->
<g id="node10" class="node">
<title>plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1406.44,-823.28 1336.44,-823.28 1336.44,-777.03 1406.44,-777.03 1406.44,-823.28"/>
<text text-anchor="middle" x="1371.44" y="-809.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1371.44" y="-797.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1371.44" y="-784.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime</text>
</g>
<!-- plugginger__internal_runtime_facade -->
<g id="node15" class="node">
<title>plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1842.69,-741.03 1750.19,-741.03 1750.19,-694.78 1842.69,-694.78 1842.69,-741.03"/>
<text text-anchor="middle" x="1796.44" y="-727.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1796.44" y="-714.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1796.44" y="-702.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime_facade</text>
</g>
<!-- plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge13" class="edge">
<title>plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1406.84,-789.48C1422.58,-785.36 1441.36,-780.67 1458.44,-777.03 1556.16,-756.24 1670.98,-737.73 1738.51,-727.45"/>
<polygon fill="blue" stroke="black" points="1738.88,-730.93 1748.25,-725.97 1737.84,-724.01 1738.88,-730.93"/>
</g>
<!-- plugginger__internal_runtime_dispatcher -->
<g id="node11" class="node">
<title>plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1418.44,-1311.33 1348.44,-1311.33 1348.44,-1252.33 1418.44,-1252.33 1418.44,-1311.33"/>
<text text-anchor="middle" x="1383.44" y="-1297.83" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1383.44" y="-1285.08" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1383.44" y="-1272.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="1383.44" y="-1259.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dispatcher</text>
</g>
<!-- plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime -->
<g id="edge14" class="edge">
<title>plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1415.02,-1251.98C1431.56,-1233.54 1448.1,-1208.14 1446.44,-1181.43"/>
<path fill="none" stroke="black" d="M1446.44,-1179.43C1444.44,-1147.25 1429.42,-1142.68 1420.44,-1111.72 1405.34,-1059.72 1396.44,-1046.43 1396.44,-992.28 1396.44,-992.28 1396.44,-992.28 1396.44,-954.28 1396.44,-912.6 1387.3,-865.37 1380.08,-834.52"/>
<polygon fill="blue" stroke="black" points="1383.53,-833.91 1377.78,-825.02 1376.73,-835.56 1383.53,-833.91"/>
</g>
<!-- plugginger__internal_runtime_executors -->
<g id="node12" class="node">
<title>plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#669933" stroke="black" cx="1873.44" cy="-1070" rx="49.5" ry="41.72"/>
<text text-anchor="middle" x="1873.44" y="-1086" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1873.44" y="-1073.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">_internal.</text>
<text text-anchor="middle" x="1873.44" y="-1060.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">runtime.</text>
<text text-anchor="middle" x="1873.44" y="-1047.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">executors</text>
</g>
<!-- plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime -->
<g id="edge15" class="edge">
<title>plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1831.93,-1046.86C1738.78,-997.15 1513.68,-877.05 1416.91,-825.42"/>
<polygon fill="#669933" stroke="black" points="1418.66,-822.39 1408.19,-820.77 1415.36,-828.56 1418.66,-822.39"/>
</g>
<!-- plugginger__internal_runtime_fault_policy -->
<g id="node13" class="node">
<title>plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#6bac2b" stroke="black" cx="1713.44" cy="-1392.26" rx="51.62" ry="41.72"/>
<text text-anchor="middle" x="1713.44" y="-1408.26" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1713.44" y="-1395.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="1713.44" y="-1382.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">runtime.</text>
<text text-anchor="middle" x="1713.44" y="-1370.01" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">fault_policy</text>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime -->
<g id="edge16" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1674.84,-1364.33C1666.12,-1359.15 1656.7,-1354.2 1647.44,-1350.54 1572.79,-1321 1526.57,-1371.93 1470.44,-1314.54 1428.41,-1271.56 1447.1,-1241.54 1446.44,-1181.43"/>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge17" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1674.93,-1364.09C1666.21,-1358.92 1656.76,-1354.04 1647.44,-1350.54 1556.74,-1316.47 1522.76,-1349.58 1432.44,-1314.54 1431.3,-1314.1 1430.17,-1313.63 1429.03,-1313.14"/>
<polygon fill="#6bac2b" stroke="black" points="1430.8,-1310.11 1420.29,-1308.8 1427.69,-1316.38 1430.8,-1310.11"/>
</g>
<!-- plugginger__internal_runtime_lifecycle -->
<g id="node14" class="node">
<title>plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1139.44,-918.28 1069.44,-918.28 1069.44,-859.28 1139.44,-859.28 1139.44,-918.28"/>
<text text-anchor="middle" x="1104.44" y="-904.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1104.44" y="-892.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1104.44" y="-879.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="1104.44" y="-866.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">lifecycle</text>
</g>
<!-- plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime -->
<g id="edge18" class="edge">
<title>plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1139.89,-876.28C1187.7,-860.77 1272.9,-833.13 1325.54,-816.05"/>
<polygon fill="blue" stroke="black" points="1326.39,-819.45 1334.82,-813.04 1324.23,-812.8 1326.39,-819.45"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app -->
<g id="edge19" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1843.11,-714.94C1949.26,-709.83 2199.4,-694.05 2196.44,-658.78"/>
<path fill="none" stroke="black" d="M2196.44,-657.78C2192.75,-613.95 1877.77,-603.01 1756.39,-600.42"/>
<polygon fill="blue" stroke="black" points="1756.51,-596.92 1746.45,-600.22 1756.37,-603.92 1756.51,-596.92"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder -->
<g id="edge20" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1793.94,-741.31C1792.96,-754.51 1792.5,-771.77 1794.57,-788.08"/>
<polygon fill="blue" stroke="black" points="1791.07,-788.39 1796.19,-797.67 1797.98,-787.22 1791.07,-788.39"/>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger_api_builder -->
<g id="edge21" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M375.63,-1368.64C394.96,-1335.44 436.08,-1274.67 490.44,-1249.13 588.05,-1203.26 1354.59,-1233.85 1460.44,-1213.13 1474.12,-1210.45 1488.51,-1205.95 1501.57,-1201.17"/>
<polygon fill="blue" stroke="black" points="1502.67,-1204.49 1510.76,-1197.66 1500.17,-1197.96 1502.67,-1204.49"/>
</g>
<!-- plugginger_api -->
<g id="node17" class="node">
<title>plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="816.94,-1088 731.94,-1088 731.94,-1052 816.94,-1052 816.94,-1088"/>
<text text-anchor="middle" x="774.44" y="-1066.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.api</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_proxy -->
<g id="edge22" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M774.68,-1051.66C774.98,-1029.37 775.44,-989.44 775.44,-955.28 775.44,-955.28 775.44,-955.28 775.44,-597.66 775.44,-549.14 769.61,-523.67 807.44,-493.28 860.27,-450.85 1334.24,-438.71 1485.95,-435.9"/>
<polygon fill="blue" stroke="black" points="1485.69,-439.4 1495.63,-435.72 1485.57,-432.4 1485.69,-439.4"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge23" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M817.32,-1062.02C899.37,-1048.16 1071.58,-1016.41 1083.44,-992.28"/>
<path fill="none" stroke="black" d="M1083.44,-991.28C1092.81,-972.2 1098.01,-949.11 1100.88,-929.78"/>
<polygon fill="blue" stroke="black" points="1104.32,-930.47 1102.16,-920.1 1097.38,-929.55 1104.32,-930.47"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge24" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M793.87,-1051.53C838.22,-1012.42 951.52,-916.73 1060.44,-859.28 1157.94,-807.85 1187.02,-801.8 1294.44,-777.03 1377.81,-757.81 1625.56,-734.14 1738.8,-723.95"/>
<polygon fill="blue" stroke="black" points="1738.82,-727.47 1748.47,-723.09 1738.2,-720.49 1738.82,-727.47"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_validation -->
<g id="edge25" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M731.68,-1079.18C654.75,-1096.25 493.39,-1143.23 411.44,-1249.13 386.63,-1281.18 374.25,-1326.79 368.35,-1357.62"/>
<polygon fill="blue" stroke="black" points="364.96,-1356.71 366.66,-1367.17 371.85,-1357.93 364.96,-1356.71"/>
</g>
<!-- plugginger_cli_cmd_core_freeze -->
<g id="node27" class="node">
<title>plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="789.06,-273.06 689.81,-273.06 689.81,-226.81 789.06,-226.81 789.06,-273.06"/>
<text text-anchor="middle" x="739.44" y="-259.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="739.44" y="-246.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="739.44" y="-234.06" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_core_freeze</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge26" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M542.44,-991.28C507.77,-977.07 553.35,-860.62 556.44,-823.28 571.57,-639.99 512.12,-576.49 592.44,-411.03 611.2,-372.38 634.96,-377.17 661.44,-343.33"/>
<path fill="none" stroke="black" d="M661.44,-341.33C670.06,-327.35 670.31,-322.56 680.44,-309.63 688.06,-299.88 697.19,-290.09 705.96,-281.37"/>
<polygon fill="blue" stroke="black" points="708.37,-283.91 713.11,-274.44 703.49,-278.89 708.37,-283.91"/>
</g>
<!-- plugginger_cli_cmd_stubs_generate -->
<g id="node29" class="node">
<title>plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="774.06,-118.25 656.81,-118.25 656.81,-72 774.06,-72 774.06,-118.25"/>
<text text-anchor="middle" x="715.44" y="-104.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="715.44" y="-92" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="715.44" y="-79.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_stubs_generate</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge27" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M731.65,-1064.5C632.95,-1053.1 394.56,-1019.31 352.44,-955.28"/>
<path fill="none" stroke="black" d="M352.44,-954.28C234.49,-885.75 86.44,-937.57 86.44,-801.16 86.44,-801.16 86.44,-801.16 86.44,-248.94 86.44,-135.01 481.01,-105.69 645.13,-98.42"/>
<polygon fill="blue" stroke="black" points="645.19,-101.92 655.03,-98 644.89,-94.93 645.19,-101.92"/>
</g>
<!-- plugginger_cli_utils -->
<g id="node30" class="node">
<title>plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75c823" stroke="black" cx="738.44" cy="-342.33" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="738.44" y="-351.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="738.44" y="-339.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">cli.</text>
<text text-anchor="middle" x="738.44" y="-326.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">utils</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_utils -->
<g id="edge28" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M731.81,-1057.89C685.62,-1042.97 618.44,-1011.59 618.44,-955.28 618.44,-955.28 618.44,-955.28 618.44,-716.91 618.44,-580.96 548.67,-528.31 617.44,-411.03 621.71,-403.75 657.65,-384.11 689.26,-367.85"/>
<polygon fill="blue" stroke="black" points="690.66,-371.06 697.97,-363.4 687.48,-364.83 690.66,-371.06"/>
</g>
<!-- plugginger_stubgen -->
<g id="node45" class="node">
<title>plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#6ba135" stroke="black" cx="508.44" cy="-249.94" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="508.44" y="-253.19" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="508.44" y="-240.44" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">stubgen</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_stubgen -->
<g id="edge29" class="edge">
<title>plugginger_api&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M731.61,-1056.37C684.68,-1042.17 607.41,-1017.75 542.44,-992.28"/>
<path fill="none" stroke="black" d="M542.44,-991.28C431.22,-947.68 352.44,-920.62 352.44,-801.16 352.44,-801.16 352.44,-801.16 352.44,-515.41 352.44,-438.93 299.93,-398.94 352.44,-343.33"/>
</g>
<!-- plugginger_testing_helpers -->
<g id="node48" class="node">
<title>plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="342.44,-273.06 272.44,-273.06 272.44,-226.81 342.44,-226.81 342.44,-273.06"/>
<text text-anchor="middle" x="307.44" y="-259.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="307.44" y="-246.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">testing.</text>
<text text-anchor="middle" x="307.44" y="-234.06" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">helpers</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_testing_helpers -->
<g id="edge30" class="edge">
<title>plugginger_api&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M352.44,-954.28C243.32,-795.2 253.2,-730.29 224.44,-539.53 212.56,-460.79 254.47,-443.32 295.44,-375.03 303.89,-360.95 322.29,-357.76 314.44,-343.33"/>
<path fill="none" stroke="black" d="M314.44,-341.33C304.42,-324.56 302.47,-302.81 303.12,-284.85"/>
<polygon fill="blue" stroke="black" points="306.61,-285.13 303.79,-274.91 299.62,-284.65 306.61,-285.13"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger__internal_proxy -->
<g id="edge31" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1684.83,-575.06C1653.65,-546.44 1599.83,-497.03 1565.2,-465.24"/>
<polygon fill="blue" stroke="black" points="1567.69,-462.77 1557.95,-458.59 1562.95,-467.93 1567.69,-462.77"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api -->
<g id="edge32" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1674.26,-608.41C1583.07,-631.54 1332.12,-697.96 1131.44,-777.03 1053.72,-807.66 1030.46,-810.8 962.44,-859.28 932.24,-880.81 934.4,-897.87 903.44,-918.28 871.12,-939.59 827.52,-917.05 816.04,-943.36"/>
<polygon fill="blue" stroke="black" points="812.7,-942.27 813.79,-952.81 819.51,-943.89 812.7,-942.27"/>
</g>
<!-- plugginger_api_app_plugin -->
<g id="node19" class="node">
<title>plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="875.44,-365.45 805.44,-365.45 805.44,-319.2 875.44,-319.2 875.44,-365.45"/>
<text text-anchor="middle" x="840.44" y="-351.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="840.44" y="-339.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="840.44" y="-326.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app_plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_app_plugin -->
<g id="edge33" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1704.41,-563.88C1695.94,-520.29 1674.35,-445.92 1622.44,-411.03 1554.29,-365.24 961.93,-402.15 884.44,-375.03 881.72,-374.08 879.01,-372.9 876.37,-371.57"/>
<polygon fill="blue" stroke="black" points="1700.93,-564.33 1706.15,-573.55 1707.82,-563.09 1700.93,-564.33"/>
<polygon fill="blue" stroke="black" points="878.21,-368.59 867.82,-366.54 874.67,-374.62 878.21,-368.59"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_builder -->
<g id="edge34" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1710.86,-622.24C1713.49,-651.27 1720.88,-702.51 1741.44,-741.03 1756.22,-768.73 1781.35,-765.86 1792.7,-788.61"/>
<polygon fill="blue" stroke="black" points="1789.29,-789.47 1795.93,-797.73 1795.89,-787.14 1789.29,-789.47"/>
<path fill="none" stroke="black" d="M1796.44,-801.16C1810.71,-867.7 1796.44,-886.23 1796.44,-954.28 1796.44,-992.28 1796.44,-992.28 1796.44,-992.28 1796.44,-1104.86 1695.31,-1093.89 1596.44,-1147.72 1591.28,-1150.53 1585.94,-1153.7 1580.77,-1156.91"/>
</g>
<!-- plugginger_api_depends -->
<g id="node22" class="node">
<title>plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="886.44,-539.53 816.44,-539.53 816.44,-493.28 886.44,-493.28 886.44,-539.53"/>
<text text-anchor="middle" x="851.44" y="-526.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="851.44" y="-513.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="851.44" y="-500.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">depends</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_depends -->
<g id="edge35" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1674.2,-594.36C1538.67,-581.69 1050.89,-536.06 897.77,-521.74"/>
<polygon fill="blue" stroke="black" points="898.28,-518.27 888,-520.83 897.63,-525.24 898.28,-518.27"/>
</g>
<!-- plugginger_api_plugin -->
<g id="node24" class="node">
<title>plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="696.44,-457.28 626.44,-457.28 626.44,-411.03 696.44,-411.03 696.44,-457.28"/>
<text text-anchor="middle" x="661.44" y="-443.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="661.44" y="-431.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="661.44" y="-418.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_plugin -->
<g id="edge36" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1663.24,-582.36C1591.27,-559.17 1447.64,-515.49 1322.44,-493.28 1094.48,-452.84 817.08,-440.08 708.34,-436.46"/>
<polygon fill="blue" stroke="black" points="1662.13,-585.68 1672.72,-585.43 1664.29,-579.02 1662.13,-585.68"/>
<polygon fill="blue" stroke="black" points="708.46,-432.96 698.35,-436.13 708.23,-439.95 708.46,-432.96"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api -->
<g id="edge37" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M824.34,-365.92C795.37,-408.87 737.44,-506.19 737.44,-597.66 737.44,-801.16 737.44,-801.16 737.44,-801.16 737.44,-873.13 821.17,-879.77 815.28,-943.15"/>
<polygon fill="blue" stroke="black" points="811.87,-942.35 813.68,-952.79 818.77,-943.5 811.87,-942.35"/>
<path fill="none" stroke="black" d="M813.44,-955.28C808.99,-990.91 792.91,-1030 782.84,-1051.81"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge39" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M866.52,-365.76C872.11,-369.49 878.22,-372.85 884.44,-375.03 957.07,-400.58 1515.74,-363.67 1576.44,-411.03 1614.69,-440.88 1608.44,-466.89 1608.44,-515.41 1608.44,-599.66 1608.44,-599.66 1608.44,-599.66 1608.44,-688.61 1624.44,-710.21 1624.44,-799.16 1624.44,-889.78 1624.44,-889.78 1624.44,-889.78 1624.44,-934 1604.68,-943.88 1590.18,-980.72"/>
<polygon fill="blue" stroke="black" points="1586.98,-979.26 1586.94,-989.86 1593.58,-981.6 1586.98,-979.26"/>
</g>
<!-- plugginger_api_background -->
<g id="node20" class="node">
<title>plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75b03a" stroke="black" cx="774.44" cy="-1281.83" rx="51.62" ry="32.7"/>
<text text-anchor="middle" x="774.44" y="-1291.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="774.44" y="-1278.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">api.</text>
<text text-anchor="middle" x="774.44" y="-1265.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">background</text>
</g>
<!-- plugginger_api_background&#45;&gt;plugginger_api -->
<g id="edge40" class="edge">
<title>plugginger_api_background&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M804.37,-1254.83C815.41,-1243.31 826.64,-1228.81 832.44,-1213.13 842.52,-1185.86 841.68,-1175.28 832.44,-1147.72 826.03,-1128.63 812.79,-1110.51 800.58,-1096.67"/>
<polygon fill="#75b03a" stroke="black" points="803.17,-1094.31 793.81,-1089.34 798.02,-1099.06 803.17,-1094.31"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_api -->
<g id="edge41" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1512.08,-1173.68C1469.87,-1166.93 1397.12,-1155.65 1334.44,-1147.72 1181.49,-1128.37 1141.97,-1134.09 989.44,-1111.72 934.23,-1103.63 871.6,-1091.42 828.24,-1082.48"/>
<polygon fill="blue" stroke="black" points="829.08,-1079.08 818.57,-1080.47 827.65,-1085.93 829.08,-1079.08"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge42" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1572.67,-1157.07C1606.53,-1124.48 1662.44,-1060.35 1662.44,-992.28 1662.44,-992.28 1662.44,-992.28 1662.44,-799.16 1662.44,-699.76 1600.32,-650.63 1665.44,-575.53 1906.51,-297.5 2371.63,-786.39 2481.44,-435.16"/>
<path fill="none" stroke="black" d="M2481.44,-433.16C2503.69,-400.01 2514.03,-366.39 2481.44,-343.33"/>
<path fill="none" stroke="black" d="M2481.44,-341.33C2331.34,-235.14 1028.7,-308.79 800.52,-273.4"/>
<polygon fill="blue" stroke="black" points="801.26,-269.98 790.8,-271.66 800.03,-276.87 801.26,-269.98"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_utils -->
<g id="edge43" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1540.04,-1156.95C1529.24,-1122.1 1510.44,-1052.73 1510.44,-992.28 1510.44,-992.28 1510.44,-992.28 1510.44,-954.28 1510.44,-738.82 1530.07,-626.14 1360.44,-493.28 1158.8,-335.36 1036.72,-463.69 796.44,-375.03 792.55,-373.6 788.6,-371.94 784.69,-370.14"/>
<polygon fill="blue" stroke="black" points="786.37,-367.07 775.85,-365.81 783.29,-373.35 786.37,-367.07"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger__internal_validation -->
<g id="edge44" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M824.1,-539.9C790.93,-567.32 733.5,-615.28 685.44,-657.78 626.95,-709.5 605.96,-716.67 556.44,-777.03 433.9,-926.39 415.93,-974.98 329.44,-1147.72 302.77,-1200.99 245.65,-1226.9 270.17,-1271.36"/>
<polygon fill="blue" stroke="black" points="267.16,-1273.16 275.6,-1279.57 273,-1269.3 267.16,-1273.16"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api -->
<g id="edge45" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M849.83,-539.72C845.25,-602.59 831.32,-786.8 814.64,-943.08"/>
<polygon fill="blue" stroke="black" points="811.19,-942.46 813.6,-952.78 818.15,-943.21 811.19,-942.46"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_builder -->
<g id="edge46" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M886.74,-519.03C1000.31,-526.44 1355.36,-567.18 1467.44,-788.81"/>
<polygon fill="blue" stroke="black" points="1464.28,-790.31 1471.78,-797.8 1470.58,-787.27 1464.28,-790.31"/>
<path fill="none" stroke="black" d="M1472.44,-801.16C1498.43,-864.05 1472.44,-886.23 1472.44,-954.28 1472.44,-992.28 1472.44,-992.28 1472.44,-992.28 1472.44,-1046.43 1474.26,-1062.33 1496.44,-1111.72 1503.85,-1128.24 1515.91,-1144.51 1526.45,-1156.96"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_plugin -->
<g id="edge47" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M815.95,-501.16C787.36,-489.59 746.17,-472.71 710.44,-457.28 709.32,-456.8 708.2,-456.31 707.06,-455.82"/>
<polygon fill="blue" stroke="black" points="708.65,-452.69 698.09,-451.86 705.83,-459.1 708.65,-452.69"/>
</g>
<!-- plugginger_api_events -->
<g id="node23" class="node">
<title>plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#6ba135" stroke="black" cx="657.44" cy="-1180.43" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="657.44" y="-1190.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="657.44" y="-1177.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">api.</text>
<text text-anchor="middle" x="657.44" y="-1164.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">events</text>
</g>
<!-- plugginger_api_events&#45;&gt;plugginger_api -->
<g id="edge48" class="edge">
<title>plugginger_api_events&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M685.76,-1153.18C704.56,-1135.76 729.08,-1113.03 747.58,-1095.89"/>
<polygon fill="#6ba135" stroke="black" points="749.8,-1098.61 754.75,-1089.25 745.04,-1093.48 749.8,-1098.61"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge49" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M696.92,-452.33C701.41,-454.16 705.99,-455.87 710.44,-457.28 880.6,-511.45 965.61,-451.87 1094.44,-575.53 1167.86,-646.01 1139.02,-780.93 1118.38,-847.96"/>
<polygon fill="blue" stroke="black" points="1115.06,-846.85 1115.36,-857.44 1121.73,-848.98 1115.06,-846.85"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge50" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1308.44,-517.41C1317.82,-524.29 1313.41,-532.2 1322.44,-539.53 1454.73,-647.03 1658.72,-693.46 1749.77,-709.65"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_validation -->
<g id="edge51" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M626.01,-438.57C539.69,-447.21 323.82,-470.83 300.44,-493.28 275.43,-517.3 238.44,-764.48 238.44,-799.16 238.44,-889.78 238.44,-889.78 238.44,-889.78 238.44,-1060.65 179.47,-1134.21 270.24,-1271.68"/>
<polygon fill="blue" stroke="black" points="267.08,-1273.26 275.59,-1279.58 272.88,-1269.34 267.08,-1273.26"/>
<path fill="none" stroke="black" d="M276.44,-1282.83C297.91,-1313.76 325.52,-1347.26 343.81,-1368.71"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api -->
<g id="edge52" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M661.44,-457.77C661.44,-489.16 661.44,-547.72 661.44,-597.66 661.44,-718.91 661.44,-718.91 661.44,-718.91 661.44,-844.61 728.23,-983.96 759.11,-1041.67"/>
<polygon fill="blue" stroke="black" points="756.03,-1043.34 763.88,-1050.46 762.19,-1040 756.03,-1043.34"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin -->
<g id="edge54" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M696.84,-420.36C724.67,-409.72 764.02,-393.4 796.44,-375.03 798.28,-373.99 800.14,-372.88 802,-371.74"/>
<polygon fill="blue" stroke="black" points="803.47,-374.95 809.93,-366.56 799.64,-369.09 803.47,-374.95"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge55" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M696.77,-452.88C701.28,-454.61 705.91,-456.15 710.44,-457.28 873.2,-498.08 1104.29,-419.63 1298.35,-510.51"/>
<polygon fill="blue" stroke="black" points="1296.55,-513.53 1307.08,-514.75 1299.61,-507.23 1296.55,-513.53"/>
<path fill="none" stroke="black" d="M1308.44,-517.41C1354.14,-546.09 1339.44,-577.62 1370.44,-621.78 1394.56,-656.16 1409.02,-658.66 1430.44,-694.78 1455.94,-737.79 1462.41,-750.17 1472.44,-799.16"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge56" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M654.39,-410.75C649.64,-390.91 646.64,-362.24 661.44,-343.33"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge57" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M585.44,-341.33C543.07,-271.14 609.13,-236.38 661.44,-173.25"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_stubgen -->
<g id="edge58" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M638.91,-410.72C622.48,-393.44 600.53,-368.34 585.44,-343.33"/>
<path fill="none" stroke="black" d="M585.44,-341.33C572.01,-319.09 553.14,-296.84 537.44,-279.98"/>
<polygon fill="blue" stroke="black" points="540.03,-277.62 530.61,-272.78 534.96,-282.44 540.03,-277.62"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_testing_helpers -->
<g id="edge59" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M626.06,-430.05C552.6,-423.08 386.39,-404.46 338.44,-375.03 323.37,-365.79 322.89,-358.85 314.44,-343.33"/>
</g>
<!-- plugginger_api_service -->
<g id="node25" class="node">
<title>plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#6ba135" stroke="black" cx="774.44" cy="-1180.43" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="774.44" y="-1190.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="774.44" y="-1177.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">api.</text>
<text text-anchor="middle" x="774.44" y="-1164.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">service</text>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_api -->
<g id="edge60" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M774.44,-1147.48C774.44,-1132.35 774.44,-1114.43 774.44,-1099.73"/>
<polygon fill="#6ba135" stroke="black" points="777.94,-1099.79 774.44,-1089.79 770.94,-1099.79 777.94,-1099.79"/>
</g>
<!-- plugginger_cli -->
<g id="node26" class="node">
<title>plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="799.06,-36 717.81,-36 717.81,0 799.06,0 799.06,-36"/>
<text text-anchor="middle" x="758.44" y="-14.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.cli</text>
</g>
<!-- plugginger_cli_cmd_core_freeze&#45;&gt;plugginger_cli -->
<g id="edge61" class="edge">
<title>plugginger_cli_cmd_core_freeze&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M785.72,-226.41C815.69,-210.51 850.11,-189.28 855.44,-173.25"/>
<path fill="none" stroke="black" d="M855.44,-171.25C871.9,-121.69 825.84,-72.15 791.68,-43.6"/>
<polygon fill="blue" stroke="black" points="794.01,-40.98 784.03,-37.41 789.6,-46.42 794.01,-40.98"/>
</g>
<!-- plugginger_cli_cmd_project_run -->
<g id="node28" class="node">
<title>plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="903.94,-273.06 806.94,-273.06 806.94,-226.81 903.94,-226.81 903.94,-273.06"/>
<text text-anchor="middle" x="855.44" y="-259.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="855.44" y="-246.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="855.44" y="-234.06" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_project_run</text>
</g>
<!-- plugginger_cli_cmd_project_run&#45;&gt;plugginger_cli -->
<g id="edge62" class="edge">
<title>plugginger_cli_cmd_project_run&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M853.05,-226.5C852.02,-211.31 851.76,-190.87 855.44,-173.25"/>
</g>
<!-- plugginger_cli_cmd_stubs_generate&#45;&gt;plugginger_cli -->
<g id="edge63" class="edge">
<title>plugginger_cli_cmd_stubs_generate&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M728.34,-71.59C732.98,-63.47 738.26,-54.24 743.1,-45.8"/>
<polygon fill="blue" stroke="black" points="746.01,-47.76 747.94,-37.34 739.93,-44.28 746.01,-47.76"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge64" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M738.79,-309.21C738.88,-301.23 738.98,-292.66 739.06,-284.61"/>
<polygon fill="#75c823" stroke="black" points="742.56,-284.87 739.17,-274.83 735.56,-284.79 742.56,-284.87"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge65" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M770.1,-316.87C784.69,-305.59 802.14,-292.12 817.44,-280.3"/>
<polygon fill="#75c823" stroke="black" points="819.19,-283.37 824.96,-274.48 814.91,-277.83 819.19,-283.37"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge66" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M710,-315.07C675.26,-279.84 625.32,-216.68 661.44,-173.25"/>
</g>
<!-- plugginger_config -->
<g id="node31" class="node">
<title>plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75e505" stroke="black" cx="2442.44" cy="-1392.26" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="2442.44" y="-1395.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2442.44" y="-1382.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge67" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2414.24,-1372.72C2349.4,-1330.44 2184.19,-1224.61 2039.44,-1147.72 2002.88,-1128.3 1960.5,-1108.76 1927.71,-1094.26"/>
<polygon fill="#75e505" stroke="black" points="1929.37,-1091.17 1918.8,-1090.35 1926.55,-1097.58 1929.37,-1091.17"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge68" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2434.8,-1368.36C2427.43,-1346.37 2415.88,-1312.26 2405.44,-1282.83"/>
<path fill="none" stroke="black" d="M2405.44,-1280.83C2392.75,-1245.08 2368.6,-1247.91 2353.44,-1213.13 2327.84,-1154.41 2343.8,-1133.43 2329.44,-1071"/>
<path fill="none" stroke="black" d="M2329.44,-1069C2323.27,-1048.92 2319.74,-1043.67 2305.44,-1028.28 2286.3,-1007.7 2273.3,-1012.17 2253.44,-992.28"/>
<path fill="none" stroke="black" d="M2253.44,-991.28C2221.45,-959.26 2211.78,-952.74 2182.44,-918.28 2131.72,-858.73 2143.39,-819.1 2077.44,-777.03 2009.17,-733.49 1914.33,-721.81 1854.17,-719.08"/>
<polygon fill="#75e505" stroke="black" points="1854.62,-715.6 1844.5,-718.72 1854.36,-722.59 1854.62,-715.6"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_app -->
<g id="edge69" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2253.44,-991.28C2202.18,-919.49 2214.18,-887.57 2196.44,-801.16"/>
<path fill="none" stroke="black" d="M2196.44,-799.16C2188.07,-764.49 2191.33,-754.21 2196.44,-718.91"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_builder -->
<g id="edge70" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2395.83,-1383.87C2316.63,-1370.32 2151.67,-1337.72 2021.44,-1282.83"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge71" class="edge">
<title>plugginger_config&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2405.44,-1280.83C2361.64,-1141.9 2443.44,-1100.95 2443.44,-955.28 2443.44,-955.28 2443.44,-955.28 2443.44,-887.78 2443.44,-685.91 2368.92,-602.77 2481.44,-435.16"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge72" class="edge">
<title>plugginger_config&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2445.48,-1368.54C2465.2,-1219.79 2571.48,-388.44 2495.44,-309.63 2439.58,-251.74 1205.62,-250.31 915.92,-250.78"/>
<polygon fill="#75e505" stroke="black" points="915.93,-247.28 905.94,-250.8 915.95,-254.28 915.93,-247.28"/>
</g>
<!-- plugginger_testing_mock_app -->
<g id="node49" class="node">
<title>plugginger_testing_mock_app</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75bc2f" stroke="black" cx="942.44" cy="-342.33" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="942.44" y="-351.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="942.44" y="-339.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">testing.</text>
<text text-anchor="middle" x="942.44" y="-326.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">mock_app</text>
</g>
<!-- plugginger_config&#45;&gt;plugginger_testing_mock_app -->
<g id="edge73" class="edge">
<title>plugginger_config&#45;&gt;plugginger_testing_mock_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2329.44,-1069C2314.48,-1020.33 2339.47,-1006.05 2343.44,-955.28 2353.75,-823.39 2336.09,-788.94 2353.44,-657.78 2366.76,-557.05 2457.47,-522.43 2405.44,-435.16"/>
<path fill="none" stroke="black" d="M2405.44,-433.16C2388.75,-419.62 2383.2,-416.59 2362.44,-411.03 2100.42,-340.93 1241.61,-341.3 1003.9,-342.8"/>
<polygon fill="#75e505" stroke="black" points="1003.89,-339.3 993.92,-342.87 1003.94,-346.3 1003.89,-339.3"/>
</g>
<!-- plugginger_config_models -->
<g id="node32" class="node">
<title>plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#70db05" stroke="black" cx="2404.44" cy="-1502.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2404.44" y="-1512.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2404.44" y="-1499.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config.</text>
<text text-anchor="middle" x="2404.44" y="-1486.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">models</text>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge74" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2372.86,-1477.38C2309.18,-1428.25 2161.07,-1313.21 2039.44,-1213.13 1996.95,-1178.17 1949.32,-1137.2 1916.01,-1108.25"/>
<polygon fill="#70db05" stroke="black" points="1918.42,-1105.71 1908.58,-1101.79 1913.83,-1110.99 1918.42,-1105.71"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge75" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2394.56,-1470.22C2371.82,-1398.56 2313.1,-1218.02 2253.44,-1071"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_app -->
<g id="edge76" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2449.99,-1489.31C2478.49,-1479.16 2513.45,-1461.82 2533.44,-1433.98 2573.09,-1378.72 2559.56,-1350.82 2557.44,-1282.83"/>
<path fill="none" stroke="black" d="M2557.44,-1280.83C2558.69,-1192.52 2360.23,-1038.06 2329.44,-955.28"/>
<path fill="none" stroke="black" d="M2329.44,-954.28C2300.74,-919.53 2207.01,-844.97 2196.44,-801.16"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_builder -->
<g id="edge77" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2354.8,-1499.48C2265.4,-1494.43 2082.66,-1478.76 2040.44,-1433.98 1993.99,-1384.71 2083.83,-1309.13 2021.44,-1282.83"/>
<path fill="none" stroke="black" d="M2021.44,-1280.83C1978.37,-1262.96 1967.05,-1258.79 1921.44,-1249.13 1779.26,-1219.02 1733.33,-1261.91 1596.44,-1213.13 1593.54,-1212.1 1590.64,-1210.86 1587.78,-1209.49"/>
<polygon fill="#70db05" stroke="black" points="1589.46,-1206.42 1579,-1204.75 1586.13,-1212.58 1589.46,-1206.42"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge78" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2557.44,-716.91C2551.11,-665.18 2557.44,-651.77 2557.44,-599.66 2557.44,-599.66 2557.44,-599.66 2557.44,-515.41 2557.44,-431.8 2549.69,-391.62 2481.44,-343.33"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge79" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2557.44,-1280.83C2566.5,-1182.06 2633.44,-1170.19 2633.44,-1071 2633.44,-1071 2633.44,-1071 2633.44,-657.78 2633.44,-593.15 2664.12,-581.63 2671.44,-517.41"/>
<path fill="none" stroke="black" d="M2671.44,-515.41C2678.46,-423.6 2715.2,-371.97 2647.44,-309.63 2582.34,-249.74 1219.8,-249.93 915.39,-250.73"/>
<polygon fill="#70db05" stroke="black" points="915.68,-247.23 905.69,-250.75 915.7,-254.23 915.68,-247.23"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_config -->
<g id="edge80" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2415.43,-1470.32C2420.24,-1456.6 2425.88,-1440.49 2430.77,-1426.56"/>
<polygon fill="#70db05" stroke="black" points="2433.98,-1427.96 2433.99,-1417.37 2427.38,-1425.65 2433.98,-1427.96"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_testing_mock_app -->
<g id="edge81" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_testing_mock_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2557.44,-1280.83C2565.53,-1187.93 2557.44,-1164.26 2557.44,-1071 2557.44,-1071 2557.44,-1071 2557.44,-887.78 2557.44,-812.73 2578.45,-790.96 2557.44,-718.91"/>
<path fill="none" stroke="black" d="M2557.44,-716.91C2511.62,-582.2 2515.93,-524.8 2405.44,-435.16"/>
</g>
<!-- plugginger_core -->
<g id="node33" class="node">
<title>plugginger_core</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85f910" stroke="black" cx="1869.44" cy="-1687.46" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="1869.44" y="-1690.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1869.44" y="-1677.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger -->
<g id="edge82" class="edge">
<title>plugginger_core&#45;&gt;plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1557.44,-1391.26C1539.47,-1382.55 1554.81,-1361.97 1538.44,-1350.54 1461.27,-1296.65 1208.3,-1335 1116.44,-1314.54 1102.72,-1311.48 1088.24,-1306.77 1075.15,-1301.89"/>
<polygon fill="#85f910" stroke="black" points="1076.53,-1298.68 1065.94,-1298.34 1074.02,-1305.21 1076.53,-1298.68"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_graph -->
<g id="edge83" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1819.66,-1685.99C1674.45,-1683.97 1253.82,-1673.43 1129.44,-1618.76 1115.7,-1612.72 1118.54,-1601.2 1104.44,-1596.07"/>
<path fill="none" stroke="black" d="M1104.44,-1594.07C1013.77,-1561.07 985.39,-1570.27 895.44,-1535.38 890.49,-1533.46 885.41,-1531.29 880.39,-1528.99"/>
<polygon fill="#85f910" stroke="black" points="882.12,-1525.94 871.58,-1524.81 879.12,-1532.26 882.12,-1525.94"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_proxy -->
<g id="edge84" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1917.45,-1681.22C1981.07,-1672.39 2094.65,-1649.91 2175.44,-1596.07"/>
<path fill="none" stroke="black" d="M2175.44,-1594.07C2198.14,-1578.94 2264.87,-1138.88 2267.44,-1111.72 2270.93,-1074.8 2279.47,-1063.36 2267.44,-1028.28 2260.86,-1009.11 2246.27,-1011.37 2239.44,-992.28 2210.81,-912.28 2234.44,-886.13 2234.44,-801.16 2234.44,-801.16 2234.44,-801.16 2234.44,-597.66 2234.44,-462.65 1735.37,-439.78 1578.83,-435.93"/>
<polygon fill="#85f910" stroke="black" points="1579.32,-432.44 1569.24,-435.71 1579.16,-439.44 1579.32,-432.44"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge85" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1869.44,-1594.07C1869.44,-1553.9 1876.06,-1543.3 1869.44,-1503.68"/>
<path fill="none" stroke="black" d="M1869.44,-1501.68C1866.9,-1486.49 1864.31,-1482.56 1855.44,-1469.98 1842.03,-1450.96 1831.93,-1452.17 1817.44,-1433.98 1804.35,-1417.54 1810.37,-1405.68 1793.44,-1393.26"/>
<path fill="none" stroke="black" d="M1793.44,-1391.26C1777.34,-1379.44 1790.62,-1362.24 1774.44,-1350.54 1719.31,-1310.68 1536.62,-1330.29 1470.44,-1314.54 1456.87,-1311.31 1442.52,-1306.63 1429.46,-1301.85"/>
<polygon fill="#85f910" stroke="black" points="1430.85,-1298.64 1420.26,-1298.38 1428.38,-1305.19 1430.85,-1298.64"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge86" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1869.44,-1594.07C1869.44,-1567.26 1880.3,-1562.01 1883.44,-1535.38 1893.03,-1453.79 1923.41,-1422.31 1883.44,-1350.54 1848.72,-1288.19 1543.36,-1205.8 1610.44,-1181.43"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge87" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1793.44,-1501.68C1790.62,-1498.7 1766.86,-1466.41 1745.95,-1437.84"/>
<polygon fill="#85f910" stroke="black" points="1748.92,-1435.97 1740.19,-1429.96 1743.27,-1440.1 1748.92,-1435.97"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge88" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1484.44,-1280.83C1436.51,-1266.41 1429.64,-1248.71 1394.44,-1213.13 1322.7,-1140.63 1326.93,-1103.85 1258.44,-1028.28 1224.11,-990.41 1180.43,-951.9 1148.36,-925.13"/>
<polygon fill="#85f910" stroke="black" points="1150.74,-922.56 1140.81,-918.87 1146.27,-927.95 1150.74,-922.56"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge89" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1900.25,-1668.44C1925.56,-1652.27 1960.81,-1626.46 1983.44,-1596.07"/>
<path fill="none" stroke="black" d="M1983.44,-1594.07C2030.47,-1530.9 2063.44,-968.54 2063.44,-889.78"/>
<path fill="none" stroke="black" d="M2063.44,-887.78C2046.35,-793.06 1927.22,-748.85 1853.98,-730.4"/>
<polygon fill="#85f910" stroke="black" points="1855.1,-727.07 1844.55,-728.12 1853.45,-733.88 1855.1,-727.07"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_validation -->
<g id="edge90" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1104.44,-1594.07C1047.58,-1573.38 1098.32,-1504.23 1048.44,-1469.98 931.74,-1389.84 544.16,-1485.82 412.44,-1433.98 405.68,-1431.32 399.17,-1427.35 393.22,-1422.91"/>
<polygon fill="#85f910" stroke="black" points="395.51,-1420.26 385.55,-1416.64 391.08,-1425.68 395.51,-1420.26"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app -->
<g id="edge91" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1983.44,-1594.07C2050.7,-1513.76 2114.48,-1529.97 2156.44,-1433.98 2171.29,-1400 2160.03,-1387.45 2156.44,-1350.54 2144.16,-1224.51 2101.44,-1197.62 2101.44,-1071 2101.44,-1071 2101.44,-1071 2101.44,-991.28 2101.44,-946.17 2087.89,-932.81 2101.44,-889.78"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app_plugin -->
<g id="edge92" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2175.44,-1594.07C2185.55,-1587.89 2181.15,-1579.85 2189.44,-1571.38 2247.57,-1512.04 2269.98,-1502.44 2346.44,-1469.98 2411.13,-1442.5 2456.76,-1489.04 2500.44,-1433.98 2523.48,-1404.92 2504.8,-1387.36 2500.44,-1350.54 2479.18,-1171.12 2405.44,-1135.95 2405.44,-955.28 2405.44,-955.28 2405.44,-955.28 2405.44,-887.78 2405.44,-837.09 2387.13,-467.9 2348.44,-435.16"/>
<path fill="none" stroke="black" d="M2348.44,-433.16C2212.55,-343.52 1038.34,-428.1 884.44,-375.03 881.71,-374.09 879.01,-372.92 876.36,-371.6"/>
<polygon fill="#85f910" stroke="black" points="878.2,-368.62 867.8,-366.58 874.66,-374.66 878.2,-368.62"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_background -->
<g id="edge93" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1679.44,-1501.68C1671.56,-1487.26 1672.69,-1480.92 1660.44,-1469.98 1631.66,-1444.27 1610.11,-1459.79 1581.44,-1433.98 1565.82,-1419.92 1576.34,-1402.42 1557.44,-1393.26"/>
<path fill="none" stroke="black" d="M1557.44,-1391.26C1539.47,-1382.55 1554.92,-1361.81 1538.44,-1350.54 1487.79,-1315.91 1048.44,-1321.06 987.44,-1314.54 936.02,-1309.04 877.98,-1300.23 835.49,-1293.29"/>
<polygon fill="#85f910" stroke="black" points="836.14,-1289.85 825.7,-1291.68 835,-1296.75 836.14,-1289.85"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_builder -->
<g id="edge94" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1522.44,-1280.83C1504,-1263.94 1513.33,-1235.66 1525.5,-1213.56"/>
<polygon fill="#85f910" stroke="black" points="1528.36,-1215.61 1530.43,-1205.22 1522.33,-1212.05 1528.36,-1215.61"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_depends -->
<g id="edge95" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1522.44,-1280.83C1473.01,-1235.55 1434.44,-1059.32 1434.44,-992.28 1434.44,-992.28 1434.44,-992.28 1434.44,-887.78 1434.44,-837.84 1446.87,-815.84 1415.44,-777.03 1326,-666.64 1131.42,-800.27 1118.44,-658.78"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_events -->
<g id="edge96" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1679.44,-1594.07C1660.59,-1574.99 1690.3,-1562.01 1693.44,-1535.38 1696.83,-1506.51 1708.85,-1494.62 1693.44,-1469.98 1674.04,-1438.97 1646.18,-1458.93 1619.44,-1433.98 1588.16,-1404.79 1606.94,-1374.41 1571.44,-1350.54 1472.8,-1284.22 1422.82,-1338.66 1306.44,-1314.54 1201.12,-1292.71 1179.12,-1269.14 1073.44,-1249.13 916.75,-1219.47 868.94,-1259.77 716.44,-1213.13 712.18,-1211.83 707.88,-1210.21 703.64,-1208.39"/>
<polygon fill="#85f910" stroke="black" points="705.2,-1205.26 694.66,-1204.19 702.23,-1211.6 705.2,-1205.26"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_plugin -->
<g id="edge97" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1869.44,-1501.68C1857.46,-1430.07 1862.62,-1397.72 1807.44,-1350.54 1757.96,-1308.23 1571.21,-1325.94 1522.44,-1282.83"/>
<path fill="none" stroke="black" d="M1522.44,-1280.83C1509.39,-1268.91 1511.83,-1260.66 1498.44,-1249.13 1473.12,-1227.32 1454.34,-1238.36 1432.44,-1213.13 1364.57,-1134.96 1358.44,-1095.8 1358.44,-992.28 1358.44,-992.28 1358.44,-992.28 1358.44,-954.28 1358.44,-835.63 1232.44,-837.57 1232.44,-718.91 1232.44,-718.91 1232.44,-718.91 1232.44,-597.66 1232.44,-357.42 941.84,-521.87 710.44,-457.28 709.45,-457.01 708.46,-456.72 707.47,-456.41"/>
<polygon fill="#85f910" stroke="black" points="708.85,-453.19 698.25,-453.15 706.51,-459.78 708.85,-453.19"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_service -->
<g id="edge98" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1793.44,-1391.26C1777.34,-1379.44 1790.52,-1362.37 1774.44,-1350.54 1678.35,-1279.83 1611.45,-1374.71 1508.44,-1314.54 1493.18,-1305.62 1501.36,-1287.93 1484.44,-1282.83"/>
<path fill="none" stroke="black" d="M1484.44,-1280.83C1456.68,-1272.48 1455.08,-1257.85 1427.44,-1249.13 1317.74,-1214.54 975.14,-1192.41 835.05,-1184.59"/>
<polygon fill="#85f910" stroke="black" points="835.42,-1181.11 825.24,-1184.05 835.03,-1188.1 835.42,-1181.11"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_config_models -->
<g id="edge99" class="edge">
<title>plugginger_core&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1915.5,-1678.45C1991.47,-1664.93 2139.61,-1637.29 2189.44,-1618.76 2230.34,-1603.55 2237,-1592.05 2275.44,-1571.38 2301.95,-1557.13 2331.68,-1541.5 2355.92,-1528.85"/>
<polygon fill="#85f910" stroke="black" points="2357.35,-1532.05 2364.6,-1524.32 2354.12,-1525.84 2357.35,-1532.05"/>
</g>
<!-- plugginger_implementations_container -->
<g id="node39" class="node">
<title>plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1039.56,-1525.8 937.31,-1525.8 937.31,-1479.55 1039.56,-1479.55 1039.56,-1525.8"/>
<text text-anchor="middle" x="988.44" y="-1512.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="988.44" y="-1499.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">implementations.</text>
<text text-anchor="middle" x="988.44" y="-1486.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">container</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_container -->
<g id="edge100" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1104.44,-1594.07C1070.34,-1581.66 1038.34,-1555.17 1016.63,-1534.05"/>
<polygon fill="#85f910" stroke="black" points="1019.16,-1531.64 1009.62,-1527.05 1014.22,-1536.59 1019.16,-1531.64"/>
</g>
<!-- plugginger_implementations_events -->
<g id="node40" class="node">
<title>plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#61883a" stroke="black" cx="1457.44" cy="-1392.26" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="1457.44" y="-1401.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1457.44" y="-1389.13" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">implementations.</text>
<text text-anchor="middle" x="1457.44" y="-1376.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">events</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_events -->
<g id="edge101" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1679.44,-1594.07C1650.63,-1566.07 1698.7,-1538.93 1679.44,-1503.68"/>
<path fill="none" stroke="black" d="M1679.44,-1501.68C1671.56,-1487.26 1673.37,-1480.11 1660.44,-1469.98 1617.61,-1436.42 1594.15,-1453.67 1543.44,-1433.98 1534.26,-1430.41 1524.7,-1426.3 1515.43,-1422.09"/>
<polygon fill="#85f910" stroke="black" points="1517.04,-1418.98 1506.49,-1417.96 1514.1,-1425.34 1517.04,-1418.98"/>
</g>
<!-- plugginger_implementations_services -->
<g id="node41" class="node">
<title>plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#668f3d" stroke="black" cx="1294.44" cy="-1392.26" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="1294.44" y="-1401.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1294.44" y="-1389.13" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">implementations.</text>
<text text-anchor="middle" x="1294.44" y="-1376.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">services</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_services -->
<g id="edge102" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1827.81,-1674.26C1792.93,-1662.94 1742.68,-1644.04 1703.44,-1618.76 1691.1,-1610.81 1689.96,-1606.3 1679.44,-1596.07"/>
<path fill="none" stroke="black" d="M1679.44,-1594.07C1632.52,-1548.48 1599.86,-1570.16 1544.44,-1535.38 1506.69,-1511.7 1506.62,-1492.96 1468.44,-1469.98 1430.82,-1447.33 1416.58,-1451.76 1376.44,-1433.98 1368.36,-1430.4 1359.93,-1426.46 1351.68,-1422.48"/>
<polygon fill="#85f910" stroke="black" points="1353.25,-1419.35 1342.72,-1418.11 1350.18,-1425.64 1353.25,-1419.35"/>
</g>
<!-- plugginger_interfaces_events -->
<g id="node43" class="node">
<title>plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75b03a" stroke="black" cx="1602.44" cy="-1502.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1602.44" y="-1512.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1602.44" y="-1499.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces.</text>
<text text-anchor="middle" x="1602.44" y="-1486.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">events</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_interfaces_events -->
<g id="edge103" class="edge">
<title>plugginger_core&#45;&gt;plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1679.44,-1594.07C1662.44,-1577.56 1645.18,-1557.64 1631.32,-1540.72"/>
<polygon fill="#85f910" stroke="black" points="1634.37,-1538.92 1625.36,-1533.35 1628.93,-1543.32 1634.37,-1538.92"/>
</g>
<!-- plugginger_interfaces_services -->
<g id="node44" class="node">
<title>plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75b03a" stroke="black" cx="1410.44" cy="-1502.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1410.44" y="-1512.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1410.44" y="-1499.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces.</text>
<text text-anchor="middle" x="1410.44" y="-1486.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_interfaces_services -->
<g id="edge104" class="edge">
<title>plugginger_core&#45;&gt;plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1828.03,-1673.91C1786.54,-1661.06 1721.03,-1640.01 1665.44,-1618.76 1578.54,-1585.55 1557.69,-1574.84 1473.44,-1535.38 1468.7,-1533.16 1463.78,-1530.8 1458.88,-1528.4"/>
<polygon fill="#85f910" stroke="black" points="1460.79,-1525.44 1450.27,-1524.15 1457.69,-1531.72 1460.79,-1525.44"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_stubgen -->
<g id="edge105" class="edge">
<title>plugginger_core&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1320.44,-1179.43C1302.95,-1098.11 1320.44,-1075.46 1320.44,-992.28 1320.44,-992.28 1320.44,-992.28 1320.44,-954.28 1320.44,-875.44 1319.61,-855.48 1327.44,-777.03 1332.72,-724.07 1346.44,-712.01 1346.44,-658.78 1346.44,-658.78 1346.44,-658.78 1346.44,-597.66 1346.44,-550.06 1356.97,-526.04 1322.44,-493.28 1294.82,-467.08 679.78,-395.11 647.44,-375.03 616.8,-356.02 625.69,-334.34 599.44,-309.63 585.04,-296.07 567.11,-283.75 550.99,-273.94"/>
<polygon fill="#85f910" stroke="black" points="552.92,-271.01 542.53,-268.93 549.35,-277.04 552.92,-271.01"/>
</g>
<!-- plugginger_testing_collectors -->
<g id="node47" class="node">
<title>plugginger_testing_collectors</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#75bc2f" stroke="black" cx="237.44" cy="-342.33" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="237.44" y="-351.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="237.44" y="-339.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">testing.</text>
<text text-anchor="middle" x="237.44" y="-326.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">collectors</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_testing_collectors -->
<g id="edge106" class="edge">
<title>plugginger_core&#45;&gt;plugginger_testing_collectors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1870.7,-1663.37C1871.39,-1645.06 1871.71,-1618.87 1869.44,-1596.07"/>
<path fill="none" stroke="black" d="M1869.44,-1594.07C1865.67,-1556.32 1831.05,-1565.09 1807.44,-1535.38 1797.85,-1523.32 1804.02,-1514.87 1793.44,-1503.68"/>
<path fill="none" stroke="black" d="M1793.44,-1501.68C1745.66,-1451.18 1702.28,-1482.43 1652.44,-1433.98 1622.52,-1404.89 1643.54,-1374.56 1609.44,-1350.54 1510.47,-1280.81 1425.43,-1399.74 1339.44,-1314.54 1296.99,-1272.48 1330.29,-1240.37 1320.44,-1181.43"/>
<path fill="none" stroke="black" d="M1320.44,-1179.43C1312.15,-1139.48 1276.24,-1148.44 1258.44,-1111.72 1181.26,-952.56 1194.44,-895.79 1194.44,-718.91 1194.44,-718.91 1194.44,-718.91 1194.44,-657.78 1194.44,-386.3 883.97,-508.91 617.44,-457.28 474.54,-429.61 436.66,-426.32 300.44,-375.03 295.47,-373.16 290.38,-371.02 285.35,-368.74"/>
<polygon fill="#85f910" stroke="black" points="287.07,-365.69 276.54,-364.59 284.09,-372.02 287.07,-365.69"/>
</g>
<!-- plugginger_core_config -->
<g id="node34" class="node">
<title>plugginger_core_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85f910" stroke="black" cx="2117.44" cy="-1687.46" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2117.44" y="-1697.09" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2117.44" y="-1684.34" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="2117.44" y="-1671.59" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger -->
<g id="edge107" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2092.83,-1658.69C2022.8,-1579.8 1826.25,-1359.48 1807.44,-1350.54 1691.42,-1295.36 1358.14,-1328.61 1230.44,-1314.54 1177.05,-1308.65 1116.15,-1298.5 1075.72,-1291.27"/>
<polygon fill="#85f910" stroke="black" points="1076.58,-1287.87 1066.12,-1289.54 1075.34,-1294.76 1076.58,-1287.87"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge108" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2080.81,-1664.92C2059.19,-1651.96 2031.45,-1634.89 2007.44,-1618.76 1955.33,-1583.75 1943.41,-1573.37 1893.44,-1535.38 1846.97,-1500.07 1794.71,-1458.6 1758.51,-1429.6"/>
<polygon fill="#85f910" stroke="black" points="1760.84,-1426.98 1750.85,-1423.45 1756.46,-1432.44 1760.84,-1426.98"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge109" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2151.29,-1663.25C2165.51,-1651.62 2180.74,-1636.28 2189.44,-1618.76 2283.08,-1430.21 2209.41,-1356.1 2239.44,-1147.72 2244.38,-1113.42 2266.7,-1103.02 2253.44,-1071"/>
<path fill="none" stroke="black" d="M2253.44,-1069C2204.03,-963.96 2115.53,-993.52 2063.44,-889.78"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_api_app -->
<g id="edge110" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2165.46,-1678.66C2286.67,-1657.63 2595.44,-1595.08 2595.44,-1503.68 2595.44,-1503.68 2595.44,-1503.68 2595.44,-1280.83 2595.44,-1146.27 2524.58,-1122.43 2428.44,-1028.28 2389.38,-990.03 2365.54,-996.33 2329.44,-955.28"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_config_models -->
<g id="edge111" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2166.86,-1683.58C2224.65,-1677.33 2318.49,-1657.68 2366.44,-1596.07"/>
<path fill="none" stroke="black" d="M2366.44,-1594.07C2377.48,-1579.88 2385.88,-1562.01 2391.94,-1545.92"/>
<polygon fill="#85f910" stroke="black" points="2395.21,-1547.14 2395.25,-1536.55 2388.62,-1544.81 2395.21,-1547.14"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_implementations_events -->
<g id="edge112" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2073.19,-1672.42C2036.15,-1660.05 1982.3,-1640.63 1937.44,-1618.76 1880.35,-1590.93 1752.14,-1494.21 1693.44,-1469.98 1630.07,-1443.81 1608.1,-1456.77 1543.44,-1433.98 1533.83,-1430.59 1523.85,-1426.47 1514.25,-1422.17"/>
<polygon fill="#85f910" stroke="black" points="1515.93,-1419.09 1505.39,-1418.09 1513.01,-1425.45 1515.93,-1419.09"/>
</g>
<!-- plugginger_core_constants -->
<g id="node35" class="node">
<title>plugginger_core_constants</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85f910" stroke="black" cx="541.44" cy="-1392.26" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="541.44" y="-1401.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="541.44" y="-1389.13" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="541.44" y="-1376.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">constants</text>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge113" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M656.44,-1280.83C682.7,-1268.56 685.53,-1256.98 713.44,-1249.13 807.42,-1222.68 1507.49,-1262.99 1591.44,-1213.13 1605.56,-1204.74 1595,-1187.04 1610.44,-1181.43"/>
<path fill="none" stroke="black" d="M1610.44,-1179.43C1698.64,-1147.38 1725.94,-1152.55 1810.44,-1111.72 1815.58,-1109.24 1820.81,-1106.41 1825.95,-1103.42"/>
<polygon fill="#85f910" stroke="black" points="1827.63,-1106.5 1834.37,-1098.33 1824,-1100.51 1827.63,-1106.5"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_builder -->
<g id="edge114" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M563.02,-1362.35C583.88,-1336.79 617.91,-1300.84 656.44,-1282.83"/>
<path fill="none" stroke="black" d="M656.44,-1280.83C682.7,-1268.56 685.56,-1257.08 713.44,-1249.13 1033.07,-1157.95 1134.6,-1278.77 1460.44,-1213.13 1474.1,-1210.38 1488.49,-1205.85 1501.55,-1201.07"/>
<polygon fill="#85f910" stroke="black" points="1502.65,-1204.4 1510.74,-1197.56 1500.15,-1197.86 1502.65,-1204.4"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_events -->
<g id="edge115" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M580.44,-1280.83C591.73,-1257.74 608.92,-1234.87 624.19,-1216.89"/>
<polygon fill="#85f910" stroke="black" points="626.59,-1219.46 630.51,-1209.62 621.31,-1214.87 626.59,-1219.46"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_plugin -->
<g id="edge116" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M550.78,-1359.67C557.78,-1337.63 568.22,-1307.81 580.44,-1282.83"/>
<path fill="none" stroke="black" d="M580.44,-1280.83C624,-1191.73 504.44,-1170.19 504.44,-1071 504.44,-1071 504.44,-1071 504.44,-887.78 504.44,-722.1 599.5,-540.2 641.68,-467.64"/>
<polygon fill="#85f910" stroke="black" points="644.68,-469.45 646.74,-459.05 638.65,-465.9 644.68,-469.45"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_service -->
<g id="edge117" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M580.44,-1280.83C588.2,-1264.96 590.04,-1259.37 604.44,-1249.13 646.7,-1219.07 667.74,-1233.49 715.44,-1213.13 719.41,-1211.43 723.48,-1209.56 727.53,-1207.6"/>
<polygon fill="#85f910" stroke="black" points="728.77,-1210.89 736.13,-1203.27 725.62,-1204.64 728.77,-1210.89"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_stubgen -->
<g id="edge118" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M521.6,-1361.81C501.93,-1332.84 470.72,-1287.5 442.44,-1249.13 322.31,-1086.15 162.44,-1092.25 162.44,-889.78 162.44,-889.78 162.44,-889.78 162.44,-597.66 162.44,-474.24 250.38,-470.64 328.44,-375.03 339.61,-361.34 339.89,-355.77 352.44,-343.33"/>
<path fill="none" stroke="black" d="M352.44,-341.33C383.12,-309.91 427.04,-285.77 460.46,-270.38"/>
<polygon fill="#85f910" stroke="black" points="461.84,-273.6 469.53,-266.31 458.97,-267.21 461.84,-273.6"/>
</g>
<!-- plugginger_core_exceptions -->
<g id="node36" class="node">
<title>plugginger_core_exceptions</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85f910" stroke="black" cx="832.44" cy="-1687.46" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="832.44" y="-1697.09" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="832.44" y="-1684.34" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="832.44" y="-1671.59" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">exceptions</text>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger -->
<g id="edge119" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M810.53,-1657.9C778.99,-1613.06 729.26,-1525.52 774.44,-1469.98 823.99,-1409.05 887.35,-1484.55 947.44,-1433.98 977.95,-1408.29 961.1,-1385.41 980.44,-1350.54 988.51,-1335.98 999.31,-1320.96 1008.8,-1308.79"/>
<polygon fill="#85f910" stroke="black" points="1011.3,-1311.27 1014.79,-1301.26 1005.82,-1306.9 1011.3,-1311.27"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_graph -->
<g id="edge120" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M862.55,-1661.37C879.9,-1644.86 900.08,-1621.62 909.44,-1596.07"/>
<path fill="none" stroke="black" d="M909.44,-1594.07C918.34,-1569.76 899.98,-1547.56 878.97,-1531.24"/>
<polygon fill="#85f910" stroke="black" points="881.24,-1528.56 871.09,-1525.5 877.12,-1534.22 881.24,-1528.56"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_proxy -->
<g id="edge121" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M426.44,-1391.26C459.5,-1156.96 362.94,-1080.3 447.44,-859.28 525.22,-655.81 577.12,-587.69 773.44,-493.28 837.5,-462.48 1329.96,-442.4 1485.51,-436.77"/>
<polygon fill="#85f910" stroke="black" points="1485.59,-440.27 1495.45,-436.42 1485.34,-433.28 1485.59,-440.27"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge122" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M909.44,-1594.07C923.25,-1556.35 885.17,-1535.7 909.44,-1503.68"/>
<path fill="none" stroke="black" d="M909.44,-1501.68C919.36,-1488.59 915.61,-1480.24 928.44,-1469.98 968.27,-1438.1 989.08,-1450.23 1037.44,-1433.98 1090.42,-1416.16 1109.63,-1423.82 1156.44,-1393.26"/>
<path fill="none" stroke="black" d="M1156.44,-1391.26C1182.51,-1374.23 1186.11,-1365.45 1213.44,-1350.54 1253.57,-1328.63 1302.31,-1309.96 1337.29,-1297.8"/>
<polygon fill="#85f910" stroke="black" points="1338.38,-1301.13 1346.71,-1294.58 1336.11,-1294.51 1338.38,-1301.13"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge123" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M868.05,-1664.26C941.97,-1619.34 1118.73,-1517.89 1281.44,-1469.98 1406.03,-1433.29 1444.18,-1459.95 1571.44,-1433.98 1599.13,-1428.32 1629.29,-1419.99 1654.66,-1412.34"/>
<polygon fill="#85f910" stroke="black" points="1655.61,-1415.71 1664.14,-1409.43 1653.56,-1409.01 1655.61,-1415.71"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge124" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M580.44,-1179.43C593.14,-1169 585.82,-1156.92 599.44,-1147.72 684.08,-1090.54 726.16,-1131.18 826.44,-1111.72 905.68,-1096.34 938.83,-1119.4 1003.44,-1071"/>
<path fill="none" stroke="black" d="M1003.44,-1069C1042.86,-1039.47 1054.81,-1032.38 1083.44,-992.28"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_validation -->
<g id="edge125" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M426.44,-1594.07C393.72,-1570.76 415.31,-1542.28 426.44,-1503.68"/>
<path fill="none" stroke="black" d="M426.44,-1501.68C434.6,-1473.36 416.52,-1444.68 397.66,-1423.94"/>
<polygon fill="#85f910" stroke="black" points="400.39,-1421.72 390.94,-1416.93 395.34,-1426.57 400.39,-1421.72"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_app -->
<g id="edge126" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M426.44,-1391.26C423.58,-1331.96 464.83,-1327.05 504.44,-1282.83"/>
<path fill="none" stroke="black" d="M504.44,-1280.83C514.71,-1269.36 510.01,-1262.03 518.44,-1249.13 540.75,-1214.97 548.9,-1207.31 580.44,-1181.43"/>
<path fill="none" stroke="black" d="M580.44,-1179.43C593.14,-1169 589.22,-1160.59 599.44,-1147.72 676.33,-1050.87 709.33,-1038.98 799.44,-954.28 845.05,-911.41 855.03,-898.98 903.44,-859.28 951.19,-820.12 961.34,-806.82 1015.44,-777.03 1103.24,-728.68 1129.21,-722.84 1225.44,-694.78 1382.83,-648.9 1575.5,-618.44 1662.66,-606"/>
<polygon fill="#85f910" stroke="black" points="1663.08,-609.47 1672.5,-604.61 1662.11,-602.54 1663.08,-609.47"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin -->
<g id="edge127" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M426.44,-1501.68C431.51,-1480.63 407.41,-1482.06 389.44,-1469.98 360.4,-1450.46 343.8,-1459.08 319.44,-1433.98 278.47,-1391.77 279.54,-1370.81 262.44,-1314.54 206.95,-1132 200.44,-1080.56 200.44,-889.78 200.44,-889.78 200.44,-889.78 200.44,-799.16 200.44,-658.86 178.66,-591.91 278.44,-493.28 388.7,-384.3 466.08,-444.6 617.44,-411.03 696.66,-393.46 722.06,-407.49 796.44,-375.03 798.75,-374.02 801.06,-372.88 803.35,-371.64"/>
<polygon fill="#85f910" stroke="black" points="805.1,-374.67 811.82,-366.48 801.46,-368.69 805.1,-374.67"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_background -->
<g id="edge128" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M618.44,-1391.26C652.24,-1360.46 695.19,-1331.22 727.46,-1310.88"/>
<polygon fill="#85f910" stroke="black" points="729.2,-1313.92 735.83,-1305.66 725.49,-1307.98 729.2,-1313.92"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_builder -->
<g id="edge129" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M426.44,-1501.68C453.12,-1409.07 543.78,-1471.77 632.44,-1433.98 796.07,-1364.21 816.22,-1297.37 987.44,-1249.13 1190.37,-1191.95 1254.36,-1257.64 1460.44,-1213.13 1474.06,-1210.19 1488.44,-1205.6 1501.5,-1200.82"/>
<polygon fill="#85f910" stroke="black" points="1502.59,-1204.15 1510.69,-1197.33 1500.1,-1197.61 1502.59,-1204.15"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_depends -->
<g id="edge130" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M426.44,-1501.68C437.71,-1454.83 428.76,-1441.39 426.44,-1393.26"/>
<path fill="none" stroke="black" d="M426.44,-1391.26C423.35,-1327.11 431.02,-1309.67 452.44,-1249.13 554.74,-959.99 761.18,-648 829.17,-549.24"/>
<polygon fill="#85f910" stroke="black" points="832.01,-551.29 834.81,-541.07 826.25,-547.31 832.01,-551.29"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_events -->
<g id="edge131" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M504.44,-1280.83C516.23,-1267.67 514.96,-1260.56 528.44,-1249.13 531.96,-1246.14 573.47,-1224.52 608.46,-1206.5"/>
<polygon fill="#85f910" stroke="black" points="609.95,-1209.67 617.24,-1201.98 606.75,-1203.44 609.95,-1209.67"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_plugin -->
<g id="edge132" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M782.58,-1686.58C706.81,-1684.84 559.93,-1673.88 450.44,-1618.76 437.33,-1612.16 438.39,-1604.59 426.44,-1596.07"/>
<path fill="none" stroke="black" d="M426.44,-1594.07C281.73,-1491 193.28,-1424.04 224.44,-1249.13 235.95,-1184.51 395.36,-751.47 428.44,-694.78 483.43,-600.53 576.44,-510.34 626.7,-465.19"/>
<polygon fill="#85f910" stroke="black" points="629.01,-467.82 634.15,-458.55 624.35,-462.59 629.01,-467.82"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_service -->
<g id="edge133" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M426.44,-1501.68C449.3,-1422.34 539.23,-1490.47 599.44,-1433.98 614,-1420.31 603.67,-1406.71 618.44,-1393.26"/>
<path fill="none" stroke="black" d="M618.44,-1391.26C643.91,-1368.05 658.8,-1275.95 680.44,-1249.13 693.32,-1233.17 710.85,-1219.18 727.29,-1208.12"/>
<polygon fill="#85f910" stroke="black" points="729.04,-1211.16 735.53,-1202.78 725.23,-1205.29 729.04,-1211.16"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_container -->
<g id="edge134" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M909.44,-1594.07C918,-1570.69 935.77,-1549.45 952.29,-1533.44"/>
<polygon fill="#85f910" stroke="black" points="954.22,-1536.43 959.15,-1527.05 949.45,-1531.31 954.22,-1536.43"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_events -->
<g id="edge135" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M909.44,-1501.68C919.36,-1488.59 914.48,-1478.64 928.44,-1469.98 1013.1,-1417.41 1279.41,-1460.62 1375.44,-1433.98 1384.9,-1431.35 1394.56,-1427.62 1403.79,-1423.48"/>
<polygon fill="#85f910" stroke="black" points="1405.07,-1426.74 1412.61,-1419.3 1402.08,-1420.42 1405.07,-1426.74"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_services -->
<g id="edge136" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M909.44,-1501.68C919.36,-1488.59 914.78,-1479.11 928.44,-1469.98 1018.84,-1409.54 1064.62,-1459.03 1170.44,-1433.98 1189.51,-1429.46 1209.86,-1423.34 1228.44,-1417.23"/>
<polygon fill="#85f910" stroke="black" points="1229.34,-1420.61 1237.71,-1414.12 1227.12,-1413.98 1229.34,-1420.61"/>
</g>
<!-- plugginger_core_types -->
<g id="node37" class="node">
<title>plugginger_core_types</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85f910" stroke="black" cx="1066.44" cy="-1687.46" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1066.44" y="-1697.09" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1066.44" y="-1684.34" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1066.44" y="-1671.59" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">types</text>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge137" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1143.44,-1501.68C1142.12,-1453.16 1115.8,-1419.79 1156.44,-1393.26"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge138" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1244.44,-1280.83C1280.77,-1255.13 1296.12,-1259.36 1339.44,-1249.13 1449.54,-1223.12 1497.2,-1275.74 1591.44,-1213.13 1605.12,-1204.04 1594.88,-1186.7 1610.44,-1181.43"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge139" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1410.44,-1594.07C1418.49,-1585.38 1416.63,-1580.3 1424.44,-1571.38 1441.08,-1552.37 1449.08,-1551.63 1468.44,-1535.38 1502.57,-1506.74 1506.88,-1493.96 1544.44,-1469.98 1579.48,-1447.6 1622.13,-1428.29 1655.82,-1414.64"/>
<polygon fill="#85f910" stroke="black" points="1657.02,-1417.92 1665.01,-1410.97 1654.43,-1411.42 1657.02,-1417.92"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge140" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1066.85,-1654.27C1067.79,-1600.21 1070.83,-1487.81 1080.44,-1393.26"/>
<path fill="none" stroke="black" d="M1080.44,-1391.26C1096.03,-1329.97 1086.73,-1310.96 1073.44,-1249.13 1055.55,-1165.97 934.59,-1120.95 1003.44,-1071"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge141" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1093.72,-1660.07C1110.01,-1643.06 1130.06,-1619.77 1143.44,-1596.07"/>
<path fill="none" stroke="black" d="M1143.44,-1594.07C1163.18,-1559.08 1144.53,-1543.84 1143.44,-1503.68"/>
<path fill="none" stroke="black" d="M1143.44,-1501.68C1141.61,-1434.53 1110.68,-1409.73 1142.44,-1350.54 1155.3,-1326.56 1222.23,-1298.55 1244.44,-1282.83"/>
<path fill="none" stroke="black" d="M1244.44,-1280.83C1334.66,-1212.02 1245.62,-855.65 1327.44,-777.03 1356.64,-748.98 1619.73,-729.63 1738.6,-722.24"/>
<polygon fill="#85f910" stroke="black" points="1738.7,-725.74 1748.47,-721.63 1738.27,-718.75 1738.7,-725.74"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_app_plugin -->
<g id="edge142" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1080.44,-1391.26C1125.28,-1195.96 1136.57,-1147 1191.44,-954.28 1206.02,-903.07 1354.81,-535.56 1322.44,-493.28 1199.84,-333.2 1066.52,-461.65 884.44,-375.03 882.31,-374.02 880.18,-372.91 878.07,-371.72"/>
<polygon fill="#85f910" stroke="black" points="880.01,-368.8 869.67,-366.51 876.32,-374.75 880.01,-368.8"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_depends -->
<g id="edge143" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M464.44,-1594.07C444.11,-1555.77 733.32,-1374.54 769.44,-1350.54 797.26,-1332.04 816.32,-1341.94 835.44,-1314.54 861.51,-1277.17 843.98,-1258.23 850.44,-1213.13 876.85,-1028.66 804.44,-940.27 928.44,-801.16"/>
<path fill="none" stroke="black" d="M928.44,-799.16C935.73,-790.09 934.06,-785.11 942.44,-777.03 979.46,-741.37 1005.23,-754.38 1042.44,-718.91"/>
<path fill="none" stroke="black" d="M1042.44,-716.91C1057.83,-702.24 1121.18,-679.87 1118.44,-658.78"/>
<path fill="none" stroke="black" d="M1118.44,-657.78C1105.89,-561.41 971.23,-531 898.09,-521.55"/>
<polygon fill="#85f910" stroke="black" points="898.76,-518.11 888.41,-520.39 897.92,-525.06 898.76,-518.11"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_events -->
<g id="edge144" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M464.44,-1594.07C439.66,-1545.77 461.08,-1400 483.44,-1350.54 492.63,-1330.2 504.02,-1331.57 518.44,-1314.54 541.73,-1287.01 541.02,-1274.71 566.44,-1249.13 580.47,-1235 597.63,-1221.47 613.24,-1210.26"/>
<polygon fill="#85f910" stroke="black" points="614.81,-1213.44 620.98,-1204.82 610.79,-1207.71 614.81,-1213.44"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_service -->
<g id="edge145" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1021.91,-1672.67C959.97,-1651.61 847.24,-1606.24 774.44,-1535.38 681.22,-1444.66 666.8,-1370.56 713.44,-1249.13 718.31,-1236.44 726.79,-1224.64 735.9,-1214.56"/>
<polygon fill="#85f910" stroke="black" points="738.17,-1217.24 742.58,-1207.61 733.13,-1212.39 738.17,-1217.24"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_container -->
<g id="edge146" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1053.21,-1655.46C1039.09,-1622.37 1016.81,-1570.17 1002.41,-1536.43"/>
<polygon fill="#85f910" stroke="black" points="1005.77,-1535.39 998.63,-1527.56 999.34,-1538.14 1005.77,-1535.39"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_events -->
<g id="edge147" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1115.17,-1680.36C1200.5,-1668.63 1371.6,-1640.14 1410.44,-1596.07"/>
<path fill="none" stroke="black" d="M1410.44,-1594.07C1434.68,-1566.56 1366.37,-1569.3 1352.44,-1535.38 1341.39,-1508.49 1339.27,-1495.89 1352.44,-1469.98 1362.45,-1450.26 1380.16,-1434.4 1398.23,-1422.34"/>
<polygon fill="#85f910" stroke="black" points="1399.8,-1425.48 1406.39,-1417.18 1396.06,-1419.56 1399.8,-1425.48"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_services -->
<g id="edge148" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1143.44,-1594.07C1179.45,-1534.33 1229.39,-1470.87 1261.8,-1431.65"/>
<polygon fill="#85f910" stroke="black" points="1264.18,-1434.27 1267.88,-1424.34 1258.79,-1429.79 1264.18,-1434.27"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_interfaces_events -->
<g id="edge149" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1115.88,-1683.16C1186.54,-1677.1 1319.45,-1660.74 1424.44,-1618.76 1476.03,-1598.13 1528.69,-1561.79 1563.21,-1535.46"/>
<polygon fill="#85f910" stroke="black" points="1565.04,-1538.47 1570.82,-1529.59 1560.76,-1532.93 1565.04,-1538.47"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_interfaces_services -->
<g id="edge150" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1410.44,-1594.07C1421.73,-1581.25 1423.42,-1562.99 1421.63,-1546.23"/>
<polygon fill="#85f910" stroke="black" points="1425.11,-1545.8 1420.14,-1536.45 1418.19,-1546.86 1425.11,-1545.8"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_testing_collectors -->
<g id="edge151" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_testing_collectors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1019.09,-1677.39C983.71,-1670.74 934.13,-1661.7 890.44,-1654.76 796.06,-1639.76 528.45,-1667.02 464.44,-1596.07"/>
<path fill="none" stroke="black" d="M464.44,-1594.07C357.6,-1475.67 302.32,-1461.1 239.44,-1314.54 147.25,-1099.65 124.44,-1034.98 124.44,-801.16 124.44,-801.16 124.44,-801.16 124.44,-515.41 124.44,-459.73 166.29,-408.09 199.05,-376.19"/>
<polygon fill="#85f910" stroke="black" points="201.2,-378.97 206.06,-369.56 196.39,-373.89 201.2,-378.97"/>
</g>
<!-- plugginger_implementations -->
<g id="node38" class="node">
<title>plugginger_implementations</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85ce3b" stroke="black" cx="868.44" cy="-1392.26" rx="70.18" ry="23.69"/>
<text text-anchor="middle" x="868.44" y="-1395.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="868.44" y="-1382.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">implementations</text>
</g>
<!-- plugginger_implementations&#45;&gt;plugginger -->
<g id="edge152" class="edge">
<title>plugginger_implementations&#45;&gt;plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M899.29,-1370.61C926.67,-1352.28 966.66,-1325.52 995.27,-1306.37"/>
<polygon fill="#85ce3b" stroke="black" points="996.89,-1309.5 1003.25,-1301.03 992.99,-1303.68 996.89,-1309.5"/>
</g>
<!-- plugginger_implementations&#45;&gt;plugginger_api_depends -->
<g id="edge153" class="edge">
<title>plugginger_implementations&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M880.69,-1368.45C888.55,-1353.37 898.69,-1333.05 906.44,-1314.54 924.83,-1270.55 928.86,-1259.12 941.44,-1213.13 956.22,-1159.07 1004,-759.7 1042.44,-718.91"/>
</g>
<!-- plugginger_implementations_container&#45;&gt;plugginger -->
<g id="edge154" class="edge">
<title>plugginger_implementations_container&#45;&gt;plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M992.77,-1479.08C1000.47,-1438.97 1016.51,-1355.4 1024.96,-1311.38"/>
<polygon fill="blue" stroke="black" points="1028.35,-1312.3 1026.79,-1301.82 1021.47,-1310.98 1028.35,-1312.3"/>
</g>
<!-- plugginger_implementations_container&#45;&gt;plugginger_api_depends -->
<g id="edge155" class="edge">
<title>plugginger_implementations_container&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M945.24,-1479.19C938.05,-1475.87 930.61,-1472.66 923.44,-1469.98 865.68,-1448.36 826.96,-1482.91 789.44,-1433.98 766.87,-1404.55 769.38,-1381.73 789.44,-1350.54 810.31,-1318.09 842.48,-1343.09 868.44,-1314.54 900.51,-1279.26 894.13,-1259.89 903.44,-1213.13 921.34,-1123.18 877.88,-877.68 928.44,-801.16"/>
</g>
<!-- plugginger_interfaces -->
<g id="node42" class="node">
<title>plugginger_interfaces</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#85ce3b" stroke="black" cx="1295.44" cy="-1595.07" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="1295.44" y="-1598.32" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1295.44" y="-1585.57" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces</text>
</g>
<!-- plugginger_interfaces&#45;&gt;plugginger_implementations_events -->
<g id="edge156" class="edge">
<title>plugginger_interfaces&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1295.44,-1501.68C1300.91,-1484.88 1306.09,-1481.56 1319.44,-1469.98 1332.84,-1458.34 1367.99,-1438.85 1399.46,-1422.42"/>
<polygon fill="#85ce3b" stroke="black" points="1400.9,-1425.62 1408.17,-1417.91 1397.68,-1419.4 1400.9,-1425.62"/>
</g>
<!-- plugginger_interfaces&#45;&gt;plugginger_implementations_services -->
<g id="edge157" class="edge">
<title>plugginger_interfaces&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1291.6,-1571.17C1289.37,-1552.52 1288.24,-1525.76 1295.44,-1503.68"/>
<path fill="none" stroke="black" d="M1295.44,-1501.68C1302.21,-1480.88 1302.63,-1456.63 1301.07,-1436.34"/>
<polygon fill="#85ce3b" stroke="black" points="1304.55,-1436.03 1300.1,-1426.41 1297.59,-1436.71 1304.55,-1436.03"/>
</g>
<!-- plugginger_interfaces_events&#45;&gt;plugginger_implementations_events -->
<g id="edge158" class="edge">
<title>plugginger_interfaces_events&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1570.26,-1477.62C1550.55,-1462.88 1525,-1443.78 1503.21,-1427.48"/>
<polygon fill="#75b03a" stroke="black" points="1505.44,-1424.78 1495.34,-1421.6 1501.25,-1430.39 1505.44,-1424.78"/>
</g>
<!-- plugginger_interfaces_services&#45;&gt;plugginger_implementations_services -->
<g id="edge159" class="edge">
<title>plugginger_interfaces_services&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1382.36,-1475.43C1367.62,-1461.66 1349.35,-1444.58 1333.28,-1429.56"/>
<polygon fill="#75b03a" stroke="black" points="1335.91,-1427.23 1326.21,-1422.96 1331.13,-1432.34 1335.91,-1427.23"/>
</g>
<!-- plugginger_stubgen&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge160" class="edge">
<title>plugginger_stubgen&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M546.4,-234.32C586.41,-218.21 645.76,-192.11 661.44,-173.25"/>
<path fill="none" stroke="black" d="M661.44,-171.25C672.95,-157.4 684.74,-141.27 694.35,-127.52"/>
<polygon fill="#6ba135" stroke="black" points="696.96,-129.89 699.77,-119.68 691.2,-125.92 696.96,-129.89"/>
</g>
<!-- plugginger_testing -->
<g id="node46" class="node">
<title>plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="342.44,-190.25 272.44,-190.25 272.44,-154.25 342.44,-154.25 342.44,-190.25"/>
<text text-anchor="middle" x="307.44" y="-175.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="307.44" y="-162.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">testing</text>
</g>
<!-- plugginger_testing_collectors&#45;&gt;plugginger_testing -->
<g id="edge161" class="edge">
<title>plugginger_testing_collectors&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M240.6,-309.22C243.9,-285.36 250.5,-252.53 263.44,-226.25 268.25,-216.48 275.2,-206.95 282.17,-198.74"/>
<polygon fill="#75bc2f" stroke="black" points="284.57,-201.3 288.62,-191.51 279.35,-196.64 284.57,-201.3"/>
</g>
<!-- plugginger_testing_collectors&#45;&gt;plugginger_testing_helpers -->
<g id="edge162" class="edge">
<title>plugginger_testing_collectors&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M259.59,-312.73C267.05,-303.09 275.43,-292.27 283.06,-282.42"/>
<polygon fill="#75bc2f" stroke="black" points="285.81,-284.59 289.16,-274.54 280.27,-280.3 285.81,-284.59"/>
</g>
<!-- plugginger_testing_helpers&#45;&gt;plugginger_testing -->
<g id="edge163" class="edge">
<title>plugginger_testing_helpers&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M307.44,-226.64C307.44,-218.9 307.44,-210.12 307.44,-201.94"/>
<polygon fill="blue" stroke="black" points="310.94,-202.1 307.44,-192.1 303.94,-202.1 310.94,-202.1"/>
</g>
<!-- plugginger_testing_mock_app&#45;&gt;plugginger_testing -->
<g id="edge164" class="edge">
<title>plugginger_testing_mock_app&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M906.45,-319.33C899.34,-315.66 891.8,-312.21 884.44,-309.63 797.56,-279.14 768.81,-299.45 680.44,-273.63 627.77,-258.24 618.89,-242.35 566.44,-226.25 493.62,-203.9 406.38,-188.25 354,-180.01"/>
<polygon fill="#75bc2f" stroke="black" points="354.66,-176.57 344.24,-178.5 353.59,-183.49 354.66,-176.57"/>
</g>
<!-- plugginger_testing_mock_app&#45;&gt;plugginger_testing_helpers -->
<g id="edge165" class="edge">
<title>plugginger_testing_mock_app&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M907.26,-319.1C899.95,-315.35 892.13,-311.93 884.44,-309.63 699.05,-254 642.6,-296.81 450.44,-273.63 418.08,-269.72 381.83,-263.96 353.92,-259.21"/>
<polygon fill="#75bc2f" stroke="black" points="354.83,-255.82 344.38,-257.57 353.64,-262.72 354.83,-255.82"/>
</g>
<!-- pydantic -->
<g id="node50" class="node">
<title>pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1210.44,-1797.85 1152.44,-1797.85 1152.44,-1761.85 1210.44,-1761.85 1210.44,-1797.85"/>
<text text-anchor="middle" x="1181.44" y="-1776.73" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic</text>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge166" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1178.11,-1761.61C1167.31,-1700.94 1138.13,-1493.33 1213.44,-1350.54 1225.38,-1327.88 1247.34,-1337.62 1258.44,-1314.54 1271.03,-1288.34 1267,-1276.91 1258.44,-1249.13 1217.74,-1117.13 1003.17,-1104.7 1083.44,-992.28"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge167" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2289.44,-1594.07C2187.69,-1464.98 2129.94,-1040.1 2063.44,-889.78"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_app_plugin -->
<g id="edge168" class="edge">
<title>pydantic&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2366.44,-1686.46C2475.25,-1623.6 2541.72,-1643.7 2605.44,-1535.38 2711.13,-1355.7 2671.44,-1279.47 2671.44,-1071 2671.44,-1071 2671.44,-1071 2671.44,-954.28 2671.44,-886.23 2686.05,-867.63 2671.44,-801.16"/>
<path fill="none" stroke="black" d="M2671.44,-799.16C2620.21,-589.02 2528.98,-554.26 2348.44,-435.16"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_builder -->
<g id="edge169" class="edge">
<title>pydantic&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1210.74,-1778.13C1383.34,-1773.65 2263.37,-1748 2366.44,-1688.46"/>
<path fill="none" stroke="black" d="M2366.44,-1686.46C2412.13,-1660.06 2328.42,-1631.65 2289.44,-1596.07"/>
<path fill="none" stroke="black" d="M2289.44,-1594.07C2187.24,-1500.79 2141.09,-1505.16 2022.44,-1433.98 1960.65,-1396.91 1946.89,-1384.67 1883.44,-1350.54 1758.89,-1283.54 1719.83,-1282.22 1596.44,-1213.13 1594.55,-1212.07 1592.63,-1210.97 1590.71,-1209.84"/>
<polygon fill="blue" stroke="black" points="1592.75,-1206.99 1582.39,-1204.79 1589.12,-1212.97 1592.75,-1206.99"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_plugin -->
<g id="edge170" class="edge">
<title>pydantic&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M124.44,-1280.83C103.23,-1052.51 314.44,-1030.46 314.44,-801.16 314.44,-801.16 314.44,-801.16 314.44,-597.66 314.44,-531.98 520.91,-470.93 615.32,-446.46"/>
<polygon fill="blue" stroke="black" points="615.97,-449.9 624.79,-444.03 614.23,-443.12 615.97,-449.9"/>
</g>
<!-- pydantic&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge171" class="edge">
<title>pydantic&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2671.44,-799.16C2662.55,-710.94 2681.48,-687.75 2671.44,-599.66"/>
<path fill="none" stroke="black" d="M2671.44,-597.66C2661.11,-523.36 2561.24,-345.63 2495.44,-309.63 2414.93,-265.58 971.66,-290.4 800.51,-273"/>
<polygon fill="blue" stroke="black" points="801.18,-269.56 790.8,-271.67 800.23,-276.49 801.18,-269.56"/>
</g>
<!-- pydantic&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge172" class="edge">
<title>pydantic&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2671.44,-597.66C2667.4,-562.22 2666.19,-552.69 2671.44,-517.41"/>
</g>
<!-- pydantic&#45;&gt;plugginger_config_models -->
<g id="edge173" class="edge">
<title>pydantic&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2366.44,-1686.46C2401.22,-1666.37 2341.76,-1627.78 2366.44,-1596.07"/>
</g>
<!-- pydantic&#45;&gt;plugginger_testing_helpers -->
<g id="edge174" class="edge">
<title>pydantic&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1152.14,-1778.23C987.35,-1774.59 181.8,-1755.03 143.44,-1720.17 102.15,-1682.64 124.44,-1651.87 124.44,-1596.07 124.44,-1596.07 124.44,-1596.07 124.44,-1501.68 124.44,-1404.41 139.79,-1378.88 124.44,-1282.83"/>
<path fill="none" stroke="black" d="M124.44,-1280.83C106.7,-1183.25 48.44,-1170.19 48.44,-1071 48.44,-1071 48.44,-1071 48.44,-433.16 48.44,-330.4 186.5,-280.56 261.02,-261.14"/>
<polygon fill="blue" stroke="black" points="261.65,-264.59 270.5,-258.75 259.95,-257.8 261.65,-264.59"/>
</g>
<!-- pydantic__migration -->
<g id="node51" class="node">
<title>pydantic__migration</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1780.69,-2752.7 1712.19,-2752.7 1712.19,-2716.7 1780.69,-2716.7 1780.69,-2752.7"/>
<text text-anchor="middle" x="1746.44" y="-2737.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1746.44" y="-2725.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_migration</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic -->
<g id="edge175" class="edge">
<title>pydantic__migration&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1810.44,-2656.02C1824.66,-2618.29 1845.31,-2617.7 1860.44,-2580.33"/>
<path fill="none" stroke="black" d="M1860.44,-2578.33C1886.6,-2513.68 1898.44,-2495.82 1898.44,-2426.08 1898.44,-2426.08 1898.44,-2426.08 1898.44,-2337.37 1898.44,-2323.87 1869.3,-2107.48 1860.44,-2097.29"/>
</g>
<!-- pydantic_config -->
<g id="node54" class="node">
<title>pydantic_config</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1448.94,-2597.33 1387.94,-2597.33 1387.94,-2561.33 1448.94,-2561.33 1448.94,-2597.33"/>
<text text-anchor="middle" x="1418.44" y="-2582.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1418.44" y="-2569.83" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">config</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic_config -->
<g id="edge176" class="edge">
<title>pydantic__migration&#45;&gt;pydantic_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1711.79,-2732.07C1664.19,-2728.49 1577.22,-2717.08 1513.44,-2680.7 1481.34,-2662.4 1453.48,-2629.79 1436.38,-2606.72"/>
<polygon fill="blue" stroke="black" points="1439.29,-2604.78 1430.61,-2598.71 1433.61,-2608.87 1439.29,-2604.78"/>
</g>
<!-- pydantic_dataclasses -->
<g id="node55" class="node">
<title>pydantic_dataclasses</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1538.31,-2443.08 1464.56,-2443.08 1464.56,-2407.08 1538.31,-2407.08 1538.31,-2443.08"/>
<text text-anchor="middle" x="1501.44" y="-2428.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1501.44" y="-2415.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dataclasses</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic_dataclasses -->
<g id="edge177" class="edge">
<title>pydantic__migration&#45;&gt;pydantic_dataclasses</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1767.45,-2716.23C1782.54,-2702.25 1801.69,-2681.2 1810.44,-2658.02"/>
<path fill="none" stroke="black" d="M1810.44,-2656.02C1824.25,-2619.4 1780.07,-2616.62 1765.44,-2580.33"/>
<path fill="none" stroke="black" d="M1765.44,-2578.33C1730.13,-2490.77 1615.79,-2451.31 1549.74,-2435.43"/>
<polygon fill="blue" stroke="black" points="1550.79,-2432.08 1540.26,-2433.24 1549.22,-2438.9 1550.79,-2432.08"/>
</g>
<!-- pydantic_errors -->
<g id="node60" class="node">
<title>pydantic_errors</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1582.94,-2675.02 1521.94,-2675.02 1521.94,-2639.02 1582.94,-2639.02 1582.94,-2675.02"/>
<text text-anchor="middle" x="1552.44" y="-2660.27" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1552.44" y="-2647.52" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">errors</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic_errors -->
<g id="edge178" class="edge">
<title>pydantic__migration&#45;&gt;pydantic_errors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1701.45,-2716.15C1669.01,-2703.5 1625.56,-2686.55 1593.95,-2674.21"/>
<polygon fill="blue" stroke="black" points="1699.76,-2719.25 1710.34,-2719.62 1702.3,-2712.73 1699.76,-2719.25"/>
<polygon fill="blue" stroke="black" points="1595.3,-2670.98 1584.71,-2670.61 1592.76,-2677.5 1595.3,-2670.98"/>
</g>
<!-- pydantic_main -->
<g id="node65" class="node">
<title>pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1410.31,-2186.29 1324.56,-2186.29 1324.56,-2150.29 1410.31,-2150.29 1410.31,-2186.29"/>
<text text-anchor="middle" x="1367.44" y="-2165.17" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.main</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic_main -->
<g id="edge179" class="edge">
<title>pydantic__migration&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1765.44,-2578.33C1742.36,-2509.86 1848.1,-2493.62 1822.44,-2426.08"/>
<path fill="none" stroke="black" d="M1822.44,-2424.08C1816.33,-2391.48 1579.08,-2318.36 1548.44,-2305.67 1483.06,-2278.59 1446.51,-2300.69 1400.44,-2246.98"/>
</g>
<!-- pydantic_networks -->
<g id="node66" class="node">
<title>pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1217.94,-2042.29 1156.94,-2042.29 1156.94,-2006.29 1217.94,-2006.29 1217.94,-2042.29"/>
<text text-anchor="middle" x="1187.44" y="-2027.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1187.44" y="-2014.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">networks</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic_networks -->
<g id="edge180" class="edge">
<title>pydantic__migration&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1822.44,-2424.08C1804.33,-2347.48 1822.44,-2325.69 1822.44,-2246.98 1822.44,-2246.98 1822.44,-2246.98 1822.44,-2167.29 1822.44,-2045.33 1370.67,-2028.09 1229.24,-2025.68"/>
<polygon fill="blue" stroke="black" points="1229.63,-2022.18 1219.58,-2025.53 1229.52,-2029.18 1229.63,-2022.18"/>
</g>
<!-- pydantic_types -->
<g id="node69" class="node">
<title>pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="992.44,-2186.29 904.44,-2186.29 904.44,-2150.29 992.44,-2150.29 992.44,-2186.29"/>
<text text-anchor="middle" x="948.44" y="-2165.17" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.types</text>
</g>
<!-- pydantic__migration&#45;&gt;pydantic_types -->
<g id="edge181" class="edge">
<title>pydantic__migration&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1765.44,-2578.33C1747.39,-2533.77 1755.39,-2514.08 1722.44,-2479.08 1647.43,-2399.4 1427.5,-2346.72 1093.44,-2222.29 1063.72,-2211.23 1030.51,-2199.07 1003.37,-2189.2"/>
<polygon fill="blue" stroke="black" points="1004.93,-2186.04 994.33,-2185.91 1002.54,-2192.62 1004.93,-2186.04"/>
</g>
<!-- pydantic_aliases -->
<g id="node52" class="node">
<title>pydantic_aliases</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#26d9d9" stroke="black" cx="1192.44" cy="-2657.02" rx="43.13" ry="23.69"/>
<text text-anchor="middle" x="1192.44" y="-2660.27" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">pydantic.</text>
<text text-anchor="middle" x="1192.44" y="-2647.52" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">aliases</text>
</g>
<!-- pydantic_aliases&#45;&gt;pydantic -->
<g id="edge182" class="edge">
<title>pydantic_aliases&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1163.86,-2638.95C1123.51,-2614.62 1047.78,-2568.18 985.44,-2525.33 924.65,-2483.55 916.72,-2462.24 852.44,-2426.08"/>
<path fill="none" stroke="black" d="M852.44,-2424.08C835.06,-2415.55 699.3,-2385.55 686.44,-2371.08 649.13,-2329.08 667.18,-2302.96 662.44,-2246.98"/>
</g>
<!-- pydantic_aliases&#45;&gt;pydantic_config -->
<g id="edge183" class="edge">
<title>pydantic_aliases&#45;&gt;pydantic_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1228.75,-2643.86C1269.18,-2630.31 1334.42,-2608.47 1376.86,-2594.25"/>
<polygon fill="#26d9d9" stroke="black" points="1377.86,-2597.61 1386.23,-2591.12 1375.63,-2590.97 1377.86,-2597.61"/>
</g>
<!-- pydantic_fields -->
<g id="node61" class="node">
<title>pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1243.94,-2520.2 1182.94,-2520.2 1182.94,-2484.2 1243.94,-2484.2 1243.94,-2520.2"/>
<text text-anchor="middle" x="1213.44" y="-2505.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1213.44" y="-2492.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">fields</text>
</g>
<!-- pydantic_aliases&#45;&gt;pydantic_fields -->
<g id="edge184" class="edge">
<title>pydantic_aliases&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1188.47,-2633.13C1186.74,-2617.74 1186.31,-2597.26 1192.44,-2580.33"/>
</g>
<!-- pydantic_aliases&#45;&gt;pydantic_main -->
<g id="edge185" class="edge">
<title>pydantic_aliases&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1185.22,-2633.44C1172.13,-2588.58 1148.5,-2485.77 1178.44,-2407.08 1212.73,-2316.93 1294.5,-2234.22 1338.4,-2194.34"/>
<polygon fill="#26d9d9" stroke="black" points="1340.66,-2197 1345.77,-2187.72 1335.99,-2191.79 1340.66,-2197"/>
</g>
<!-- pydantic_annotated_handlers -->
<g id="node53" class="node">
<title>pydantic_annotated_handlers</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="878.81,-2263.98 766.06,-2263.98 766.06,-2227.98 878.81,-2227.98 878.81,-2263.98"/>
<text text-anchor="middle" x="822.44" y="-2249.23" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="822.44" y="-2236.48" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">annotated_handlers</text>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic -->
<g id="edge186" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M796.05,-2227.51C758.86,-2200.58 698.3,-2146.82 724.44,-2097.29"/>
</g>
<!-- pydantic_functional_serializers -->
<g id="node62" class="node">
<title>pydantic_functional_serializers</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="372.56,-2186.29 252.31,-2186.29 252.31,-2150.29 372.56,-2150.29 372.56,-2186.29"/>
<text text-anchor="middle" x="312.44" y="-2171.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="312.44" y="-2158.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">functional_serializers</text>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic_functional_serializers -->
<g id="edge187" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic_functional_serializers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M765.91,-2236.59C672.54,-2222.74 486.28,-2195.09 383.92,-2179.9"/>
<polygon fill="blue" stroke="black" points="384.67,-2176.48 374.27,-2178.47 383.64,-2183.4 384.67,-2176.48"/>
</g>
<!-- pydantic_functional_validators -->
<g id="node63" class="node">
<title>pydantic_functional_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2006.81,-2186.29 1888.06,-2186.29 1888.06,-2150.29 2006.81,-2150.29 2006.81,-2186.29"/>
<text text-anchor="middle" x="1947.44" y="-2171.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1947.44" y="-2158.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">functional_validators</text>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic_functional_validators -->
<g id="edge188" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic_functional_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M879.1,-2236.92C915.43,-2232.02 963.63,-2226.01 1006.44,-2222.29 1323.87,-2194.78 1404.44,-2206.3 1722.44,-2186.29 1774.05,-2183.05 1832.26,-2178.63 1876.54,-2175.11"/>
<polygon fill="blue" stroke="black" points="1876.58,-2178.62 1886.27,-2174.33 1876.02,-2171.64 1876.58,-2178.62"/>
</g>
<!-- pydantic_json_schema -->
<g id="node64" class="node">
<title>pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1184.31,-2356.37 1104.56,-2356.37 1104.56,-2320.37 1184.31,-2320.37 1184.31,-2356.37"/>
<text text-anchor="middle" x="1144.44" y="-2341.62" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1144.44" y="-2328.87" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">json_schema</text>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic_main -->
<g id="edge190" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M879.22,-2237.1C984.73,-2222.44 1209.66,-2191.21 1313.21,-2176.83"/>
<polygon fill="blue" stroke="black" points="1313.4,-2180.33 1322.83,-2175.49 1312.44,-2173.4 1313.4,-2180.33"/>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic_networks -->
<g id="edge191" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M817.06,-2227.53C807.83,-2193.7 793.96,-2120.5 838.44,-2097.29"/>
</g>
<!-- pydantic_annotated_handlers&#45;&gt;pydantic_types -->
<g id="edge192" class="edge">
<title>pydantic_annotated_handlers&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M851.34,-2227.62C868.71,-2217.19 891.03,-2203.78 909.88,-2192.45"/>
<polygon fill="blue" stroke="black" points="911.6,-2195.51 918.37,-2187.36 907.99,-2189.51 911.6,-2195.51"/>
</g>
<!-- pydantic_config&#45;&gt;pydantic -->
<g id="edge193" class="edge">
<title>pydantic_config&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1477.44,-2337.37C1514.73,-2281.09 1562.33,-2313.79 1613.44,-2269.67 1632.86,-2252.9 1629.21,-2241.31 1646.44,-2222.29 1670.77,-2195.43 1679.99,-2191.76 1708.44,-2169.29"/>
<path fill="none" stroke="black" d="M1708.44,-2167.29C1767.03,-2121.48 1909.26,-2153.41 1860.44,-2097.29"/>
<path fill="none" stroke="black" d="M1860.44,-2095.29C1800.82,-2026.77 1726.56,-2095.09 1668.44,-2025.29"/>
</g>
<!-- pydantic_config&#45;&gt;pydantic_dataclasses -->
<g id="edge194" class="edge">
<title>pydantic_config&#45;&gt;pydantic_dataclasses</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1427.21,-2560.99C1437.36,-2541.07 1454.69,-2507.49 1470.44,-2479.08 1475.21,-2470.46 1480.58,-2461.16 1485.49,-2452.79"/>
<polygon fill="blue" stroke="black" points="1488.34,-2454.84 1490.41,-2444.45 1482.31,-2451.28 1488.34,-2454.84"/>
</g>
<!-- pydantic_config&#45;&gt;pydantic_fields -->
<g id="edge195" class="edge">
<title>pydantic_config&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1376.96,-2563.13C1341.39,-2550.09 1290.39,-2531.4 1254.84,-2518.38"/>
<polygon fill="blue" stroke="black" points="1375.73,-2566.41 1386.33,-2566.56 1378.14,-2559.83 1375.73,-2566.41"/>
<polygon fill="blue" stroke="black" points="1256.35,-2515.2 1245.76,-2515.05 1253.94,-2521.78 1256.35,-2515.2"/>
</g>
<!-- pydantic_config&#45;&gt;pydantic_json_schema -->
<g id="edge196" class="edge">
<title>pydantic_config&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1400.77,-2561.09C1388.07,-2546.95 1372.79,-2525.68 1370.44,-2503.2"/>
<path fill="none" stroke="black" d="M1370.44,-2501.2C1366.93,-2467.7 1377.65,-2455.03 1360.44,-2426.08"/>
</g>
<!-- pydantic_config&#45;&gt;pydantic_main -->
<g id="edge197" class="edge">
<title>pydantic_config&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1370.44,-2501.2C1365.81,-2457 1420.43,-2467.54 1436.44,-2426.08"/>
<path fill="none" stroke="black" d="M1436.44,-2424.08C1465.36,-2349.15 1453.1,-2307.63 1400.44,-2246.98"/>
<path fill="none" stroke="black" d="M1400.44,-2244.98C1388.2,-2231.47 1379.98,-2212.71 1374.81,-2197.25"/>
<polygon fill="blue" stroke="black" points="1378.29,-2196.67 1372,-2188.14 1371.6,-2198.73 1378.29,-2196.67"/>
</g>
<!-- pydantic_type_adapter -->
<g id="node68" class="node">
<title>pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1286.69,-2114.29 1206.19,-2114.29 1206.19,-2078.29 1286.69,-2078.29 1286.69,-2114.29"/>
<text text-anchor="middle" x="1246.44" y="-2099.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1246.44" y="-2086.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">type_adapter</text>
</g>
<!-- pydantic_config&#45;&gt;pydantic_type_adapter -->
<g id="edge198" class="edge">
<title>pydantic_config&#45;&gt;pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1436.44,-2424.08C1456.39,-2387.32 1457.34,-2376.06 1477.44,-2339.37"/>
<path fill="none" stroke="black" d="M1477.44,-2337.37C1519.26,-2261.03 1481.09,-2211.75 1419.44,-2150.29 1387.24,-2118.2 1336.16,-2105.46 1297.84,-2100.44"/>
<polygon fill="blue" stroke="black" points="1298.73,-2097.02 1288.39,-2099.34 1297.92,-2103.98 1298.73,-2097.02"/>
</g>
<!-- pydantic_validate_call_decorator -->
<g id="node70" class="node">
<title>pydantic_validate_call_decorator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1642.44,-2520.2 1512.44,-2520.2 1512.44,-2484.2 1642.44,-2484.2 1642.44,-2520.2"/>
<text text-anchor="middle" x="1577.44" y="-2505.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1577.44" y="-2492.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">validate_call_decorator</text>
</g>
<!-- pydantic_config&#45;&gt;pydantic_validate_call_decorator -->
<g id="edge199" class="edge">
<title>pydantic_config&#45;&gt;pydantic_validate_call_decorator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1449.09,-2563.84C1472.12,-2552.97 1504.06,-2537.87 1530.32,-2525.46"/>
<polygon fill="blue" stroke="black" points="1531.53,-2528.76 1539.08,-2521.33 1528.54,-2522.43 1531.53,-2528.76"/>
</g>
<!-- pydantic_dataclasses&#45;&gt;pydantic -->
<g id="edge200" class="edge">
<title>pydantic_dataclasses&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1518.5,-2406.8C1551.92,-2372.83 1624.63,-2298.13 1646.44,-2269.67 1678.32,-2228.04 1671.13,-2206.14 1708.44,-2169.29"/>
</g>
<!-- pydantic_deprecated -->
<g id="node56" class="node">
<title>pydantic_deprecated</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="1554.44" cy="-2245.98" rx="50.03" ry="23.69"/>
<text text-anchor="middle" x="1554.44" y="-2249.23" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">pydantic.</text>
<text text-anchor="middle" x="1554.44" y="-2236.48" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">deprecated</text>
</g>
<!-- pydantic_deprecated&#45;&gt;pydantic -->
<g id="edge201" class="edge">
<title>pydantic_deprecated&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1530.73,-2224.79C1493.09,-2190.84 1428.6,-2123.44 1476.44,-2097.29"/>
</g>
<!-- pydantic_deprecated&#45;&gt;pydantic_main -->
<g id="edge202" class="edge">
<title>pydantic_deprecated&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1517.04,-2229.85C1489.59,-2218.73 1451.88,-2203.47 1421.19,-2191.05"/>
<polygon fill="#47c2c2" stroke="black" points="1422.65,-2187.87 1412.06,-2187.36 1420.02,-2194.35 1422.65,-2187.87"/>
</g>
<!-- pydantic_deprecated_class_validators -->
<g id="node57" class="node">
<title>pydantic_deprecated_class_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1088.81,-2525.33 994.06,-2525.33 994.06,-2479.08 1088.81,-2479.08 1088.81,-2525.33"/>
<text text-anchor="middle" x="1041.44" y="-2511.83" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1041.44" y="-2499.08" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">deprecated.</text>
<text text-anchor="middle" x="1041.44" y="-2486.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">class_validators</text>
</g>
<!-- pydantic_deprecated_class_validators&#45;&gt;pydantic -->
<g id="edge203" class="edge">
<title>pydantic_deprecated_class_validators&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M993.62,-2486.19C954.41,-2472.95 898.14,-2451.79 852.44,-2426.08"/>
</g>
<!-- pydantic_deprecated_config -->
<g id="node58" class="node">
<title>pydantic_deprecated_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#4cb3b3" stroke="black" cx="468.44" cy="-2338.37" rx="52.15" ry="32.7"/>
<text text-anchor="middle" x="468.44" y="-2348" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">pydantic.</text>
<text text-anchor="middle" x="468.44" y="-2335.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">deprecated.</text>
<text text-anchor="middle" x="468.44" y="-2322.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- pydantic_deprecated_config&#45;&gt;pydantic -->
<g id="edge204" class="edge">
<title>pydantic_deprecated_config&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M455.04,-2306.52C433,-2250.67 398.12,-2135.06 468.44,-2097.29"/>
</g>
<!-- pydantic_deprecated_tools -->
<g id="node59" class="node">
<title>pydantic_deprecated_tools</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1218.31,-1969.73 1144.56,-1969.73 1144.56,-1923.48 1218.31,-1923.48 1218.31,-1969.73"/>
<text text-anchor="middle" x="1181.44" y="-1956.23" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1181.44" y="-1943.48" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">deprecated.</text>
<text text-anchor="middle" x="1181.44" y="-1930.73" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">tools</text>
</g>
<!-- pydantic_deprecated_tools&#45;&gt;pydantic -->
<g id="edge205" class="edge">
<title>pydantic_deprecated_tools&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1178.17,-1923.05C1176.54,-1906.45 1175.94,-1883.53 1181.44,-1864.23"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic -->
<g id="edge206" class="edge">
<title>pydantic_errors&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1708.44,-2501.2C1731.1,-2443.71 1739.93,-2430.35 1757.44,-2371.08 1773.42,-2316.95 1763.32,-2299.33 1784.44,-2246.98"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_config -->
<g id="edge208" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1521.69,-2638.65C1503.05,-2628.12 1479.06,-2614.57 1458.89,-2603.18"/>
<polygon fill="blue" stroke="black" points="1460.78,-2600.23 1450.35,-2598.36 1457.34,-2606.32 1460.78,-2600.23"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_dataclasses -->
<g id="edge209" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_dataclasses</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1577.44,-2578.33C1590.73,-2526.88 1492.58,-2555.71 1484.44,-2503.2"/>
<path fill="none" stroke="black" d="M1484.44,-2501.2C1481.99,-2485.46 1485.51,-2468.1 1489.96,-2454.03"/>
<polygon fill="blue" stroke="black" points="1493.13,-2455.58 1493.14,-2444.98 1486.52,-2453.26 1493.13,-2455.58"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_deprecated_class_validators -->
<g id="edge210" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_deprecated_class_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1521.67,-2655.74C1456.16,-2654.2 1299.04,-2645.33 1178.44,-2597.33 1140.06,-2582.05 1101.77,-2554.44 1075.47,-2533.05"/>
<polygon fill="blue" stroke="black" points="1077.79,-2530.43 1067.85,-2526.75 1073.32,-2535.82 1077.79,-2530.43"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_fields -->
<g id="edge211" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1521.73,-2651.3C1476.64,-2643.17 1391.49,-2623.08 1332.44,-2580.33"/>
<path fill="none" stroke="black" d="M1332.44,-2578.33C1306.63,-2559.65 1276.25,-2540.51 1252.64,-2526.23"/>
<polygon fill="blue" stroke="black" points="1254.64,-2523.35 1244.27,-2521.2 1251.04,-2529.35 1254.64,-2523.35"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_functional_validators -->
<g id="edge212" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_functional_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1577.44,-2578.33C1594.61,-2511.9 1669.03,-2568.41 1722.44,-2525.33 1752.46,-2501.11 1885.87,-2274.89 1931.82,-2196.16"/>
<polygon fill="blue" stroke="black" points="1934.69,-2198.19 1936.7,-2187.78 1928.64,-2194.66 1934.69,-2198.19"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_json_schema -->
<g id="edge213" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1332.44,-2578.33C1304.57,-2558.16 1378.02,-2455.65 1360.44,-2426.08"/>
<path fill="none" stroke="black" d="M1360.44,-2424.08C1359.24,-2422.23 1258.88,-2383.38 1195.39,-2358.94"/>
<polygon fill="blue" stroke="black" points="1196.78,-2355.72 1186.19,-2355.4 1194.26,-2362.26 1196.78,-2355.72"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_main -->
<g id="edge214" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1577.44,-2578.33C1594.23,-2513.35 1685.72,-2566.36 1708.44,-2503.2"/>
<path fill="none" stroke="black" d="M1708.44,-2501.2C1717.75,-2475.31 1865.28,-2583.94 1491.44,-2305.67 1452.83,-2276.94 1432.74,-2282.66 1400.44,-2246.98"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_networks -->
<g id="edge215" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1566.44,-2424.08C1586.53,-2398.69 1602.31,-2400.22 1616.44,-2371.08 1645.44,-2311.28 1661.31,-2281.67 1631.44,-2222.29 1592.67,-2145.24 1332.31,-2065.53 1229.29,-2036.62"/>
<polygon fill="blue" stroke="black" points="1230.41,-2033.3 1219.84,-2033.99 1228.54,-2040.04 1230.41,-2033.3"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_type_adapter -->
<g id="edge216" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1484.44,-2501.2C1476.86,-2452.36 1540.45,-2468.12 1566.44,-2426.08"/>
<path fill="none" stroke="black" d="M1566.44,-2424.08C1590.65,-2384.92 1638.57,-2260.87 1613.44,-2222.29 1578.3,-2168.36 1390.79,-2125.24 1298.22,-2106.88"/>
<polygon fill="blue" stroke="black" points="1299.12,-2103.49 1288.63,-2105.01 1297.78,-2110.36 1299.12,-2103.49"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_types -->
<g id="edge217" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1559.54,-2638.61C1565.39,-2623.47 1573.36,-2600.82 1577.44,-2580.33"/>
<path fill="none" stroke="black" d="M1577.44,-2578.33C1587.8,-2526.28 1511.89,-2558.47 1470.44,-2525.33 1417.56,-2483.06 1426.22,-2450.68 1374.44,-2407.08 1276.25,-2324.42 1233.49,-2330.44 1120.44,-2269.67 1073.73,-2244.57 1021.26,-2213.52 986.69,-2192.66"/>
<polygon fill="blue" stroke="black" points="988.66,-2189.76 978.29,-2187.57 985.04,-2195.75 988.66,-2189.76"/>
</g>
<!-- pydantic_errors&#45;&gt;pydantic_validate_call_decorator -->
<g id="edge218" class="edge">
<title>pydantic_errors&#45;&gt;pydantic_validate_call_decorator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1577.44,-2578.33C1581.34,-2563.23 1581.6,-2545.86 1580.76,-2531.62"/>
<polygon fill="blue" stroke="black" points="1584.28,-2531.71 1579.98,-2522.03 1577.31,-2532.28 1584.28,-2531.71"/>
</g>
<!-- pydantic_fields&#45;&gt;pydantic -->
<g id="edge219" class="edge">
<title>pydantic_fields&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1235.12,-2483.88C1280.83,-2445.14 1381.37,-2347.01 1362.44,-2246.98"/>
<path fill="none" stroke="black" d="M1362.44,-2244.98C1358.76,-2211.77 1326.51,-2217.83 1315.44,-2186.29 1310.14,-2171.2 1306.06,-2163.26 1315.44,-2150.29 1359.6,-2089.26 1410.44,-2133.61 1476.44,-2097.29"/>
<path fill="none" stroke="black" d="M1476.44,-2095.29C1555.39,-2050.39 1726.56,-2095.09 1668.44,-2025.29"/>
<path fill="none" stroke="black" d="M1668.44,-2023.29C1546.5,-1876.87 1365.59,-2096.31 1246.44,-1947.61"/>
</g>
<!-- pydantic_fields&#45;&gt;pydantic_dataclasses -->
<g id="edge221" class="edge">
<title>pydantic_fields&#45;&gt;pydantic_dataclasses</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1244.26,-2493.16C1294.42,-2480.08 1394.1,-2454.08 1453.55,-2438.57"/>
<polygon fill="blue" stroke="black" points="1454.26,-2442 1463.06,-2436.09 1452.5,-2435.23 1454.26,-2442"/>
</g>
<!-- pydantic_fields&#45;&gt;pydantic_main -->
<g id="edge222" class="edge">
<title>pydantic_fields&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1362.44,-2244.98C1359.51,-2229.52 1360.41,-2211.99 1362.19,-2197.7"/>
<polygon fill="blue" stroke="black" points="1365.62,-2198.48 1363.62,-2188.08 1358.69,-2197.46 1365.62,-2198.48"/>
</g>
<!-- pydantic_root_model -->
<g id="node67" class="node">
<title>pydantic_root_model</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1066.19,-2443.08 994.69,-2443.08 994.69,-2407.08 1066.19,-2407.08 1066.19,-2443.08"/>
<text text-anchor="middle" x="1030.44" y="-2428.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic.</text>
<text text-anchor="middle" x="1030.44" y="-2415.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">root_model</text>
</g>
<!-- pydantic_fields&#45;&gt;pydantic_root_model -->
<g id="edge223" class="edge">
<title>pydantic_fields&#45;&gt;pydantic_root_model</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1182.81,-2488.63C1153.89,-2476.76 1110.12,-2458.79 1076.9,-2445.15"/>
<polygon fill="blue" stroke="black" points="1078.5,-2442.02 1067.92,-2441.46 1075.84,-2448.5 1078.5,-2442.02"/>
</g>
<!-- pydantic_functional_serializers&#45;&gt;pydantic -->
<g id="edge224" class="edge">
<title>pydantic_functional_serializers&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M355.6,-2149.88C387.36,-2136.59 431.3,-2117.24 468.44,-2097.29"/>
<path fill="none" stroke="black" d="M468.44,-2095.29C479.95,-2089.11 480.42,-2083.45 492.44,-2078.29 604.83,-2030.1 654.83,-2083.4 762.44,-2025.29"/>
<path fill="none" stroke="black" d="M762.44,-2023.29C850.07,-1975.97 1154.16,-1960.02 1181.44,-1864.23"/>
<path fill="none" stroke="black" d="M1181.44,-1862.23C1184.14,-1844.76 1184.2,-1824.93 1183.55,-1809.22"/>
<polygon fill="blue" stroke="black" points="1187.07,-1809.43 1183.04,-1799.63 1180.08,-1809.8 1187.07,-1809.43"/>
</g>
<!-- pydantic_functional_validators&#45;&gt;pydantic -->
<g id="edge225" class="edge">
<title>pydantic_functional_validators&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1919.37,-2149.95C1900.68,-2137.24 1876.66,-2118.56 1860.44,-2097.29"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic -->
<g id="edge226" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1138.74,-2320.07C1122.63,-2267.69 1082.05,-2108.15 1148.44,-2006.29 1176.16,-1963.76 1277.11,-1988.06 1246.44,-1947.61"/>
<path fill="none" stroke="black" d="M1246.44,-1945.61C1244.63,-1943.35 1181,-1867.09 1181.44,-1864.23"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_annotated_handlers -->
<g id="edge227" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_annotated_handlers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1093.5,-2323.08C1038.56,-2307.65 950.8,-2283.02 890.1,-2265.98"/>
<polygon fill="blue" stroke="black" points="1092.28,-2326.37 1102.85,-2325.7 1094.17,-2319.63 1092.28,-2326.37"/>
<polygon fill="blue" stroke="black" points="891.12,-2262.63 880.55,-2263.3 889.23,-2269.37 891.12,-2262.63"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_deprecated_tools -->
<g id="edge228" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_deprecated_tools</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1020.44,-2244.98C1005.05,-2226.04 998.78,-2036.55 1020.44,-2025.29"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_fields -->
<g id="edge229" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1153.28,-2356.7C1161.15,-2371.75 1173.4,-2394.28 1186.28,-2414.62"/>
<polygon fill="blue" stroke="black" points="1183.22,-2416.34 1191.61,-2422.81 1189.09,-2412.52 1183.22,-2416.34"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_main -->
<g id="edge230" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1174.4,-2312.95C1189.62,-2300.3 1208.27,-2284.45 1224.44,-2269.67 1246.56,-2249.45 1248.46,-2240.29 1272.44,-2222.29 1287.31,-2211.13 1304.79,-2200.72 1320.67,-2192.13"/>
<polygon fill="blue" stroke="black" points="1172.52,-2309.96 1167.04,-2319.02 1176.98,-2315.35 1172.52,-2309.96"/>
<polygon fill="blue" stroke="black" points="1322.29,-2195.23 1329.5,-2187.46 1319.02,-2189.04 1322.29,-2195.23"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_networks -->
<g id="edge231" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1146.81,-2320.15C1153.87,-2268.89 1174.81,-2116.96 1183.54,-2053.57"/>
<polygon fill="blue" stroke="black" points="1186.97,-2054.35 1184.86,-2043.96 1180.03,-2053.39 1186.97,-2054.35"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_type_adapter -->
<g id="edge232" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1163.08,-2310.39C1170.94,-2298.29 1179.78,-2283.62 1186.44,-2269.67 1209.85,-2220.59 1228.83,-2159.86 1238.81,-2125.15"/>
<polygon fill="blue" stroke="black" points="1160.23,-2308.35 1157.62,-2318.62 1166.07,-2312.22 1160.23,-2308.35"/>
<polygon fill="blue" stroke="black" points="1242.07,-2126.46 1241.42,-2115.88 1235.34,-2124.55 1242.07,-2126.46"/>
</g>
<!-- pydantic_json_schema&#45;&gt;pydantic_types -->
<g id="edge233" class="edge">
<title>pydantic_json_schema&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1111.77,-2319.99C1084.38,-2304.05 1045.8,-2278.21 1020.44,-2246.98"/>
<path fill="none" stroke="black" d="M1020.44,-2244.98C1006.1,-2227.33 988.53,-2208.81 974.34,-2194.51"/>
<polygon fill="blue" stroke="black" points="977.05,-2192.27 967.5,-2187.69 972.11,-2197.23 977.05,-2192.27"/>
</g>
<!-- pydantic_main&#45;&gt;pydantic -->
<g id="edge234" class="edge">
<title>pydantic_main&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1392.37,-2149.85C1414.07,-2135.01 1446.64,-2113.59 1476.44,-2097.29"/>
</g>
<!-- pydantic_main&#45;&gt;pydantic_root_model -->
<g id="edge236" class="edge">
<title>pydantic_main&#45;&gt;pydantic_root_model</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1357.16,-2186.68C1333.34,-2225.51 1270.48,-2319.85 1193.44,-2371.08 1158.26,-2394.47 1111.98,-2408.21 1077.72,-2415.82"/>
<polygon fill="blue" stroke="black" points="1077.14,-2412.37 1068.08,-2417.86 1078.59,-2419.21 1077.14,-2412.37"/>
</g>
<!-- pydantic_main&#45;&gt;pydantic_type_adapter -->
<g id="edge237" class="edge">
<title>pydantic_main&#45;&gt;pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1337.22,-2149.81C1321.65,-2140.81 1302.48,-2129.72 1285.78,-2120.05"/>
<polygon fill="blue" stroke="black" points="1287.99,-2117.29 1277.58,-2115.31 1284.49,-2123.35 1287.99,-2117.29"/>
</g>
<!-- pydantic_networks&#45;&gt;pydantic -->
<g id="edge238" class="edge">
<title>pydantic_networks&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1216.31,-2005.96C1238.07,-1990.69 1261.74,-1967.79 1246.44,-1947.61"/>
</g>
<!-- pydantic_root_model&#45;&gt;pydantic -->
<g id="edge239" class="edge">
<title>pydantic_root_model&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M994.21,-2422.16C909.53,-2415.4 702.7,-2384.8 662.44,-2246.98"/>
</g>
<!-- pydantic_root_model&#45;&gt;pydantic_json_schema -->
<g id="edge240" class="edge">
<title>pydantic_root_model&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1053.78,-2406.73C1070.61,-2394.23 1093.54,-2377.19 1112.12,-2363.38"/>
<polygon fill="blue" stroke="black" points="1113.97,-2366.37 1119.91,-2357.6 1109.79,-2360.75 1113.97,-2366.37"/>
</g>
<!-- pydantic_type_adapter&#45;&gt;pydantic -->
<g id="edge241" class="edge">
<title>pydantic_type_adapter&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1246.44,-2023.29C1239.82,-1990.31 1266.76,-1974.41 1246.44,-1947.61"/>
</g>
<!-- pydantic_type_adapter&#45;&gt;pydantic_deprecated_tools -->
<g id="edge242" class="edge">
<title>pydantic_type_adapter&#45;&gt;pydantic_deprecated_tools</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1248.33,-2077.85C1249.42,-2063.57 1249.98,-2042.93 1246.44,-2025.29"/>
<path fill="none" stroke="black" d="M1246.44,-2023.29C1242.94,-2005.88 1231.82,-1990.27 1219.74,-1977.82"/>
<polygon fill="blue" stroke="black" points="1222.45,-1975.57 1212.82,-1971.16 1217.59,-1980.62 1222.45,-1975.57"/>
</g>
<!-- pydantic_type_adapter&#45;&gt;pydantic_networks -->
<g id="edge244" class="edge">
<title>pydantic_type_adapter&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1231.85,-2077.99C1224.99,-2069.85 1216.67,-2059.98 1209.1,-2051"/>
<polygon fill="blue" stroke="black" points="1211.95,-2048.95 1202.83,-2043.56 1206.6,-2053.46 1211.95,-2048.95"/>
</g>
<!-- pydantic_types&#45;&gt;pydantic -->
<g id="edge245" class="edge">
<title>pydantic_types&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M903.94,-2151.15C829.66,-2122.54 694.89,-2062.45 762.44,-2025.29"/>
</g>
<!-- pydantic_types&#45;&gt;pydantic_fields -->
<g id="edge246" class="edge">
<title>pydantic_types&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M957.11,-2186.62C967.69,-2207.14 986.61,-2242.03 1006.44,-2269.67 1041.38,-2318.4 1048.63,-2333.59 1095.44,-2371.08 1130.64,-2399.27 1159.92,-2385.97 1185.63,-2415.17"/>
<polygon fill="blue" stroke="black" points="1182.66,-2417.06 1191.52,-2422.88 1188.23,-2412.81 1182.66,-2417.06"/>
<path fill="none" stroke="black" d="M1192.44,-2426.08C1204.19,-2443.63 1209.42,-2467.69 1211.71,-2483.81"/>
</g>
<!-- pydantic_validate_call_decorator&#45;&gt;pydantic -->
<g id="edge247" class="edge">
<title>pydantic_validate_call_decorator&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1602.57,-2483.87C1652.72,-2447.25 1762.31,-2356.32 1784.44,-2246.98"/>
<path fill="none" stroke="black" d="M1784.44,-2244.98C1788.45,-2208.29 1882.82,-2126.64 1860.44,-2097.29"/>
</g>
<!-- pydantic_version -->
<g id="node71" class="node">
<title>pydantic_version</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#26d9d9" stroke="black" cx="1787.44" cy="-2812.39" rx="43.13" ry="23.69"/>
<text text-anchor="middle" x="1787.44" y="-2815.64" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">pydantic.</text>
<text text-anchor="middle" x="1787.44" y="-2802.89" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">version</text>
</g>
<!-- pydantic_version&#45;&gt;pydantic -->
<g id="edge248" class="edge">
<title>pydantic_version&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1794.23,-2788.64C1806.72,-2747.08 1834.46,-2656.14 1860.44,-2580.33"/>
</g>
<!-- pydantic_version&#45;&gt;pydantic__migration -->
<g id="edge249" class="edge">
<title>pydantic_version&#45;&gt;pydantic__migration</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1775.58,-2789.5C1771.07,-2781.18 1765.88,-2771.6 1761.15,-2762.86"/>
<polygon fill="#26d9d9" stroke="black" points="1764.23,-2761.2 1756.39,-2754.07 1758.07,-2764.53 1764.23,-2761.2"/>
</g>
<!-- pydantic_version&#45;&gt;pydantic_errors -->
<g id="edge250" class="edge">
<title>pydantic_version&#45;&gt;pydantic_errors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1760.48,-2793.8C1718.16,-2766.18 1636.08,-2712.61 1588.65,-2681.65"/>
<polygon fill="#26d9d9" stroke="black" points="1590.63,-2678.77 1580.35,-2676.23 1586.81,-2684.63 1590.63,-2678.77"/>
</g>
<!-- pydantic_warnings -->
<g id="node72" class="node">
<title>pydantic_warnings</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#06f9f9" stroke="black" cx="1050.44" cy="-2657.02" rx="43.13" ry="23.69"/>
<text text-anchor="middle" x="1050.44" y="-2660.27" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">pydantic.</text>
<text text-anchor="middle" x="1050.44" y="-2647.52" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">warnings</text>
</g>
<!-- pydantic_version&#45;&gt;pydantic_warnings -->
<g id="edge251" class="edge">
<title>pydantic_version&#45;&gt;pydantic_warnings</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1746.79,-2803.88C1644.58,-2784.84 1368.69,-2732.5 1140.44,-2680.7 1127.18,-2677.7 1112.88,-2674.21 1099.67,-2670.88"/>
<polygon fill="#26d9d9" stroke="black" points="1100.9,-2667.58 1090.35,-2668.51 1099.18,-2674.36 1100.9,-2667.58"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic -->
<g id="edge252" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1009.51,-2649.33C966,-2640.35 897.38,-2620.62 852.44,-2580.33"/>
<path fill="none" stroke="black" d="M852.44,-2578.33C758.49,-2494.11 704.79,-2488.42 658.44,-2371.08 638.16,-2319.76 668.27,-2301.86 662.44,-2246.98"/>
<path fill="none" stroke="black" d="M662.44,-2244.98C646.49,-2175.6 693.21,-2161.27 724.44,-2097.29"/>
<path fill="none" stroke="black" d="M724.44,-2095.29C737.03,-2062.21 730.51,-2040.59 762.44,-2025.29"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_config -->
<g id="edge253" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1088.83,-2645.67C1104.74,-2641.56 1123.42,-2636.96 1140.44,-2633.33 1231.34,-2613.96 1255.39,-2616.02 1346.44,-2597.33 1356.2,-2595.32 1366.67,-2592.97 1376.51,-2590.67"/>
<polygon fill="#06f9f9" stroke="black" points="1377.3,-2594.08 1386.22,-2588.36 1375.68,-2587.27 1377.3,-2594.08"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_deprecated_class_validators -->
<g id="edge254" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_deprecated_class_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1057.44,-2578.33C1060.41,-2564.62 1058.23,-2549.49 1054.64,-2536.43"/>
<polygon fill="#06f9f9" stroke="black" points="1058.02,-2535.49 1051.67,-2527.01 1051.34,-2537.6 1058.02,-2535.49"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_deprecated_config -->
<g id="edge255" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_deprecated_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1011.07,-2646.81C958.66,-2634.26 869.02,-2611.71 838.44,-2597.33 706.79,-2535.44 571.38,-2427.55 506.98,-2373"/>
<polygon fill="#06f9f9" stroke="black" points="509.41,-2370.47 499.53,-2366.65 504.87,-2375.8 509.41,-2370.47"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_deprecated_tools -->
<g id="edge256" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_deprecated_tools</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M852.44,-2578.33C796.22,-2526.64 891.57,-2479.19 866.44,-2407.08 840.79,-2333.47 783.09,-2343.28 757.44,-2269.67 750.51,-2249.79 752.89,-2242.85 757.44,-2222.29 772.69,-2153.37 770.43,-2123.75 824.44,-2078.29 893.47,-2020.18 939.93,-2066.05 1020.44,-2025.29"/>
<path fill="none" stroke="black" d="M1020.44,-2023.29C1057.69,-2002.55 1101.59,-1982.17 1134.13,-1967.79"/>
<polygon fill="#06f9f9" stroke="black" points="1135.13,-1971.17 1142.88,-1963.95 1132.32,-1964.76 1135.13,-1971.17"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_fields -->
<g id="edge257" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1057.44,-2578.33C1062.75,-2553.82 1127.41,-2529.34 1171.8,-2515.27"/>
<polygon fill="#06f9f9" stroke="black" points="1172.64,-2518.67 1181.15,-2512.37 1170.56,-2511.99 1172.64,-2518.67"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_json_schema -->
<g id="edge258" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1057.44,-2578.33C1063.68,-2549.49 1084.68,-2551.94 1097.44,-2525.33 1122.57,-2472.9 1135.28,-2405.49 1140.83,-2367.92"/>
<polygon fill="#06f9f9" stroke="black" points="1144.25,-2368.71 1142.17,-2358.32 1137.32,-2367.74 1144.25,-2368.71"/>
</g>
<!-- pydantic_warnings&#45;&gt;pydantic_types -->
<g id="edge259" class="edge">
<title>pydantic_warnings&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1051.29,-2632.98C1052.17,-2617.89 1053.92,-2597.79 1057.44,-2580.33"/>
<path fill="none" stroke="black" d="M1057.44,-2578.33C1065.28,-2539.37 1005.18,-2559.81 985.44,-2525.33 923.8,-2417.66 935.8,-2261.4 944.06,-2197.82"/>
<polygon fill="#06f9f9" stroke="black" points="947.49,-2198.6 945.39,-2188.21 940.56,-2197.64 947.49,-2198.6"/>
</g>
<!-- pydantic_core -->
<g id="node73" class="node">
<title>pydantic_core</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1433.56,-2945.08 1349.31,-2945.08 1349.31,-2909.08 1433.56,-2909.08 1433.56,-2945.08"/>
<text text-anchor="middle" x="1391.44" y="-2923.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic_core</text>
</g>
<!-- pydantic_core&#45;&gt;pydantic -->
<g id="edge260" class="edge">
<title>pydantic_core&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1898.44,-2872.08C1902.58,-2829.61 1962.31,-2854.31 1974.44,-2813.39"/>
<path fill="none" stroke="black" d="M1974.44,-2811.39C2058.06,-2529.13 2186.64,-2389.78 2015.44,-2150.29 1973.1,-2091.07 1909.27,-2151.29 1860.44,-2097.29"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_aliases -->
<g id="edge261" class="edge">
<title>pydantic_core&#45;&gt;pydantic_aliases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1192.44,-2733.7C1192.62,-2720.12 1192.65,-2705.17 1192.63,-2692.17"/>
<polygon fill="blue" stroke="black" points="1196.13,-2692.37 1192.6,-2682.38 1189.13,-2692.4 1196.13,-2692.37"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_annotated_handlers -->
<g id="edge262" class="edge">
<title>pydantic_core&#45;&gt;pydantic_annotated_handlers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1433.8,-2926.04C1554.86,-2925.38 1893.96,-2919.38 1898.44,-2873.08"/>
<path fill="none" stroke="black" d="M1898.44,-2872.08C1902.42,-2830.81 1194.21,-2825.1 1154.44,-2813.39"/>
<path fill="none" stroke="black" d="M1154.44,-2811.39C875.21,-2729.19 501.58,-2551.93 700.44,-2339.37"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_fields -->
<g id="edge263" class="edge">
<title>pydantic_core&#45;&gt;pydantic_fields</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1154.44,-2811.39C1118.33,-2800.76 1191.94,-2773.34 1192.44,-2735.7"/>
<path fill="none" stroke="black" d="M1192.44,-2733.7C1192.87,-2700.71 1153.48,-2711.02 1140.44,-2680.7 1132.12,-2661.36 1132.47,-2652.82 1140.44,-2633.33 1152.92,-2602.78 1181.21,-2611.36 1192.44,-2580.33"/>
<path fill="none" stroke="black" d="M1192.44,-2578.33C1197.24,-2562.98 1202.1,-2545.71 1205.94,-2531.6"/>
<polygon fill="blue" stroke="black" points="1209.29,-2532.63 1208.51,-2522.06 1202.53,-2530.81 1209.29,-2532.63"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_functional_serializers -->
<g id="edge264" class="edge">
<title>pydantic_core&#45;&gt;pydantic_functional_serializers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1348.91,-2925.25C1197.13,-2922.01 687.75,-2908.56 624.44,-2873.08"/>
<path fill="none" stroke="black" d="M624.44,-2872.08C517.51,-2812.17 575.68,-2717.33 486.44,-2633.33 460.39,-2608.81 441.37,-2620.88 414.44,-2597.33 376.36,-2564.02 350.44,-2553.79 350.44,-2503.2 350.44,-2503.2 350.44,-2503.2 350.44,-2337.37 350.44,-2287.06 334.04,-2230.32 322.79,-2197.31"/>
<polygon fill="blue" stroke="black" points="326.17,-2196.37 319.56,-2188.1 319.56,-2198.69 326.17,-2196.37"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_functional_validators -->
<g id="edge265" class="edge">
<title>pydantic_core&#45;&gt;pydantic_functional_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1898.44,-2656.02C1894.56,-2618.58 1931.93,-2617.7 1936.44,-2580.33"/>
<path fill="none" stroke="black" d="M1936.44,-2578.33C1944.53,-2511.15 1936.44,-2493.75 1936.44,-2426.08 1936.44,-2426.08 1936.44,-2426.08 1936.44,-2337.37 1936.44,-2288.15 1941.19,-2230.99 1944.45,-2197.61"/>
<polygon fill="blue" stroke="black" points="1947.9,-2198.26 1945.42,-2187.96 1940.94,-2197.56 1947.9,-2198.26"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_json_schema -->
<g id="edge266" class="edge">
<title>pydantic_core&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M928.44,-2656.02C871.06,-2619.67 908.88,-2568.24 928.44,-2503.2"/>
<path fill="none" stroke="black" d="M928.44,-2501.2C942.97,-2471.14 902.02,-2446.5 928.44,-2426.08"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_main -->
<g id="edge267" class="edge">
<title>pydantic_core&#45;&gt;pydantic_main</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1898.44,-2872.08C1907.59,-2777.38 1909.82,-2752.47 1898.44,-2658.02"/>
<path fill="none" stroke="black" d="M1898.44,-2656.02C1892.73,-2621.64 1859.65,-2629.58 1846.44,-2597.33 1814.34,-2518.98 1881.56,-2478.72 1836.44,-2407.08 1786.26,-2327.41 1738.43,-2339.16 1650.44,-2305.67 1584.34,-2280.51 1559.22,-2300.22 1495.44,-2269.67 1455.74,-2250.66 1416.93,-2217.59 1392.65,-2194.63"/>
<polygon fill="blue" stroke="black" points="1395.11,-2192.14 1385.47,-2187.72 1390.25,-2197.18 1395.11,-2192.14"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_networks -->
<g id="edge268" class="edge">
<title>pydantic_core&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M624.44,-2872.08C574.46,-2837.79 638.86,-2794.57 624.44,-2735.7"/>
<path fill="none" stroke="black" d="M624.44,-2733.7C589.63,-2523.28 294.1,-2486.35 407.44,-2305.67 520.5,-2125.43 645.56,-2187.12 838.44,-2097.29"/>
<path fill="none" stroke="black" d="M838.44,-2095.29C945.22,-2056.3 1078.74,-2037.2 1145.44,-2029.54"/>
<polygon fill="blue" stroke="black" points="1145.66,-2033.04 1155.21,-2028.45 1144.88,-2026.08 1145.66,-2033.04"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_root_model -->
<g id="edge269" class="edge">
<title>pydantic_core&#45;&gt;pydantic_root_model</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M928.44,-2501.2C936.41,-2474.69 961.2,-2456.09 984.35,-2444.03"/>
<polygon fill="blue" stroke="black" points="985.64,-2447.29 993.09,-2439.76 982.57,-2441 985.64,-2447.29"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_type_adapter -->
<g id="edge270" class="edge">
<title>pydantic_core&#45;&gt;pydantic_type_adapter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1154.44,-2811.39C1045.63,-2757.57 1033.13,-2719.47 928.44,-2658.02"/>
<path fill="none" stroke="black" d="M928.44,-2656.02C890.34,-2627.23 861.18,-2639.32 838.44,-2597.33 830.82,-2583.26 837.11,-2577.27 838.44,-2561.33 849.36,-2430.34 872.13,-2400.22 887.44,-2269.67 888.98,-2256.47 886.43,-2160.08 895.44,-2150.29 934.83,-2107.49 1106.98,-2099 1194.68,-2097.49"/>
<polygon fill="blue" stroke="black" points="1194.51,-2100.99 1204.45,-2097.34 1194.4,-2093.99 1194.51,-2100.99"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_types -->
<g id="edge271" class="edge">
<title>pydantic_core&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M624.44,-2733.7C609.06,-2667.29 624.44,-2648.5 624.44,-2580.33 624.44,-2580.33 624.44,-2580.33 624.44,-2424.08 624.44,-2316.67 666.89,-2280.08 757.44,-2222.29 798.58,-2196.04 852.76,-2182.61 892.91,-2175.85"/>
<polygon fill="blue" stroke="black" points="893.27,-2179.33 902.6,-2174.31 892.18,-2172.42 893.27,-2179.33"/>
</g>
<!-- pydantic_core&#45;&gt;pydantic_version -->
<g id="edge272" class="edge">
<title>pydantic_core&#45;&gt;pydantic_version</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1898.44,-2872.08C1901.27,-2842.8 1870.28,-2828.09 1840.33,-2820.71"/>
<polygon fill="blue" stroke="black" points="1841.41,-2817.36 1830.89,-2818.64 1839.91,-2824.2 1841.41,-2817.36"/>
</g>
<!-- pydantic_core_core_schema -->
<g id="node74" class="node">
<title>pydantic_core_core_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1435.06,-3017.08 1347.81,-3017.08 1347.81,-2981.08 1435.06,-2981.08 1435.06,-3017.08"/>
<text text-anchor="middle" x="1391.44" y="-3002.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">pydantic_core.</text>
<text text-anchor="middle" x="1391.44" y="-2989.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">core_schema</text>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic -->
<g id="edge273" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1974.44,-2872.08C1980.65,-2846.75 1963.43,-2837.04 1974.44,-2813.39"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_annotated_handlers -->
<g id="edge274" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_annotated_handlers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M586.44,-2872.08C535.36,-2839.44 586.44,-2796.32 586.44,-2735.7 586.44,-2735.7 586.44,-2735.7 586.44,-2501.2 586.44,-2413.23 634.86,-2398.02 700.44,-2339.37"/>
<path fill="none" stroke="black" d="M700.44,-2337.37C726.92,-2311.97 760.68,-2287.47 785.97,-2270.4"/>
<polygon fill="blue" stroke="black" points="787.58,-2273.53 793.95,-2265.07 783.69,-2267.71 787.58,-2273.53"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_functional_serializers -->
<g id="edge275" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_functional_serializers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M312.44,-2244.98C308.43,-2229.79 308.17,-2212.29 309.02,-2197.94"/>
<polygon fill="blue" stroke="black" points="312.49,-2198.51 309.82,-2188.26 305.51,-2197.94 312.49,-2198.51"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_functional_validators -->
<g id="edge276" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_functional_validators</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1435.21,-2997.93C1573.55,-2996.32 1991.29,-2982.42 1974.44,-2873.08"/>
<path fill="none" stroke="black" d="M1974.44,-2872.08C1977.32,-2855.16 1964.39,-2852.79 1960.44,-2836.08 1934.16,-2724.98 1920.78,-2693.42 1936.44,-2580.33"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_json_schema -->
<g id="edge277" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_json_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M814.44,-2926.08C766.37,-2912 814.44,-2863.48 814.44,-2813.39 814.44,-2813.39 814.44,-2813.39 814.44,-2578.33 814.44,-2493.8 861.55,-2477.78 928.44,-2426.08"/>
<path fill="none" stroke="black" d="M928.44,-2424.08C975.88,-2382.79 1045.79,-2360.36 1093.12,-2349.14"/>
<polygon fill="blue" stroke="black" points="1093.89,-2352.55 1102.87,-2346.92 1092.34,-2345.73 1093.89,-2352.55"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_networks -->
<g id="edge278" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_networks</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1347.49,-2997.29C1249.81,-2994.54 1008.04,-2982.34 814.44,-2928.08"/>
<path fill="none" stroke="black" d="M814.44,-2926.08C714.26,-2898 676.27,-2925.56 586.44,-2873.08"/>
<path fill="none" stroke="black" d="M586.44,-2872.08C512.31,-2828.77 478.09,-2667.69 472.44,-2658.02"/>
<path fill="none" stroke="black" d="M472.44,-2656.02C444.02,-2611.19 406.39,-2634.42 368.44,-2597.33 333.62,-2563.31 312.44,-2551.88 312.44,-2503.2 312.44,-2503.2 312.44,-2503.2 312.44,-2424.08 312.44,-2345.37 332.49,-2323.09 312.44,-2246.98"/>
<path fill="none" stroke="black" d="M312.44,-2244.98C300.89,-2206.42 260.33,-2222.84 243.44,-2186.29 236.72,-2171.77 232.89,-2162.33 243.44,-2150.29 251.04,-2141.62 963.13,-2053.03 1145.35,-2030.49"/>
<polygon fill="blue" stroke="black" points="1145.58,-2033.99 1155.07,-2029.29 1144.72,-2027.04 1145.58,-2033.99"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_types -->
<g id="edge279" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M472.44,-2656.02C418.99,-2562.66 596.41,-2279.62 687.44,-2222.29 750.26,-2182.73 836.96,-2171.95 892.87,-2169.4"/>
<polygon fill="blue" stroke="black" points="892.94,-2172.9 902.8,-2169.04 892.68,-2165.91 892.94,-2172.9"/>
</g>
<!-- pydantic_core_core_schema&#45;&gt;pydantic_core -->
<g id="edge280" class="edge">
<title>pydantic_core_core_schema&#45;&gt;pydantic_core</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1391.44,-2980.78C1391.44,-2973.49 1391.44,-2964.81 1391.44,-2956.62"/>
<polygon fill="blue" stroke="black" points="1394.94,-2956.7 1391.44,-2946.7 1387.94,-2956.7 1394.94,-2956.7"/>
</g>
</g>
</svg>
