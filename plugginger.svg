<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.1 (20241206.2353)
 -->
<!-- Title: G Pages: 1 -->
<svg width="2800pt" height="1812pt"
 viewBox="0.00 0.00 2799.61 1811.54" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1807.54)">
<title>G</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="white" stroke="none" points="-4,4 -4,-1807.54 2795.61,-1807.54 2795.61,4 -4,4"/>
<!-- packaging -->
<g id="node1" class="node">
<title>packaging</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b65353" stroke="black" cx="335" cy="-1687.46" rx="40.08" ry="18"/>
<text text-anchor="middle" x="335" y="-1684.34" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging</text>
</g>
<!-- plugginger__internal_validation -->
<g id="node14" class="node">
<title>plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="478,-1415.38 408,-1415.38 408,-1369.13 478,-1369.13 478,-1415.38"/>
<text text-anchor="middle" x="443" y="-1401.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="443" y="-1389.13" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="443" y="-1376.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">validation</text>
</g>
<!-- packaging&#45;&gt;plugginger__internal_validation -->
<g id="edge1" class="edge">
<title>packaging&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M336.41,-1669.04C337.55,-1650.76 338.48,-1621.25 335,-1596.07"/>
</g>
<!-- packaging_specifiers -->
<g id="node2" class="node">
<title>packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#b34c4c" stroke="black" cx="228" cy="-1687.46" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="228" y="-1690.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="228" y="-1677.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">specifiers</text>
</g>
<!-- packaging_specifiers&#45;&gt;plugginger__internal_validation -->
<g id="edge2" class="edge">
<title>packaging_specifiers&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M258.52,-1668.76C291.12,-1648.72 337.79,-1616.25 335,-1596.07"/>
<path fill="none" stroke="black" d="M335,-1594.07C325.81,-1527.54 376.24,-1460.99 411.3,-1423.67"/>
<polygon fill="#b34c4c" stroke="black" points="413.5,-1426.44 417.91,-1416.8 408.45,-1421.58 413.5,-1426.44"/>
</g>
<!-- packaging_version -->
<g id="node3" class="node">
<title>packaging_version</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#c24747" stroke="black" cx="329" cy="-1779.85" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="329" y="-1783.1" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">packaging.</text>
<text text-anchor="middle" x="329" y="-1770.35" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">version</text>
</g>
<!-- packaging_version&#45;&gt;packaging_specifiers -->
<g id="edge3" class="edge">
<title>packaging_version&#45;&gt;packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M306.33,-1758.57C292.3,-1746.01 274.09,-1729.71 258.77,-1716"/>
<polygon fill="#c24747" stroke="black" points="261.5,-1713.75 251.72,-1709.69 256.84,-1718.97 261.5,-1713.75"/>
</g>
<!-- packaging_version&#45;&gt;plugginger__internal_validation -->
<g id="edge4" class="edge">
<title>packaging_version&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M353.26,-1758.91C364.34,-1748.51 376.6,-1734.89 384,-1720.17 433.41,-1621.86 441.94,-1488.52 443.08,-1426.96"/>
<polygon fill="#c24747" stroke="black" points="446.58,-1427.19 443.21,-1417.15 439.58,-1427.1 446.58,-1427.19"/>
</g>
<!-- plugginger -->
<g id="node4" class="node">
<title>plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1615.88,-1299.83 1548.12,-1299.83 1548.12,-1263.83 1615.88,-1263.83 1615.88,-1299.83"/>
<text text-anchor="middle" x="1582" y="-1278.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger</text>
</g>
<!-- plugginger__internal -->
<g id="node5" class="node">
<title>plugginger__internal</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f910" stroke="black" cx="2367" cy="-1392.26" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="2367" y="-1395.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2367" y="-1382.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal</text>
</g>
<!-- plugginger_api_app -->
<g id="node16" class="node">
<title>plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1898,-621.78 1828,-621.78 1828,-575.53 1898,-575.53 1898,-621.78"/>
<text text-anchor="middle" x="1863" y="-608.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1863" y="-595.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1863" y="-582.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_app -->
<g id="edge5" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2352.22,-1369.26C2327.1,-1331.97 2277.82,-1259.4 2268,-1249.13 2249.82,-1230.11 2236.3,-1234.53 2221,-1213.13 2087.12,-1025.86 2066,-949.11 2066,-718.91"/>
<path fill="none" stroke="black" d="M2066,-716.91C2062.97,-691.25 2088.07,-672.22 2066,-658.78"/>
<path fill="none" stroke="black" d="M2066,-657.78C2017.24,-628.1 1952.68,-613.02 1909.59,-605.76"/>
<polygon fill="#10f910" stroke="black" points="1910.23,-602.32 1899.81,-604.2 1909.13,-609.23 1910.23,-602.32"/>
</g>
<!-- plugginger_api_builder -->
<g id="node19" class="node">
<title>plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2063,-1203.55 1993,-1203.55 1993,-1157.3 2063,-1157.3 2063,-1203.55"/>
<text text-anchor="middle" x="2028" y="-1190.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2028" y="-1177.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="2028" y="-1164.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_builder -->
<g id="edge6" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2338.72,-1372.42C2307.82,-1351.51 2257.23,-1316.27 2216,-1282.83"/>
</g>
<!-- plugginger__internal_graph -->
<g id="node6" class="node">
<title>plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#2fbc2f" stroke="black" cx="1464" cy="-1502.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1464" y="-1512.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1464" y="-1499.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="1464" y="-1486.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">graph</text>
</g>
<!-- plugginger__internal_graph&#45;&gt;plugginger__internal_validation -->
<g id="edge7" class="edge">
<title>plugginger__internal_graph&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1427.72,-1480.18C1419.22,-1476.07 1410.01,-1472.32 1401,-1469.98 1221.69,-1423.29 750.32,-1466.97 568,-1433.98 541.24,-1429.13 512.32,-1419.98 489.04,-1411.58"/>
<polygon fill="#2fbc2f" stroke="black" points="490.45,-1408.36 479.86,-1408.18 488.02,-1414.93 490.45,-1408.36"/>
</g>
<!-- plugginger__internal_graph&#45;&gt;plugginger_api_builder -->
<g id="edge8" class="edge">
<title>plugginger__internal_graph&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1480.07,-1471.25C1485.67,-1459.79 1491.64,-1446.5 1496,-1433.98 1523.73,-1354.32 1476.71,-1306 1539,-1249.13 1547.17,-1241.67 1860.7,-1202.15 1981.35,-1187.18"/>
<polygon fill="#2fbc2f" stroke="black" points="1981.75,-1190.66 1991.24,-1185.95 1980.88,-1183.71 1981.75,-1190.66"/>
</g>
<!-- plugginger__internal_proxy -->
<g id="node7" class="node">
<title>plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2019,-457.28 1949,-457.28 1949,-411.03 2019,-411.03 2019,-457.28"/>
<text text-anchor="middle" x="1984" y="-443.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1984" y="-431.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1984" y="-418.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">proxy</text>
</g>
<!-- plugginger__internal_proxy&#45;&gt;plugginger_api_builder -->
<g id="edge9" class="edge">
<title>plugginger__internal_proxy&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1997.34,-457.53C2019.73,-496.55 2064,-580.29 2080,-657.78 2087.48,-694.02 2082.18,-704.1 2080,-741.03 2077.63,-781.16 2044.23,-1049.9 2032.24,-1145.64"/>
<polygon fill="blue" stroke="black" points="2028.78,-1145.08 2031.01,-1155.44 2035.73,-1145.95 2028.78,-1145.08"/>
</g>
<!-- plugginger__internal_runtime -->
<g id="node8" class="node">
<title>plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="827,-823.28 757,-823.28 757,-777.03 827,-777.03 827,-823.28"/>
<text text-anchor="middle" x="792" y="-809.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="792" y="-797.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="792" y="-784.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime</text>
</g>
<!-- plugginger__internal_runtime_facade -->
<g id="node13" class="node">
<title>plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1430.25,-741.03 1337.75,-741.03 1337.75,-694.78 1430.25,-694.78 1430.25,-741.03"/>
<text text-anchor="middle" x="1384" y="-727.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1384" y="-714.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1384" y="-702.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime_facade</text>
</g>
<!-- plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge10" class="edge">
<title>plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M827.37,-781.61C831.88,-779.85 836.5,-778.26 841,-777.03 1013.05,-730.27 1225.34,-720.85 1325.91,-719.14"/>
<polygon fill="blue" stroke="black" points="1325.86,-722.64 1335.81,-718.99 1325.76,-715.64 1325.86,-722.64"/>
</g>
<!-- plugginger__internal_runtime_dispatcher -->
<g id="node9" class="node">
<title>plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="870,-1311.33 800,-1311.33 800,-1252.33 870,-1252.33 870,-1311.33"/>
<text text-anchor="middle" x="835" y="-1297.83" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="835" y="-1285.08" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="835" y="-1272.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="835" y="-1259.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dispatcher</text>
</g>
<!-- plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime -->
<g id="edge11" class="edge">
<title>plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M847.39,-1251.9C863.33,-1211.98 889,-1137.39 889,-1071 889,-1071 889,-1071 889,-991.28 889,-943.11 949.88,-932.17 927,-889.78"/>
<path fill="none" stroke="black" d="M927,-887.78C916.23,-870.01 872.12,-843.51 837.22,-824.51"/>
<polygon fill="blue" stroke="black" points="838.99,-821.49 828.52,-819.83 835.67,-827.65 838.99,-821.49"/>
</g>
<!-- plugginger__internal_runtime_executors -->
<g id="node10" class="node">
<title>plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#339933" stroke="black" cx="1042" cy="-1070" rx="49.5" ry="41.72"/>
<text text-anchor="middle" x="1042" y="-1086" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1042" y="-1073.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">_internal.</text>
<text text-anchor="middle" x="1042" y="-1060.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">runtime.</text>
<text text-anchor="middle" x="1042" y="-1047.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">executors</text>
</g>
<!-- plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime -->
<g id="edge12" class="edge">
<title>plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1001.36,-1045.42C984.29,-1032.41 967.71,-1014.39 965,-992.28"/>
</g>
<!-- plugginger__internal_runtime_fault_policy -->
<g id="node11" class="node">
<title>plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#2bac2b" stroke="black" cx="890" cy="-1392.26" rx="51.62" ry="41.72"/>
<text text-anchor="middle" x="890" y="-1408.26" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="890" y="-1395.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">_internal.</text>
<text text-anchor="middle" x="890" y="-1382.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">runtime.</text>
<text text-anchor="middle" x="890" y="-1370.01" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">fault_policy</text>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime -->
<g id="edge13" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M905.53,-1352.16C931.97,-1281 981.34,-1125.37 965,-992.28"/>
<path fill="none" stroke="black" d="M965,-991.28C953.88,-944.42 951.97,-930.98 927,-889.78"/>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge14" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M870.76,-1353.34C865.56,-1343.08 859.92,-1331.96 854.69,-1321.65"/>
<polygon fill="#2bac2b" stroke="black" points="857.96,-1320.36 850.32,-1313.02 851.72,-1323.53 857.96,-1320.36"/>
</g>
<!-- plugginger__internal_runtime_lifecycle -->
<g id="node12" class="node">
<title>plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="827,-918.28 757,-918.28 757,-859.28 827,-859.28 827,-918.28"/>
<text text-anchor="middle" x="792" y="-904.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="792" y="-892.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="792" y="-879.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="792" y="-866.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">lifecycle</text>
</g>
<!-- plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime -->
<g id="edge15" class="edge">
<title>plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M792,-858.94C792,-851.29 792,-842.96 792,-835.07"/>
<polygon fill="blue" stroke="black" points="795.5,-835.09 792,-825.09 788.5,-835.09 795.5,-835.09"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app -->
<g id="edge16" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1430.36,-705.56C1521.89,-683.16 1724.84,-633.48 1816.49,-611.04"/>
<polygon fill="blue" stroke="black" points="1817.27,-614.46 1826.15,-608.68 1815.6,-607.66 1817.27,-614.46"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder -->
<g id="edge17" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1388.88,-741.31C1398.99,-782.4 1426.09,-870.02 1482,-918.28 1645.14,-1059.11 1800.9,-910.28 1944.31,-1060.64"/>
<polygon fill="blue" stroke="black" points="1941.63,-1062.89 1950.98,-1067.89 1946.79,-1058.16 1941.63,-1062.89"/>
<path fill="none" stroke="black" d="M1952,-1071C1965.94,-1086.72 1964.25,-1094.31 1976,-1111.72 1986.53,-1127.32 1999.41,-1144.11 2009.72,-1157.06"/>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger_api_builder -->
<g id="edge18" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M478.23,-1371.97C539.6,-1339.5 671.06,-1274.96 791,-1249.13 1005.13,-1203.01 1557.68,-1230.82 1776,-1213.13 1847.97,-1207.3 1930.97,-1195.96 1981.45,-1188.54"/>
<polygon fill="blue" stroke="black" points="1981.75,-1192.03 1991.13,-1187.1 1980.73,-1185.11 1981.75,-1192.03"/>
</g>
<!-- plugginger_api -->
<g id="node15" class="node">
<title>plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1328.5,-1088 1243.5,-1088 1243.5,-1052 1328.5,-1052 1328.5,-1088"/>
<text text-anchor="middle" x="1286" y="-1066.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.api</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_proxy -->
<g id="edge19" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1328.9,-1063C1382.98,-1054.01 1477.39,-1033.51 1548,-992.28 1754.34,-871.81 1664.79,-714.4 1800,-517.41"/>
<path fill="none" stroke="black" d="M1800,-515.41C1828.52,-467.65 1893,-448.25 1937.51,-440.41"/>
<polygon fill="blue" stroke="black" points="1937.84,-443.9 1947.15,-438.86 1936.72,-436.99 1937.84,-443.9"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge20" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1243.03,-1060.16C1174.5,-1045.59 1044.01,-1015.81 1003,-992.28"/>
<path fill="none" stroke="black" d="M1003,-991.28C947.74,-959.58 881.06,-928.66 837.66,-909.44"/>
<polygon fill="blue" stroke="black" points="839.09,-906.24 828.53,-905.42 836.27,-912.65 839.09,-906.24"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge21" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1281.55,-1051.8C1276.22,-1029.64 1268,-989.86 1268,-955.28 1268,-955.28 1268,-955.28 1268,-887.78 1268,-831.22 1312.76,-779.77 1346.67,-748.9"/>
<polygon fill="blue" stroke="black" points="1348.73,-751.76 1353.89,-742.51 1344.09,-746.51 1348.73,-751.76"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_validation -->
<g id="edge22" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1243.15,-1080.55C1082.6,-1116.42 521.71,-1242.77 439.9,-1275.32"/>
<polygon fill="blue" stroke="black" points="438.35,-1272.17 431.32,-1280.1 441.76,-1278.29 438.35,-1272.17"/>
</g>
<!-- plugginger_cli_cmd_core_freeze -->
<g id="node25" class="node">
<title>plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2607.62,-273.06 2508.38,-273.06 2508.38,-226.81 2607.62,-226.81 2607.62,-273.06"/>
<text text-anchor="middle" x="2558" y="-259.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2558" y="-246.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="2558" y="-234.06" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_core_freeze</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge23" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1304.1,-1051.68C1319.94,-1036.36 1343.42,-1013.24 1363,-992.28 1396.99,-955.92 1487.42,-867.64 1510,-823.28 1531.6,-780.87 1534,-766.51 1534,-718.91 1534,-718.91 1534,-718.91 1534,-433.16 1534,-376.44 1522.19,-345.65 1566,-309.63 1637.77,-250.61 2291.32,-249.36 2496.6,-250.45"/>
<polygon fill="blue" stroke="black" points="2496.51,-253.95 2506.53,-250.5 2496.55,-246.95 2496.51,-253.95"/>
</g>
<!-- plugginger_cli_cmd_stubs_generate -->
<g id="node27" class="node">
<title>plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2538.62,-118.25 2421.38,-118.25 2421.38,-72 2538.62,-72 2538.62,-118.25"/>
<text text-anchor="middle" x="2480" y="-104.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2480" y="-92" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="2480" y="-79.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_stubs_generate</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge24" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M855,-799.16C844.36,-791.75 844.97,-786.39 836,-777.03 766.1,-704.11 727.44,-704.88 670,-621.78 641.47,-580.51 627,-567.58 627,-517.41 627,-517.41 627,-517.41 627,-433.16 627,-377.23 639.18,-364.29 651,-309.63 656.67,-283.42 662.29,-277.62 665,-250.94"/>
<path fill="none" stroke="black" d="M665,-248.94C667.06,-215.36 636.8,-191.59 665,-173.25"/>
</g>
<!-- plugginger_cli_utils -->
<g id="node28" class="node">
<title>plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#23c823" stroke="black" cx="2558" cy="-342.33" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2558" y="-351.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2558" y="-339.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">cli.</text>
<text text-anchor="middle" x="2558" y="-326.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">utils</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_utils -->
<g id="edge25" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1327.59,-1051.55C1380.03,-1027.83 1470.4,-981.07 1529,-918.28 1679.62,-756.91 1626.93,-646.32 1786,-493.28 1841.92,-439.49 1865.87,-433.95 1940,-411.03 2136.95,-350.15 2384.01,-342.43 2496.78,-342.43"/>
<polygon fill="blue" stroke="black" points="2496.72,-345.93 2506.73,-342.46 2496.74,-338.93 2496.72,-345.93"/>
</g>
<!-- plugginger_stubgen -->
<g id="node43" class="node">
<title>plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#35a135" stroke="black" cx="588" cy="-249.94" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="588" y="-253.19" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="588" y="-240.44" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">stubgen</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_stubgen -->
<g id="edge26" class="edge">
<title>plugginger_api&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1003,-991.28C947.68,-957.51 983.14,-908.53 941,-859.28 911.01,-824.23 895.4,-823.44 855,-801.16"/>
<path fill="none" stroke="black" d="M855,-799.16C844.09,-792.16 847.05,-783.82 836,-777.03 685.27,-684.48 378.62,-817.84 456,-658.78"/>
</g>
<!-- plugginger_testing_helpers -->
<g id="node46" class="node">
<title>plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1531,-273.06 1461,-273.06 1461,-226.81 1531,-226.81 1531,-273.06"/>
<text text-anchor="middle" x="1496" y="-259.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1496" y="-246.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">testing.</text>
<text text-anchor="middle" x="1496" y="-234.06" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">helpers</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_testing_helpers -->
<g id="edge27" class="edge">
<title>plugginger_api&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M855,-799.16C827.45,-783.7 817,-549 817,-517.41 817,-517.41 817,-517.41 817,-433.16 817,-301.86 1296.57,-262.55 1449.64,-253.35"/>
<polygon fill="blue" stroke="black" points="1449.63,-256.85 1459.41,-252.78 1449.23,-249.87 1449.63,-256.85"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger__internal_proxy -->
<g id="edge28" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1827.73,-576.09C1806.41,-560.25 1785.87,-538 1800,-517.41"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api -->
<g id="edge29" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1856.27,-622.19C1834.76,-690.03 1761.01,-893.73 1624,-992.28 1538.4,-1053.86 1411.6,-1067.08 1340.13,-1069.3"/>
<polygon fill="blue" stroke="black" points="1340.15,-1065.8 1330.24,-1069.54 1340.32,-1072.8 1340.15,-1065.8"/>
</g>
<!-- plugginger_api_app_plugin -->
<g id="node17" class="node">
<title>plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1645,-365.45 1575,-365.45 1575,-319.2 1645,-319.2 1645,-365.45"/>
<text text-anchor="middle" x="1610" y="-351.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1610" y="-339.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1610" y="-326.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app_plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_app_plugin -->
<g id="edge30" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1908.37,-575.19C1973.8,-539.96 2081.27,-469.68 2028,-411.03 1971.86,-349.23 1732.14,-404.46 1654,-375.03 1651.47,-374.08 1648.96,-372.94 1646.49,-371.68"/>
<polygon fill="blue" stroke="black" points="1906.8,-572.06 1899.59,-579.83 1910.07,-578.25 1906.8,-572.06"/>
<polygon fill="blue" stroke="black" points="1648.34,-368.7 1637.94,-366.63 1644.77,-374.73 1648.34,-368.7"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_builder -->
<g id="edge31" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1898.11,-611.04C1915.62,-618.58 1935.41,-630.33 1946.68,-647.61"/>
<polygon fill="blue" stroke="black" points="1943.56,-649.2 1951.3,-656.44 1949.77,-645.96 1943.56,-649.2"/>
<path fill="none" stroke="black" d="M1952,-658.78C1971.66,-717.99 1952,-736.77 1952,-799.16 1952,-955.28 1952,-955.28 1952,-955.28 1952,-1005.83 1920.89,-1029.17 1952,-1069"/>
</g>
<!-- plugginger_api_depends -->
<g id="node20" class="node">
<title>plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1345,-539.53 1275,-539.53 1275,-493.28 1345,-493.28 1345,-539.53"/>
<text text-anchor="middle" x="1310" y="-526.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1310" y="-513.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1310" y="-500.53" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">depends</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_depends -->
<g id="edge32" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1827.64,-593.07C1755.45,-583.62 1585.95,-561.09 1444,-539.53 1414.89,-535.11 1382.29,-529.74 1356.54,-525.4"/>
<polygon fill="blue" stroke="black" points="1357.16,-521.95 1346.72,-523.73 1355.99,-528.85 1357.16,-521.95"/>
</g>
<!-- plugginger_api_plugin -->
<g id="node22" class="node">
<title>plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1499,-457.28 1429,-457.28 1429,-411.03 1499,-411.03 1499,-457.28"/>
<text text-anchor="middle" x="1464" y="-443.78" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1464" y="-431.03" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1464" y="-418.28" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_plugin -->
<g id="edge33" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1817.29,-579.04C1740.47,-547.76 1586.39,-485 1509.63,-453.74"/>
<polygon fill="blue" stroke="black" points="1815.61,-582.14 1826.19,-582.67 1818.25,-575.65 1815.61,-582.14"/>
<polygon fill="blue" stroke="black" points="1511.31,-450.65 1500.73,-450.12 1508.67,-457.13 1511.31,-450.65"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api -->
<g id="edge34" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1610,-365.88C1610,-398.76 1610,-461.78 1610,-515.41 1610,-718.91 1610,-718.91 1610,-718.91 1610,-792.89 1475.56,-943.43 1420,-992.28 1394.94,-1014.32 1362.57,-1033.06 1335.92,-1046.51"/>
<polygon fill="blue" stroke="black" points="1334.42,-1043.35 1327,-1050.91 1337.52,-1049.62 1334.42,-1043.35"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge36" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1636.63,-365.89C1642.09,-369.5 1648,-372.79 1654,-375.03 1819.13,-436.74 1891.59,-331.68 2049,-411.03 2104.48,-439 2142,-453.28 2142,-515.41 2142,-801.16 2142,-801.16 2142,-801.16 2142,-934.08 2076.67,-1082.52 2044.92,-1146.82"/>
<polygon fill="blue" stroke="black" points="2041.8,-1145.25 2040.45,-1155.76 2048.05,-1148.38 2041.8,-1145.25"/>
</g>
<!-- plugginger_api_background -->
<g id="node18" class="node">
<title>plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3ab03a" stroke="black" cx="1168" cy="-1281.83" rx="51.62" ry="32.7"/>
<text text-anchor="middle" x="1168" y="-1291.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1168" y="-1278.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">api.</text>
<text text-anchor="middle" x="1168" y="-1265.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">background</text>
</g>
<!-- plugginger_api_background&#45;&gt;plugginger_api -->
<g id="edge37" class="edge">
<title>plugginger_api_background&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1204.47,-1258.47C1220.64,-1246.82 1238.55,-1231.22 1250,-1213.13 1272.26,-1177.97 1280.77,-1129.76 1284.01,-1099.5"/>
<polygon fill="#3ab03a" stroke="black" points="1287.49,-1099.89 1284.94,-1089.61 1280.52,-1099.24 1287.49,-1099.89"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_api -->
<g id="edge38" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1992.71,-1174.27C1873.26,-1156.81 1483.56,-1099.87 1339.86,-1078.87"/>
<polygon fill="blue" stroke="black" points="1340.63,-1075.45 1330.23,-1077.47 1339.62,-1082.38 1340.63,-1075.45"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge39" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2254,-1069C2299.95,-1019.91 2636,-666.9 2636,-599.66"/>
<path fill="none" stroke="black" d="M2636,-597.66C2619.07,-470.46 2668.84,-426.56 2616,-309.63 2611.31,-299.25 2603.94,-289.66 2596.01,-281.36"/>
<polygon fill="blue" stroke="black" points="2598.65,-279.05 2589.05,-274.58 2593.76,-284.06 2598.65,-279.05"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_utils -->
<g id="edge40" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2063.43,-1174.11C2115.56,-1164.18 2211.31,-1137.46 2254,-1071"/>
<path fill="none" stroke="black" d="M2254,-1069C2260.4,-1059.04 2370,-670.63 2370,-658.78 2370,-658.78 2370,-658.78 2370,-597.66 2370,-500.4 2460.01,-415.96 2515.47,-373.17"/>
<polygon fill="blue" stroke="black" points="2517.32,-376.16 2523.18,-367.33 2513.1,-370.58 2517.32,-376.16"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger__internal_validation -->
<g id="edge41" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1274.77,-519.54C1179.82,-525.63 920.45,-544.7 841,-575.53 588.2,-673.64 473.65,-790.84 468.17,-1057.81"/>
<polygon fill="blue" stroke="black" points="464.67,-1057.44 468.02,-1067.49 471.67,-1057.54 464.67,-1057.44"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api -->
<g id="edge42" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1287.29,-539.99C1263.64,-566.02 1230,-611.22 1230,-657.78 1230,-955.28 1230,-955.28 1230,-955.28 1230,-987.87 1248.74,-1020.81 1264.54,-1042.79"/>
<polygon fill="blue" stroke="black" points="1261.58,-1044.68 1270.38,-1050.58 1267.18,-1040.48 1261.58,-1044.68"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_builder -->
<g id="edge43" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1344.71,-540C1446.99,-607.95 1752.1,-820.53 1944.9,-1060.11"/>
<polygon fill="blue" stroke="black" points="1942.08,-1062.19 1951.06,-1067.82 1947.56,-1057.82 1942.08,-1062.19"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_plugin -->
<g id="edge44" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1345.33,-497C1367.07,-485.67 1395.14,-471.04 1418.53,-458.85"/>
<polygon fill="blue" stroke="black" points="1419.97,-462.05 1427.23,-454.32 1416.74,-455.84 1419.97,-462.05"/>
</g>
<!-- plugginger_api_events -->
<g id="node21" class="node">
<title>plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#35a135" stroke="black" cx="1192" cy="-1180.43" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1192" y="-1190.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1192" y="-1177.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">api.</text>
<text text-anchor="middle" x="1192" y="-1164.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">events</text>
</g>
<!-- plugginger_api_events&#45;&gt;plugginger_api -->
<g id="edge45" class="edge">
<title>plugginger_api_events&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1216.21,-1151.5C1230.8,-1134.67 1249.27,-1113.37 1263.54,-1096.91"/>
<polygon fill="#35a135" stroke="black" points="1266.09,-1099.32 1269.99,-1089.47 1260.8,-1094.73 1266.09,-1099.32"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge46" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1428.71,-437.51C1304.22,-446.74 893,-485.7 893,-597.66 893,-658.78 893,-658.78 893,-658.78 893,-716.33 841.75,-710.45 793,-741.03 760.66,-761.33 735.58,-744.87 715,-777.03 703.92,-794.35 706.3,-804.66 715,-823.28 722.07,-838.43 734.65,-851.28 747.61,-861.43"/>
<polygon fill="blue" stroke="black" points="745.23,-864.02 755.37,-867.12 749.38,-858.38 745.23,-864.02"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge47" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1462.79,-457.69C1462,-470.78 1460.8,-487.87 1459.19,-504.25"/>
<polygon fill="blue" stroke="black" points="1455.74,-503.59 1458.16,-513.9 1462.7,-504.33 1455.74,-503.59"/>
<path fill="none" stroke="black" d="M1458,-517.41C1440.95,-583.4 1410.04,-657.89 1394.1,-694.36"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_validation -->
<g id="edge48" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1428.73,-436.25C1286.03,-440.87 755.86,-460.13 689,-493.28 607.64,-533.63 418,-708.35 418,-799.16 418,-955.28 418,-955.28 418,-955.28 418,-1006.51 454.83,-1013.45 465.91,-1057.9"/>
<polygon fill="blue" stroke="black" points="462.43,-1058.34 467.72,-1067.52 469.31,-1057.04 462.43,-1058.34"/>
<path fill="none" stroke="black" d="M468,-1071C473.47,-1165.62 474.85,-1197.34 430,-1280.83"/>
<path fill="none" stroke="black" d="M430,-1282.83C415.55,-1309.72 424.42,-1345.67 432.99,-1368.65"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api -->
<g id="edge49" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1458,-517.41C1450.89,-579.39 1458,-595.39 1458,-657.78 1458,-718.91 1458,-718.91 1458,-718.91 1458,-859.77 1344.45,-1002.85 1301.87,-1051.55"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin -->
<g id="edge51" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1499.34,-411.41C1519.09,-399.27 1543.89,-384 1565.01,-371.01"/>
<polygon fill="blue" stroke="black" points="1566.76,-374.04 1573.44,-365.82 1563.09,-368.08 1566.76,-374.04"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge52" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1499.47,-443.87C1542.12,-454.56 1615.76,-473.69 1678,-493.28 1781.15,-525.76 1824.81,-505.25 1907,-575.53 1935.58,-599.97 1936.62,-615.78 1947.92,-647.05"/>
<polygon fill="blue" stroke="black" points="1944.64,-648.27 1951.46,-656.37 1951.18,-645.78 1944.64,-648.27"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge53" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1496,-341.33C1509.47,-329.89 1504.4,-317.94 1520,-309.63 1606.08,-263.75 2286.87,-253.5 2496.67,-251.43"/>
<polygon fill="blue" stroke="black" points="2496.58,-254.93 2506.54,-251.33 2496.51,-247.93 2496.58,-254.93"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge54" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M665,-341.33C632.9,-317.17 660.93,-290.91 665,-250.94"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_stubgen -->
<g id="edge55" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1428.74,-431.29C1282.3,-423.26 726.9,-389.92 665,-343.33"/>
<path fill="none" stroke="black" d="M665,-341.33C643.16,-324.89 623.29,-301.23 609.2,-282.24"/>
<polygon fill="blue" stroke="black" points="612.16,-280.36 603.46,-274.3 606.48,-284.46 612.16,-280.36"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_testing_helpers -->
<g id="edge56" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1465.77,-410.79C1468.64,-390.13 1476.19,-360.15 1496,-343.33"/>
<path fill="none" stroke="black" d="M1496,-341.33C1511.8,-327.92 1511.3,-304.28 1507.11,-284.55"/>
<polygon fill="blue" stroke="black" points="1510.53,-283.79 1504.68,-274.95 1503.74,-285.5 1510.53,-283.79"/>
</g>
<!-- plugginger_api_service -->
<g id="node23" class="node">
<title>plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#35a135" stroke="black" cx="1051" cy="-1180.43" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1051" y="-1190.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1051" y="-1177.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">api.</text>
<text text-anchor="middle" x="1051" y="-1164.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">service</text>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_api -->
<g id="edge57" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1091.13,-1160.91C1131.82,-1142.14 1194.8,-1113.08 1238.08,-1093.11"/>
<polygon fill="#35a135" stroke="black" points="1239.38,-1096.37 1246.99,-1089 1236.44,-1090.01 1239.38,-1096.37"/>
</g>
<!-- plugginger_cli -->
<g id="node24" class="node">
<title>plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2617.62,-36 2536.38,-36 2536.38,0 2617.62,0 2617.62,-36"/>
<text text-anchor="middle" x="2577" y="-14.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.cli</text>
</g>
<!-- plugginger_cli_cmd_core_freeze&#45;&gt;plugginger_cli -->
<g id="edge58" class="edge">
<title>plugginger_cli_cmd_core_freeze&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2604.28,-226.41C2634.25,-210.51 2668.68,-189.28 2674,-173.25"/>
<path fill="none" stroke="black" d="M2674,-171.25C2690.46,-121.69 2644.41,-72.15 2610.24,-43.6"/>
<polygon fill="blue" stroke="black" points="2612.57,-40.98 2602.6,-37.41 2608.17,-46.42 2612.57,-40.98"/>
</g>
<!-- plugginger_cli_cmd_project_run -->
<g id="node26" class="node">
<title>plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2722.5,-273.06 2625.5,-273.06 2625.5,-226.81 2722.5,-226.81 2722.5,-273.06"/>
<text text-anchor="middle" x="2674" y="-259.56" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2674" y="-246.81" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="2674" y="-234.06" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_project_run</text>
</g>
<!-- plugginger_cli_cmd_project_run&#45;&gt;plugginger_cli -->
<g id="edge59" class="edge">
<title>plugginger_cli_cmd_project_run&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2671.62,-226.5C2670.58,-211.31 2670.32,-190.87 2674,-173.25"/>
</g>
<!-- plugginger_cli_cmd_stubs_generate&#45;&gt;plugginger_cli -->
<g id="edge60" class="edge">
<title>plugginger_cli_cmd_stubs_generate&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2509.1,-71.59C2520.64,-62.65 2533.93,-52.36 2545.68,-43.26"/>
<polygon fill="blue" stroke="black" points="2547.74,-46.09 2553.51,-37.2 2543.46,-40.55 2547.74,-46.09"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge61" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2558,-309.21C2558,-301.23 2558,-292.66 2558,-284.61"/>
<polygon fill="#23c823" stroke="black" points="2561.5,-284.83 2558,-274.83 2554.5,-284.83 2561.5,-284.83"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge62" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2589.39,-316.87C2603.73,-305.7 2620.83,-292.37 2635.9,-280.63"/>
<polygon fill="#23c823" stroke="black" points="2638.04,-283.4 2643.78,-274.49 2633.74,-277.87 2638.04,-283.4"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge63" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2529.02,-315.72C2517.72,-304.05 2505.86,-289.34 2499,-273.63 2478.52,-226.73 2476.55,-166.48 2477.74,-129.61"/>
<polygon fill="#23c823" stroke="black" points="2481.22,-130.17 2478.15,-120.03 2474.23,-129.87 2481.22,-130.17"/>
</g>
<!-- plugginger_config -->
<g id="node29" class="node">
<title>plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#05e505" stroke="black" cx="2559" cy="-1392.26" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="2559" y="-1395.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2559" y="-1382.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge64" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2537.06,-1370.91C2508.94,-1346.02 2457.62,-1304.54 2406,-1282.83"/>
<path fill="none" stroke="black" d="M2406,-1280.83C2345.41,-1255.35 1299.72,-1123.18 1235,-1111.72 1189.73,-1103.71 1138.87,-1092.9 1100.72,-1084.43"/>
<polygon fill="#05e505" stroke="black" points="1101.82,-1081.09 1091.29,-1082.33 1100.29,-1087.92 1101.82,-1081.09"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge65" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2551.59,-1368.57C2544.19,-1346.46 2532.2,-1311.99 2520,-1282.83"/>
<path fill="none" stroke="black" d="M2520,-1280.83C2504.7,-1244.26 2298.87,-1053.15 2268,-1028.28 2213.75,-984.59 2069.63,-882.62 2004,-859.28 1789.89,-783.14 1702.08,-896.94 1496,-801.16"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_app -->
<g id="edge66" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2520,-1280.83C2505.85,-1236.02 2499.68,-1225.72 2484,-1181.43"/>
<path fill="none" stroke="black" d="M2484,-1179.43C2453.44,-1093.11 2406.24,-1095.44 2344,-1028.28 2298.68,-979.38 2283.5,-970.47 2242,-918.28 2155.26,-809.21 2185.04,-731.24 2066,-658.78"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_builder -->
<g id="edge67" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2406,-1280.83C2291.41,-1232.64 2147.25,-1202.52 2074.52,-1189.28"/>
<polygon fill="#05e505" stroke="black" points="2075.43,-1185.89 2064.97,-1187.57 2074.19,-1192.78 2075.43,-1185.89"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge68" class="edge">
<title>plugginger_config&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2484,-991.28C2528.08,-809.85 2663.4,-784.34 2636,-599.66"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge69" class="edge">
<title>plugginger_config&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2520,-1280.83C2452.59,-1067.31 2674,-1025.06 2674,-801.16 2674,-801.16 2674,-801.16 2674,-433.16 2674,-381.43 2674,-321.44 2674,-284.85"/>
<polygon fill="#05e505" stroke="black" points="2677.5,-284.89 2674,-274.89 2670.5,-284.89 2677.5,-284.89"/>
</g>
<!-- plugginger_testing_mock_app -->
<g id="node47" class="node">
<title>plugginger_testing_mock_app</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#2fbc2f" stroke="black" cx="1712" cy="-342.33" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1712" y="-351.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1712" y="-339.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">testing.</text>
<text text-anchor="middle" x="1712" y="-326.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">mock_app</text>
</g>
<!-- plugginger_config&#45;&gt;plugginger_testing_mock_app -->
<g id="edge70" class="edge">
<title>plugginger_config&#45;&gt;plugginger_testing_mock_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2484,-1179.43C2454.3,-1101.73 2484,-1075.46 2484,-992.28"/>
<path fill="none" stroke="black" d="M2484,-991.28C2486.38,-943.17 2446,-937.95 2446,-889.78 2446,-889.78 2446,-889.78 2446,-597.66 2446,-507.24 2437.94,-464.47 2365,-411.03 2317.82,-376.46 1926.43,-353.8 1773.19,-346.18"/>
<polygon fill="#05e505" stroke="black" points="1773.38,-342.68 1763.22,-345.69 1773.04,-349.68 1773.38,-342.68"/>
</g>
<!-- plugginger_config_models -->
<g id="node30" class="node">
<title>plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#05db05" stroke="black" cx="2482" cy="-1502.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2482" y="-1512.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2482" y="-1499.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config.</text>
<text text-anchor="middle" x="2482" y="-1486.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">models</text>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge71" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2432.67,-1498.9C2379.6,-1493.61 2295.14,-1478.3 2238,-1433.98 2204.19,-1407.75 2223.93,-1376.59 2190,-1350.54 1942.33,-1160.38 1802.47,-1294.49 1501,-1213.13 1430.78,-1194.18 1419.87,-1171.1 1351,-1147.72 1266,-1118.87 1164.5,-1095.75 1101.64,-1082.7"/>
<polygon fill="#05db05" stroke="black" points="1102.34,-1079.27 1091.84,-1080.69 1100.93,-1086.13 1102.34,-1079.27"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge72" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2485.91,-1469.9C2487.65,-1448.03 2488.13,-1418.5 2482,-1393.26"/>
<path fill="none" stroke="black" d="M2482,-1391.26C2477.04,-1370.84 2473.1,-1365.14 2458,-1350.54 2433.98,-1327.31 2418.88,-1334.39 2392,-1314.54 2202.72,-1174.73 2209.32,-1069.24 2004,-954.28 1798.24,-839.08 1709.35,-901.61 1496,-801.16"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_app -->
<g id="edge73" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2482,-1391.26C2476.52,-1370.98 2468.67,-1368.63 2458,-1350.54 2373.57,-1207.31 2355.69,-1169.54 2268,-1028.28 2224.69,-958.53 2075.63,-800.45 2066,-718.91"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_builder -->
<g id="edge74" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2437.65,-1487.72C2401.88,-1475.67 2350.82,-1456.64 2309,-1433.98 2281.63,-1419.14 2270.72,-1418.13 2252,-1393.26"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge75" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2636,-1280.83C2625.79,-1132.61 2712,-1103.86 2712,-955.28 2712,-955.28 2712,-955.28 2712,-716.91 2712,-654.81 2645.64,-661.01 2636,-599.66"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge76" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2636,-1391.26C2726.24,-1270.08 2750,-1222.09 2750,-1071 2750,-1071 2750,-1071 2750,-887.78 2750,-837.42 2762.77,-826.13 2774,-777.03 2779.93,-751.13 2784.99,-745.31 2788,-718.91"/>
<path fill="none" stroke="black" d="M2788,-716.91C2791.25,-664.9 2788,-651.77 2788,-599.66 2788,-599.66 2788,-599.66 2788,-433.16 2788,-377.23 2795.23,-356.03 2764,-309.63 2755.87,-297.54 2744.32,-287.37 2732.2,-279.06"/>
<polygon fill="#05db05" stroke="black" points="2734.45,-276.35 2724.14,-273.9 2730.67,-282.24 2734.45,-276.35"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_config -->
<g id="edge77" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2502.63,-1472.62C2513.26,-1457.66 2526.22,-1439.42 2537.03,-1424.2"/>
<polygon fill="#05db05" stroke="black" points="2539.63,-1426.58 2542.56,-1416.4 2533.92,-1422.53 2539.63,-1426.58"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_testing_mock_app -->
<g id="edge78" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_testing_mock_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2523.52,-1484.46C2557.83,-1469.59 2603.06,-1448.31 2617,-1433.98 2630.92,-1419.66 2624.07,-1409.27 2636,-1393.26"/>
<path fill="none" stroke="black" d="M2636,-1391.26C2650.39,-1371.93 2642.1,-1306.14 2636,-1282.83"/>
<path fill="none" stroke="black" d="M2636,-1280.83C2576.77,-1054.59 2522,-961.1 2522,-599.66 2522,-599.66 2522,-599.66 2522,-515.41 2522,-445.06 2467.55,-439 2403,-411.03 2290.47,-362.28 1920.24,-348.25 1773.09,-344.55"/>
<polygon fill="#05db05" stroke="black" points="1773.23,-341.06 1763.15,-344.31 1773.06,-348.05 1773.23,-341.06"/>
</g>
<!-- plugginger_core -->
<g id="node31" class="node">
<title>plugginger_core</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f910" stroke="black" cx="1450" cy="-1687.46" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="1450" y="-1690.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1450" y="-1677.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger -->
<g id="edge79" class="edge">
<title>plugginger_core&#45;&gt;plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1462.53,-1664.07C1478.28,-1635.12 1505.28,-1582.79 1522,-1535.38 1549.92,-1456.21 1568.98,-1358.53 1577.29,-1311.21"/>
<polygon fill="#10f910" stroke="black" points="1580.7,-1311.98 1578.95,-1301.53 1573.81,-1310.79 1580.7,-1311.98"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_graph -->
<g id="edge80" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1387,-1594.07C1377.67,-1569.83 1396.18,-1547.55 1417.38,-1531.18"/>
<polygon fill="#10f910" stroke="black" points="1419.28,-1534.12 1425.33,-1525.42 1415.18,-1528.45 1419.28,-1534.12"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_proxy -->
<g id="edge81" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1498.02,-1681.21C1545.68,-1673.96 1618.83,-1657.09 1670,-1618.76 1748.76,-1559.76 1763.69,-1528.23 1792,-1433.98 1820.79,-1338.12 1763.45,-1312.01 1748,-1213.13 1743.51,-1184.41 1747.21,-1176.78 1748,-1147.72 1751.11,-1032.96 1762,-1004.59 1762,-889.78 1762,-889.78 1762,-889.78 1762,-799.16 1762,-735.98 1767.61,-571.65 1800,-517.41"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge82" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1400.67,-1683.9C1294.84,-1677.3 1051.36,-1655.83 999,-1596.07"/>
<path fill="none" stroke="black" d="M999,-1594.07C966.79,-1557.31 942.02,-1568.42 906,-1535.38 864.29,-1497.13 847.05,-1487.61 829,-1433.98 816.82,-1397.78 820.41,-1353.94 825.79,-1322.81"/>
<polygon fill="#10f910" stroke="black" points="829.22,-1323.51 827.63,-1313.04 822.34,-1322.22 829.22,-1323.51"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge83" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M730,-1501.68C667.89,-1404.33 726.25,-1344.74 791,-1249.13 840.47,-1176.09 930.52,-1123.18 988.23,-1094.81"/>
<polygon fill="#10f910" stroke="black" points="989.42,-1098.12 996.9,-1090.62 986.38,-1091.82 989.42,-1098.12"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge84" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M999,-1594.07C973.17,-1567.16 958.95,-1566.9 939,-1535.38 921.44,-1507.64 909.31,-1472.84 901.51,-1444.62"/>
<polygon fill="#10f910" stroke="black" points="904.96,-1443.99 899.01,-1435.23 898.19,-1445.79 904.96,-1443.99"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge85" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M999,-1594.07C966.94,-1551.42 1082.24,-1556.87 1078,-1503.68"/>
<path fill="none" stroke="black" d="M1078,-1501.68C1076.69,-1485.3 1072.62,-1479.16 1059,-1469.98 973.2,-1412.15 907.53,-1501.34 829,-1433.98 786.72,-1397.71 801.38,-1369.27 791,-1314.54 761.19,-1157.31 806.93,-1110.25 767,-955.28"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge86" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M806,-1594.07C756.2,-1577.49 759.81,-1546.88 730,-1503.68"/>
<path fill="none" stroke="black" d="M730,-1501.68C702.37,-1461.64 671.71,-1473.96 644,-1433.98 612.27,-1388.19 614.55,-1369.58 606,-1314.54 601.54,-1285.81 602.12,-1277.94 606,-1249.13 612.9,-1197.98 680.79,-819.6 729,-801.16"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_validation -->
<g id="edge87" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1400.56,-1685.58C1290.75,-1682.33 1019.44,-1667.13 806,-1596.07"/>
<path fill="none" stroke="black" d="M806,-1594.07C778.16,-1584.8 771.68,-1581.13 744,-1571.38 655.48,-1540.21 613.27,-1566.99 544,-1503.68"/>
<path fill="none" stroke="black" d="M544,-1501.68C530.95,-1489.76 531.62,-1483.29 520,-1469.98 506,-1453.93 489.38,-1437.12 475.17,-1423.34"/>
<polygon fill="#10f910" stroke="black" points="477.84,-1421.06 468.21,-1416.66 472.99,-1426.11 477.84,-1421.06"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app -->
<g id="edge88" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1387,-1501.68C1390.16,-1485.56 1394.52,-1481.73 1406,-1469.98 1426.94,-1448.54 1445.96,-1458.62 1463,-1433.98 1510.71,-1365 1455.21,-1319.4 1501,-1249.13 1624,-1060.4 1800.8,-1150.05 1914,-955.28"/>
<path fill="none" stroke="black" d="M1914,-954.28C1930.79,-888.33 1914,-869.21 1914,-801.16 1914,-801.16 1914,-801.16 1914,-716.91 1914,-686.16 1899.07,-654.37 1885.37,-631.65"/>
<polygon fill="#10f910" stroke="black" points="1888.51,-630.08 1880.22,-623.48 1882.59,-633.81 1888.51,-630.08"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app_plugin -->
<g id="edge89" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1499.5,-1685.45C1586.98,-1681.82 1772.65,-1665.78 1910,-1596.07"/>
<path fill="none" stroke="black" d="M1910,-1594.07C2164.77,-1464.76 1838,-1240.99 1838,-955.28 1838,-955.28 1838,-955.28 1838,-887.78 1838,-678.03 1692.25,-455.65 1633.81,-374.91"/>
<polygon fill="#10f910" stroke="black" points="1636.76,-373.01 1628.03,-367 1631.11,-377.14 1636.76,-373.01"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_background -->
<g id="edge90" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1086,-1391.26C1087.53,-1361.43 1107.59,-1334.59 1127.48,-1315.08"/>
<polygon fill="#10f910" stroke="black" points="1129.63,-1317.86 1134.54,-1308.48 1124.85,-1312.75 1129.63,-1317.86"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_builder -->
<g id="edge91" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1910,-1594.07C1920.9,-1589.43 1915.17,-1579.29 1924,-1571.38 1951.85,-1546.45 1972.74,-1560.96 2000,-1535.38 2093.6,-1447.58 2200.84,-1395.83 2140,-1282.83"/>
<path fill="none" stroke="black" d="M2140,-1280.83C2124.39,-1251.7 2096.67,-1226.99 2072.71,-1209.49"/>
<polygon fill="#10f910" stroke="black" points="2074.82,-1206.7 2064.64,-1203.78 2070.78,-1212.41 2074.82,-1206.7"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_depends -->
<g id="edge92" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M544,-1501.68C423.23,-1391.31 595.16,-920.39 674,-777.03 793.28,-560.13 1137.21,-524.28 1263.11,-518.48"/>
<polygon fill="#10f910" stroke="black" points="1263.23,-521.98 1273.08,-518.07 1262.95,-514.98 1263.23,-521.98"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_events -->
<g id="edge93" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1086,-1391.26C1089.27,-1327.49 1075.14,-1304.46 1107,-1249.13 1116.28,-1233.02 1131.01,-1219.28 1145.62,-1208.47"/>
<polygon fill="#10f910" stroke="black" points="1147.24,-1211.61 1153.41,-1203 1143.22,-1205.88 1147.24,-1211.61"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_plugin -->
<g id="edge94" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M730,-1594.07C715.68,-1590.83 719.02,-1578.15 706,-1571.38 635.16,-1534.55 602.56,-1568.69 530,-1535.38 463.08,-1504.67 437.95,-1496.46 399,-1433.98 330.31,-1323.77 248.16,-981.16 293,-859.28 309.58,-814.23 331.27,-813.05 363,-777.03 395.7,-739.91 400.5,-727.06 438,-694.78 545.42,-602.33 577.53,-583.31 703,-517.41"/>
<path fill="none" stroke="black" d="M703,-515.41C830.99,-455.13 1272.75,-439.6 1417.6,-436.09"/>
<polygon fill="#10f910" stroke="black" points="1417.27,-439.6 1427.19,-435.86 1417.11,-432.6 1417.27,-439.6"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_service -->
<g id="edge95" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1078,-1501.68C1074.16,-1453.51 1082.9,-1441.48 1086,-1393.26"/>
<path fill="none" stroke="black" d="M1086,-1391.26C1088.03,-1359.56 1071.54,-1275.74 1060.49,-1224.14"/>
<polygon fill="#10f910" stroke="black" points="1063.94,-1223.54 1058.41,-1214.51 1057.1,-1225.02 1063.94,-1223.54"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_config_models -->
<g id="edge96" class="edge">
<title>plugginger_core&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1497.92,-1680.61C1583.65,-1669.91 1768.82,-1645.82 1924,-1618.76 2107.67,-1586.73 2323.37,-1539.48 2424.36,-1516.79"/>
<polygon fill="#10f910" stroke="black" points="2425.06,-1520.22 2434.04,-1514.61 2423.52,-1513.39 2425.06,-1520.22"/>
</g>
<!-- plugginger_implementations_container -->
<g id="node37" class="node">
<title>plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1050.12,-1525.8 947.88,-1525.8 947.88,-1479.55 1050.12,-1479.55 1050.12,-1525.8"/>
<text text-anchor="middle" x="999" y="-1512.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="999" y="-1499.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">implementations.</text>
<text text-anchor="middle" x="999" y="-1486.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">container</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_container -->
<g id="edge97" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M999,-1594.07C984.9,-1579.37 985.09,-1556.34 988.72,-1537.21"/>
<polygon fill="#10f910" stroke="black" points="992.08,-1538.2 990.9,-1527.67 985.26,-1536.64 992.08,-1538.2"/>
</g>
<!-- plugginger_implementations_events -->
<g id="node38" class="node">
<title>plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3a883a" stroke="black" cx="1658" cy="-1392.26" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="1658" y="-1401.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1658" y="-1389.13" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">implementations.</text>
<text text-anchor="middle" x="1658" y="-1376.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">events</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_events -->
<g id="edge98" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1499.15,-1683.37C1544.56,-1677.76 1610.35,-1662.12 1647,-1618.76 1680.46,-1579.17 1657.68,-1554.85 1666,-1503.68"/>
</g>
<!-- plugginger_implementations_services -->
<g id="node39" class="node">
<title>plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3d8f3d" stroke="black" cx="1382" cy="-1392.26" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="1382" y="-1401.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1382" y="-1389.13" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">implementations.</text>
<text text-anchor="middle" x="1382" y="-1376.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">services</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_services -->
<g id="edge99" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1387,-1594.07C1372.57,-1556.58 1379.28,-1543.1 1387,-1503.68"/>
<path fill="none" stroke="black" d="M1387,-1501.68C1391.15,-1480.5 1390.59,-1456.57 1388.7,-1436.59"/>
<polygon fill="#10f910" stroke="black" points="1392.19,-1436.4 1387.62,-1426.84 1385.24,-1437.16 1392.19,-1436.4"/>
</g>
<!-- plugginger_interfaces_events -->
<g id="node41" class="node">
<title>plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3ab03a" stroke="black" cx="1155" cy="-1502.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1155" y="-1512.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1155" y="-1499.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">interfaces.</text>
<text text-anchor="middle" x="1155" y="-1486.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">events</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_interfaces_events -->
<g id="edge100" class="edge">
<title>plugginger_core&#45;&gt;plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M999,-1594.07C994.58,-1589.47 1059.06,-1554.07 1106.48,-1528.96"/>
<polygon fill="#10f910" stroke="black" points="1108.06,-1532.09 1115.27,-1524.32 1104.79,-1525.89 1108.06,-1532.09"/>
</g>
<!-- plugginger_interfaces_services -->
<g id="node42" class="node">
<title>plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3ab03a" stroke="black" cx="1272" cy="-1502.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1272" y="-1512.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1272" y="-1499.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">interfaces.</text>
<text text-anchor="middle" x="1272" y="-1486.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">services</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_interfaces_services -->
<g id="edge101" class="edge">
<title>plugginger_core&#45;&gt;plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1430.66,-1665.17C1416.11,-1647.87 1397.03,-1622.14 1387,-1596.07"/>
<path fill="none" stroke="black" d="M1387,-1594.07C1380.58,-1577.39 1346.45,-1551.72 1317.02,-1532"/>
<polygon fill="#10f910" stroke="black" points="1319.21,-1529.25 1308.94,-1526.65 1315.35,-1535.09 1319.21,-1529.25"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_stubgen -->
<g id="edge102" class="edge">
<title>plugginger_core&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1400.74,-1684.31C1282.16,-1678.62 971.74,-1660.63 716,-1618.76 676.33,-1612.27 397.11,-1558.18 364,-1535.38 319.1,-1504.47 323.79,-1480.27 295,-1433.98 277.97,-1406.59 152,-1213.67 152,-1181.43 152,-1181.43 152,-1181.43 152,-991.28 152,-905.11 114,-887.33 114,-801.16 114,-801.16 114,-801.16 114,-716.91 114,-618.44 393.69,-511.41 456,-435.16"/>
<path fill="none" stroke="black" d="M456,-433.16C487.31,-375.89 533.97,-315.76 562.61,-280.9"/>
<polygon fill="#10f910" stroke="black" points="565.19,-283.28 568.87,-273.34 559.8,-278.81 565.19,-283.28"/>
</g>
<!-- plugginger_testing_collectors -->
<g id="node45" class="node">
<title>plugginger_testing_collectors</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#2fbc2f" stroke="black" cx="409" cy="-342.33" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="409" y="-351.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="409" y="-339.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">testing.</text>
<text text-anchor="middle" x="409" y="-326.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">collectors</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_testing_collectors -->
<g id="edge103" class="edge">
<title>plugginger_core&#45;&gt;plugginger_testing_collectors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1400.2,-1685.54C1289.46,-1682.57 1014.96,-1670.18 792,-1618.76 763.41,-1612.16 758.66,-1602.34 730,-1596.07"/>
<path fill="none" stroke="black" d="M730,-1594.07C715.66,-1590.93 719.27,-1577.65 706,-1571.38 590.57,-1516.83 528.95,-1603.55 421,-1535.38 366.56,-1501.01 190,-1162.23 190,-1071 190,-1071 190,-1071 190,-887.78 190,-758.62 114.43,-723.24 152,-599.66"/>
</g>
<!-- plugginger_core_config -->
<g id="node32" class="node">
<title>plugginger_core_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f910" stroke="black" cx="2063" cy="-1687.46" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2063" y="-1697.09" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2063" y="-1684.34" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="2063" y="-1671.59" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger -->
<g id="edge104" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2067.39,-1654.67C2071.91,-1609.85 2073.94,-1527.16 2038,-1469.98 1947.84,-1326.52 1724.38,-1293.02 1627.74,-1285.21"/>
<polygon fill="#10f910" stroke="black" points="1628.07,-1281.72 1617.84,-1284.47 1627.55,-1288.7 1628.07,-1281.72"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge105" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2025.49,-1665.67C2018.4,-1661.92 2011.02,-1658.14 2004,-1654.76 1967.41,-1637.13 1956.28,-1637.02 1920,-1618.76 1854.29,-1585.68 1833.07,-1583.02 1777,-1535.38 1748.27,-1510.98 1756.72,-1486.83 1723,-1469.98 1569.39,-1393.17 1114.15,-1487.61 951,-1433.98 945.97,-1432.32 940.97,-1430.13 936.12,-1427.63"/>
<polygon fill="#10f910" stroke="black" points="938.14,-1424.75 927.72,-1422.85 934.68,-1430.83 938.14,-1424.75"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge106" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2107.03,-1671.78C2134.04,-1660.84 2167.62,-1643.49 2190,-1618.76 2273.14,-1526.89 2287.03,-1472.64 2266,-1350.54 2257.88,-1303.41 2260.01,-1286.37 2230,-1249.13 2210.96,-1225.5 2196.27,-1231.33 2172,-1213.13 2073.58,-1139.33 2071.28,-1091.9 1966,-1028.28 1845.88,-955.7 1794.86,-982.36 1670,-918.28 1587.06,-875.72 1579.31,-843 1496,-801.16"/>
<path fill="none" stroke="black" d="M1496,-799.16C1468,-785.74 1439.6,-765.3 1418.41,-748.41"/>
<polygon fill="#10f910" stroke="black" points="1420.64,-745.71 1410.67,-742.13 1416.23,-751.15 1420.64,-745.71"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_api_app -->
<g id="edge107" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2559,-1594.07C2585.73,-1544.66 2549.71,-1515.22 2583,-1469.98 2603.04,-1442.75 2632.65,-1462.99 2650,-1433.98 2692.16,-1363.46 2671.62,-1328.39 2650,-1249.13 2614.99,-1120.79 2585.59,-1091.4 2498,-991.28 2407.94,-888.34 2256,-937.94 2256,-801.16 2256,-801.16 2256,-801.16 2256,-716.91 2256,-645.35 2013.72,-613.88 1909.62,-603.66"/>
<polygon fill="#10f910" stroke="black" points="1910.02,-600.18 1899.73,-602.72 1909.35,-607.15 1910.02,-600.18"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_config_models -->
<g id="edge108" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2112.97,-1686.49C2230.32,-1685.24 2516.78,-1674.11 2559,-1596.07"/>
<path fill="none" stroke="black" d="M2559,-1594.07C2571.77,-1570.46 2551.63,-1547.48 2528.75,-1530.6"/>
<polygon fill="#10f910" stroke="black" points="2531.02,-1527.91 2520.81,-1525.08 2527.02,-1533.66 2531.02,-1527.91"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_implementations_events -->
<g id="edge109" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2033.61,-1660.65C2005.95,-1636.71 1963.05,-1600.51 1924,-1571.38 1851.29,-1517.16 1763.18,-1459.68 1708.86,-1425.13"/>
<polygon fill="#10f910" stroke="black" points="1711.04,-1422.37 1700.72,-1419.97 1707.29,-1428.28 1711.04,-1422.37"/>
</g>
<!-- plugginger_core_constants -->
<g id="node33" class="node">
<title>plugginger_core_constants</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f910" stroke="black" cx="1009" cy="-1392.26" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1009" y="-1401.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1009" y="-1389.13" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1009" y="-1376.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">constants</text>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge110" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M974,-1280.83C956.75,-1223.62 972.22,-1203.75 993,-1147.72 997.12,-1136.6 1003.04,-1125.41 1009.34,-1115.15"/>
<polygon fill="#10f910" stroke="black" points="1012.18,-1117.2 1014.64,-1106.9 1006.29,-1113.42 1012.18,-1117.2"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_builder -->
<g id="edge111" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1050,-1280.83C1072.22,-1262.22 1079.15,-1257.16 1107,-1249.13 1250.06,-1207.89 1627.71,-1226.43 1776,-1213.13 1847.91,-1206.68 1930.93,-1195.51 1981.43,-1188.3"/>
<polygon fill="#10f910" stroke="black" points="1981.72,-1191.79 1991.11,-1186.9 1980.72,-1184.86 1981.72,-1191.79"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_events -->
<g id="edge112" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1012.63,-1359.36C1017.17,-1334.76 1027.32,-1301.83 1050,-1282.83"/>
<path fill="none" stroke="black" d="M1050,-1280.83C1063.55,-1269.49 1060.74,-1260.81 1074,-1249.13 1093.97,-1231.55 1119.22,-1216.33 1141.23,-1204.8"/>
<polygon fill="#10f910" stroke="black" points="1142.74,-1207.95 1150.06,-1200.29 1139.56,-1201.72 1142.74,-1207.95"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_plugin -->
<g id="edge113" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M998.38,-1360.1C991.16,-1338.51 981.58,-1309.06 974,-1282.83"/>
<path fill="none" stroke="black" d="M974,-1280.83C961.86,-1238.83 773.03,-994.14 753,-955.28 724.33,-899.66 715.16,-884.86 704,-823.28 691.87,-756.4 644.57,-552.14 703,-517.41"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_service -->
<g id="edge114" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M974,-1280.83C965.86,-1253.83 985.55,-1228.76 1007.05,-1210.56"/>
<polygon fill="#10f910" stroke="black" points="1009.16,-1213.35 1014.79,-1204.38 1004.79,-1207.88 1009.16,-1213.35"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_stubgen -->
<g id="edge115" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M986.81,-1362.7C962.39,-1332.38 921.02,-1284.11 879,-1249.13 850.05,-1225.03 304,-927.45 304,-889.78 304,-889.78 304,-889.78 304,-799.16 304,-745.2 325,-729.86 366,-694.78 398.74,-666.78 447.55,-701.03 456,-658.78"/>
<path fill="none" stroke="black" d="M456,-657.78C460.5,-558.94 394.84,-512.94 456,-435.16"/>
</g>
<!-- plugginger_core_exceptions -->
<g id="node34" class="node">
<title>plugginger_core_exceptions</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f910" stroke="black" cx="1946" cy="-1687.46" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1946" y="-1697.09" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1946" y="-1684.34" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1946" y="-1671.59" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">exceptions</text>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger -->
<g id="edge116" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1937.09,-1655.03C1915.23,-1580 1857.33,-1393.53 1810,-1350.54 1796.3,-1338.09 1689.9,-1309.86 1627.41,-1294.06"/>
<polygon fill="#10f910" stroke="black" points="1628.3,-1290.68 1617.75,-1291.63 1626.59,-1297.47 1628.3,-1290.68"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_graph -->
<g id="edge117" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1471,-1594.07C1458.68,-1581.97 1455.64,-1563.65 1456.18,-1546.65"/>
<polygon fill="#10f910" stroke="black" points="1459.67,-1546.94 1456.94,-1536.7 1452.69,-1546.4 1459.67,-1546.94"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_proxy -->
<g id="edge118" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1982.47,-1665.05C1989.78,-1661.28 1997.51,-1657.65 2005,-1654.76 2066.95,-1630.86 2089.3,-1645.68 2150,-1618.76 2225.27,-1585.37 2449.64,-1475.4 2444,-1393.26"/>
<path fill="none" stroke="black" d="M2444,-1391.26C2448.28,-1350.29 2410.59,-1351.3 2392,-1314.54 2265.32,-1064.01 2408.03,-939.24 2270,-694.78 2210.57,-589.53 2093.04,-503.79 2028.7,-462.26"/>
<polygon fill="#10f910" stroke="black" points="2030.81,-459.45 2020.5,-457.03 2027.05,-465.36 2030.81,-459.45"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge119" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1709,-1501.68C1708.85,-1468.77 1732.19,-1466.17 1739,-1433.98 1746.68,-1397.7 1764.47,-1377.49 1739,-1350.54 1690.7,-1299.41 1177.07,-1320.61 1107,-1314.54 1027.57,-1307.65 935.49,-1296.18 881.41,-1289.09"/>
<polygon fill="#10f910" stroke="black" points="882.01,-1285.64 871.64,-1287.8 881.1,-1292.58 882.01,-1285.64"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge120" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1471,-1594.07C1434.76,-1558.46 1557.37,-1551.78 1541,-1503.68"/>
<path fill="none" stroke="black" d="M1541,-1501.68C1535.71,-1486.13 1536.03,-1478.52 1522,-1469.98 1413.41,-1403.85 1071.45,-1474.67 951,-1433.98 945.99,-1432.28 940.99,-1430.06 936.14,-1427.54"/>
<polygon fill="#10f910" stroke="black" points="938.17,-1424.67 927.75,-1422.74 934.7,-1430.74 938.17,-1424.67"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge121" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2024,-1501.68C2021.19,-1431.77 2029.03,-1400.44 1980,-1350.54 1942.17,-1312.03 1912.38,-1338.48 1864,-1314.54 1819.03,-1292.29 1819.1,-1266.41 1772,-1249.13 1595.7,-1184.44 1528.66,-1273.98 1351,-1213.13 1300.41,-1195.8 1300.15,-1166.29 1250,-1147.72 1138.12,-1106.3 1094.48,-1156.74 984,-1111.72 900.91,-1077.87 895.57,-1042.64 822,-991.28 798.04,-974.56 774.71,-983.46 767,-955.28"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_validation -->
<g id="edge122" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1896.35,-1683.82C1787.11,-1676.98 1530.88,-1654.9 1471,-1596.07"/>
<path fill="none" stroke="black" d="M1471,-1594.07C1467.28,-1590.41 1334.84,-1471.93 1330,-1469.98 1172.85,-1406.39 734.75,-1464.5 568,-1433.98 541.25,-1429.08 512.33,-1419.92 489.05,-1411.53"/>
<polygon fill="#10f910" stroke="black" points="490.46,-1408.32 479.86,-1408.14 488.04,-1414.89 490.46,-1408.32"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_app -->
<g id="edge123" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1982.97,-1665.33C1990.17,-1661.58 1997.74,-1657.88 2005,-1654.76 2051.87,-1634.61 2076.02,-1652.82 2114,-1618.76 2191.38,-1549.36 2208.64,-1491.94 2176,-1393.26"/>
<path fill="none" stroke="black" d="M2176,-1391.26C2134.6,-1282.47 2052.05,-1307.57 1984,-1213.13 1914.58,-1116.79 1857.33,-1059.63 1914,-955.28"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin -->
<g id="edge124" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2444,-1391.26C2508.49,-1068.3 2332,-988.11 2332,-658.78 2332,-658.78 2332,-658.78 2332,-597.66 2332,-561.3 2378.01,-459.5 2351,-435.16"/>
<path fill="none" stroke="black" d="M2351,-433.16C2230.36,-335.17 1799.87,-428.7 1654,-375.03 1651.47,-374.1 1648.95,-372.98 1646.47,-371.72"/>
<polygon fill="#10f910" stroke="black" points="1648.31,-368.75 1637.92,-366.7 1644.77,-374.78 1648.31,-368.75"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_background -->
<g id="edge125" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1709,-1594.07C1701.48,-1576.5 1768.44,-1452.76 1772,-1433.98 1778.91,-1397.54 1796.72,-1378.18 1772,-1350.54 1754.08,-1330.5 1381.84,-1299.45 1230.83,-1287.63"/>
<polygon fill="#10f910" stroke="black" points="1231.24,-1284.15 1221,-1286.86 1230.69,-1291.13 1231.24,-1284.15"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_builder -->
<g id="edge126" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2176,-1391.26C2168.03,-1367.15 2152.03,-1305.19 2140,-1282.83"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_depends -->
<g id="edge127" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1980.64,-1663.55C1995.61,-1651.89 2011.97,-1636.43 2022,-1618.76 2054.83,-1560.92 2056.02,-1534 2038,-1469.98 2021.15,-1410.09 2005.2,-1398.01 1965,-1350.54 1920.53,-1298.02 1901.05,-1292.96 1848,-1249.13 1810.5,-1218.15 1789.14,-1221.8 1762,-1181.43"/>
<path fill="none" stroke="black" d="M1762,-1179.43C1752.66,-1166.19 1754.77,-1159.83 1744,-1147.72 1629.32,-1018.88 1537.74,-1053.77 1431,-918.28 1396.08,-873.96 1397.17,-855.51 1382,-801.16"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_events -->
<g id="edge128" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1969.54,-1658.33C1995.88,-1623.51 2033.7,-1561.7 2024,-1503.68"/>
<path fill="none" stroke="black" d="M2024,-1501.68C2017.74,-1421.38 1989.68,-1397.16 1924,-1350.54 1870.91,-1312.85 1835.99,-1350.92 1782,-1314.54 1752.1,-1294.39 1765.42,-1266.81 1734,-1249.13 1654.1,-1204.17 1413.48,-1227.95 1323,-1213.13 1298.29,-1209.08 1271.36,-1202.88 1248.37,-1197.03"/>
<polygon fill="#10f910" stroke="black" points="1249.45,-1193.7 1238.89,-1194.58 1247.7,-1200.47 1249.45,-1193.7"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_plugin -->
<g id="edge129" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1762,-1179.43C1738.1,-1151.21 1683.23,-894.18 1671,-859.28 1644.78,-784.46 1635.97,-766.42 1602,-694.78 1562.46,-611.42 1509.97,-516.36 1482.52,-467.67"/>
<polygon fill="#10f910" stroke="black" points="1485.61,-466.02 1477.64,-459.04 1479.51,-469.47 1485.61,-466.02"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_service -->
<g id="edge130" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1947.09,-1654.4C1947.5,-1587.09 1938.2,-1430.65 1849,-1350.54 1809.15,-1314.75 1782.86,-1336.47 1734,-1314.54 1682.46,-1291.4 1678.86,-1266.19 1625,-1249.13 1416.41,-1183.04 1347.58,-1260.67 1134,-1213.13 1123.5,-1210.79 1112.55,-1207.37 1102.19,-1203.64"/>
<polygon fill="#10f910" stroke="black" points="1103.65,-1200.45 1093.06,-1200.19 1101.18,-1207 1103.65,-1200.45"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_container -->
<g id="edge131" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1896.83,-1683.61C1760.84,-1674.55 1369.55,-1640.53 1064,-1535.38 1059.91,-1533.98 1055.74,-1532.37 1051.59,-1530.65"/>
<polygon fill="#10f910" stroke="black" points="1053.33,-1527.59 1042.76,-1526.79 1050.52,-1534 1053.33,-1527.59"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_events -->
<g id="edge132" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1896.08,-1686.16C1835.83,-1682.62 1738.85,-1665.8 1709,-1596.07"/>
<path fill="none" stroke="black" d="M1709,-1594.07C1693.19,-1557.14 1709.18,-1543.85 1709,-1503.68"/>
<path fill="none" stroke="black" d="M1709,-1501.68C1708.89,-1477.59 1698.44,-1453.1 1687.03,-1433.51"/>
<polygon fill="#10f910" stroke="black" points="1690.04,-1431.72 1681.82,-1425.03 1684.07,-1435.39 1690.04,-1431.72"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_services -->
<g id="edge133" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1541,-1501.68C1527.07,-1460.77 1486.63,-1433.52 1450.02,-1416.57"/>
<polygon fill="#10f910" stroke="black" points="1451.77,-1413.52 1441.21,-1412.68 1448.94,-1419.92 1451.77,-1413.52"/>
</g>
<!-- plugginger_core_types -->
<g id="node35" class="node">
<title>plugginger_core_types</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f910" stroke="black" cx="730" cy="-1687.46" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="730" y="-1697.09" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="730" y="-1684.34" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="730" y="-1671.59" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">types</text>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge134" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M707.94,-1658.02C697.06,-1640.64 687.18,-1617.67 692,-1596.07"/>
<path fill="none" stroke="black" d="M692,-1594.07C716.41,-1484.62 695.25,-1443.48 758,-1350.54 766.9,-1337.36 778.96,-1325.14 790.89,-1314.8"/>
<polygon fill="#10f910" stroke="black" points="792.91,-1317.68 798.35,-1308.6 788.43,-1312.3 792.91,-1317.68"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge135" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M468,-1594.07C400.27,-1542.92 569.12,-1306.14 632,-1249.13 735.72,-1155.08 896.86,-1105.13 982.99,-1083.83"/>
<polygon fill="#10f910" stroke="black" points="983.76,-1087.24 992.65,-1081.48 982.11,-1080.44 983.76,-1087.24"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge136" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M692,-1594.07C711.72,-1516.74 786.6,-1456.48 838.4,-1422.89"/>
<polygon fill="#10f910" stroke="black" points="840.26,-1425.86 846.83,-1417.55 836.51,-1419.95 840.26,-1425.86"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge137" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M468,-1594.07C266.94,-1442.92 272.84,-1202.79 454,-1028.28 556.88,-929.19 701.27,-1082.11 767,-955.28"/>
<path fill="none" stroke="black" d="M767,-954.28C764.85,-945.95 765.86,-937.24 768.43,-929"/>
<polygon fill="#10f910" stroke="black" points="771.6,-930.52 772.02,-919.93 765.09,-927.95 771.6,-930.52"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge138" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M228,-1391.26C209.55,-1305.78 323.98,-1095.44 380,-1028.28 498.54,-886.17 554.57,-862.98 729,-801.16"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_app_plugin -->
<g id="edge139" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M190,-1594.07C184.29,-1588.62 138.73,-1322.4 138,-1314.54 126.32,-1187.85 107.12,-1151.7 138,-1028.28 178.77,-865.29 203.36,-819.44 316,-694.78 423.68,-575.61 465.76,-550.09 616,-493.28 990.89,-351.54 1118.8,-462.21 1510,-375.03 1527.94,-371.04 1547.27,-365.27 1564.11,-359.74"/>
<polygon fill="#10f910" stroke="black" points="1564.97,-363.14 1573.34,-356.65 1562.75,-356.5 1564.97,-363.14"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_depends -->
<g id="edge140" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M681.77,-1679.7C631.73,-1671.14 552.49,-1653.17 492,-1618.76 479.24,-1611.5 479.52,-1605.17 468,-1596.07"/>
<path fill="none" stroke="black" d="M468,-1594.07C406.61,-1545.54 361.07,-1586.7 302,-1535.38 248.24,-1488.68 246.3,-1462.08 228,-1393.26"/>
<path fill="none" stroke="black" d="M228,-1391.26C156.79,-1116.36 223.91,-996.6 404,-777.03 458.25,-710.89 650.95,-602.89 732,-575.53 830.37,-542.33 1144.39,-524.94 1263.55,-519.4"/>
<polygon fill="#10f910" stroke="black" points="1263.44,-522.91 1273.27,-518.96 1263.12,-515.92 1263.44,-522.91"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_events -->
<g id="edge141" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M582,-1391.26C586.57,-1385.93 784.42,-1251.57 791,-1249.13 924.37,-1199.69 970.8,-1246.77 1109,-1213.13 1119.46,-1210.58 1130.38,-1207.07 1140.73,-1203.31"/>
<polygon fill="#10f910" stroke="black" points="1141.74,-1206.67 1149.86,-1199.86 1139.27,-1200.12 1141.74,-1206.67"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_service -->
<g id="edge142" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M468,-1594.07C448.67,-1577.2 574.38,-1417.76 582,-1393.26"/>
<path fill="none" stroke="black" d="M582,-1391.26C630.93,-1318.06 642.77,-1291.41 720,-1249.13 806.02,-1202.03 920.86,-1187.58 990.19,-1183.21"/>
<polygon fill="#10f910" stroke="black" points="990.04,-1186.73 999.82,-1182.66 989.64,-1179.74 990.04,-1186.73"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_container -->
<g id="edge143" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M739.1,-1654.9C748.26,-1628.71 764.98,-1592.46 792,-1571.38 843.34,-1531.34 872.86,-1557.77 934,-1535.38 937.82,-1533.98 941.72,-1532.43 945.6,-1530.79"/>
<polygon fill="#10f910" stroke="black" points="946.72,-1534.12 954.45,-1526.87 943.89,-1527.72 946.72,-1534.12"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_events -->
<g id="edge144" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M692,-1594.07C704.3,-1542.54 770.95,-1574.04 851,-1535.38 894.88,-1514.19 893.29,-1486.86 939,-1469.98 1048.49,-1429.53 1347.38,-1449.94 1463,-1433.98 1503.36,-1428.4 1547.87,-1419.33 1584.17,-1411.17"/>
<polygon fill="#10f910" stroke="black" points="1584.7,-1414.64 1593.68,-1409 1583.15,-1407.81 1584.7,-1414.64"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_services -->
<g id="edge145" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M692,-1594.07C695.47,-1579.81 704.09,-1579.96 716,-1571.38 793.69,-1515.47 814.43,-1497.84 906,-1469.98 1058.56,-1423.55 1107.2,-1468.01 1263,-1433.98 1281.02,-1430.04 1300.1,-1424.27 1317.59,-1418.3"/>
<polygon fill="#10f910" stroke="black" points="1318.53,-1421.68 1326.81,-1415.07 1316.22,-1415.07 1318.53,-1421.68"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_interfaces_events -->
<g id="edge146" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M769.95,-1667.76C819.94,-1644.52 908.33,-1603.93 985,-1571.38 1023.92,-1554.86 1068.32,-1537.22 1101.99,-1524.09"/>
<polygon fill="#10f910" stroke="black" points="1103.06,-1527.42 1111.11,-1520.54 1100.52,-1520.9 1103.06,-1527.42"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_interfaces_services -->
<g id="edge147" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M776.31,-1675.12C863.56,-1653.06 1057.5,-1600.78 1213,-1535.38 1216.99,-1533.71 1221.06,-1531.85 1225.11,-1529.9"/>
<polygon fill="#10f910" stroke="black" points="1226.35,-1533.19 1233.72,-1525.58 1223.21,-1526.93 1226.35,-1533.19"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_testing_collectors -->
<g id="edge148" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_testing_collectors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M681.08,-1681.02C555.38,-1666.65 228.26,-1626.7 190,-1596.07"/>
<path fill="none" stroke="black" d="M190,-1594.07C170.93,-1578.8 38,-1205.86 38,-1181.43 38,-1181.43 38,-1181.43 38,-991.28 38,-840.52 33.5,-793.09 100,-657.78 115.29,-626.68 141.44,-632.67 152,-599.66"/>
<path fill="none" stroke="black" d="M152,-597.66C161.72,-551.06 148.64,-532.23 176,-493.28 220.72,-429.63 301.92,-386.56 355.67,-363.46"/>
<polygon fill="#10f910" stroke="black" points="356.77,-366.8 364.64,-359.7 354.06,-360.34 356.77,-366.8"/>
</g>
<!-- plugginger_implementations -->
<g id="node36" class="node">
<title>plugginger_implementations</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3bce3b" stroke="black" cx="1184" cy="-1392.26" rx="70.18" ry="23.69"/>
<text text-anchor="middle" x="1184" y="-1395.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1184" y="-1382.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">implementations</text>
</g>
<!-- plugginger_implementations&#45;&gt;plugginger -->
<g id="edge149" class="edge">
<title>plugginger_implementations&#45;&gt;plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1219.51,-1371.46C1234.16,-1363.99 1251.54,-1355.98 1268,-1350.54 1349.6,-1323.56 1374.1,-1333.19 1458,-1314.54 1484.19,-1308.71 1513.26,-1301.33 1536.67,-1295.16"/>
<polygon fill="#3bce3b" stroke="black" points="1537.44,-1298.57 1546.2,-1292.62 1535.64,-1291.81 1537.44,-1298.57"/>
</g>
<!-- plugginger_implementations&#45;&gt;plugginger_api_depends -->
<g id="edge150" class="edge">
<title>plugginger_implementations&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1219.88,-1371.6C1291.2,-1331.31 1444,-1238.34 1444,-1181.43 1444,-1181.43 1444,-1181.43 1444,-1069 1444,-993.98 1385.36,-991.27 1368,-918.28 1361.93,-892.77 1364.91,-885.32 1368,-859.28 1371.13,-832.9 1386.15,-827.4 1382,-801.16"/>
<path fill="none" stroke="black" d="M1382,-799.16C1371.16,-765.92 1343,-773.07 1329,-741.03 1301.57,-678.27 1302.85,-596.26 1306.35,-551.03"/>
<polygon fill="#3bce3b" stroke="black" points="1309.81,-551.59 1307.19,-541.33 1302.84,-550.99 1309.81,-551.59"/>
</g>
<!-- plugginger_implementations_container&#45;&gt;plugginger -->
<g id="edge151" class="edge">
<title>plugginger_implementations_container&#45;&gt;plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1040.96,-1479.15C1048.48,-1475.71 1056.36,-1472.48 1064,-1469.98 1149.42,-1442.01 1192.06,-1489.17 1263,-1433.98 1295.16,-1408.95 1269.43,-1376.3 1301,-1350.54 1356.47,-1305.28 1388.43,-1331.4 1458,-1314.54 1484.17,-1308.19 1513.34,-1300.75 1536.82,-1294.67"/>
<polygon fill="blue" stroke="black" points="1537.58,-1298.09 1546.38,-1292.19 1535.82,-1291.32 1537.58,-1298.09"/>
</g>
<!-- plugginger_implementations_container&#45;&gt;plugginger_api_depends -->
<g id="edge152" class="edge">
<title>plugginger_implementations_container&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M947.42,-1498.75C900.05,-1493.24 831.29,-1477.67 791,-1433.98 614.17,-1242.23 623.89,-1115.64 672,-859.28 679.24,-820.73 676.74,-805.23 704,-777.03 745.93,-733.66 1129.79,-585.65 1263.96,-534.76"/>
<polygon fill="blue" stroke="black" points="1265.15,-538.05 1273.26,-531.24 1262.67,-531.51 1265.15,-538.05"/>
</g>
<!-- plugginger_interfaces -->
<g id="node40" class="node">
<title>plugginger_interfaces</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3bce3b" stroke="black" cx="1589" cy="-1595.07" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="1589" y="-1598.32" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1589" y="-1585.57" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces</text>
</g>
<!-- plugginger_interfaces&#45;&gt;plugginger_implementations_events -->
<g id="edge153" class="edge">
<title>plugginger_interfaces&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1616.34,-1575.13C1636.47,-1558.95 1661.13,-1533.59 1666,-1503.68"/>
<path fill="none" stroke="black" d="M1666,-1501.68C1669.47,-1480.34 1668.23,-1456.4 1665.81,-1436.45"/>
<polygon fill="#3bce3b" stroke="black" points="1669.3,-1436.15 1664.48,-1426.71 1662.37,-1437.1 1669.3,-1436.15"/>
</g>
<!-- plugginger_interfaces&#45;&gt;plugginger_implementations_services -->
<g id="edge154" class="edge">
<title>plugginger_interfaces&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1588.45,-1571.21C1586.63,-1544.04 1579.77,-1498.76 1555,-1469.98 1529.96,-1440.87 1491.74,-1422.35 1457.58,-1410.82"/>
<polygon fill="#3bce3b" stroke="black" points="1458.69,-1407.5 1448.1,-1407.78 1456.55,-1414.16 1458.69,-1407.5"/>
</g>
<!-- plugginger_interfaces_events&#45;&gt;plugginger_implementations_events -->
<g id="edge155" class="edge">
<title>plugginger_interfaces_events&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1190.94,-1479.83C1198.35,-1476.05 1206.27,-1472.52 1214,-1469.98 1320.2,-1434.98 1352.89,-1453.41 1463,-1433.98 1502.66,-1426.97 1546.59,-1417.92 1582.7,-1410.12"/>
<polygon fill="#3ab03a" stroke="black" points="1583.13,-1413.61 1592.16,-1408.07 1581.64,-1406.77 1583.13,-1413.61"/>
</g>
<!-- plugginger_interfaces_services&#45;&gt;plugginger_implementations_services -->
<g id="edge156" class="edge">
<title>plugginger_interfaces_services&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1299.19,-1474.88C1312.83,-1461.44 1329.56,-1444.95 1344.4,-1430.32"/>
<polygon fill="#3ab03a" stroke="black" points="1346.68,-1432.99 1351.34,-1423.47 1341.76,-1428 1346.68,-1432.99"/>
</g>
<!-- plugginger_stubgen&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge157" class="edge">
<title>plugginger_stubgen&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M605.43,-227.63C619.94,-211 641.9,-188.27 665,-173.25"/>
<path fill="none" stroke="black" d="M665,-171.25C739.37,-122.89 2085.28,-101.5 2409.96,-97.03"/>
<polygon fill="#35a135" stroke="black" points="2409.61,-100.54 2419.56,-96.9 2409.51,-93.54 2409.61,-100.54"/>
</g>
<!-- plugginger_testing -->
<g id="node44" class="node">
<title>plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1531,-190.25 1461,-190.25 1461,-154.25 1531,-154.25 1531,-190.25"/>
<text text-anchor="middle" x="1496" y="-175.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1496" y="-162.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">testing</text>
</g>
<!-- plugginger_testing_collectors&#45;&gt;plugginger_testing -->
<g id="edge158" class="edge">
<title>plugginger_testing_collectors&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M429.72,-312.15C451.25,-284.49 487.68,-244.52 530,-226.25 614.88,-189.61 1268.25,-176.79 1449.51,-173.92"/>
<polygon fill="#2fbc2f" stroke="black" points="1449.29,-177.42 1459.24,-173.77 1449.18,-170.43 1449.29,-177.42"/>
</g>
<!-- plugginger_testing_collectors&#45;&gt;plugginger_testing_helpers -->
<g id="edge159" class="edge">
<title>plugginger_testing_collectors&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M457.11,-334.15C505.87,-327.08 583.54,-316.38 651,-309.63 954.65,-279.22 1321.27,-259.54 1449.24,-253.18"/>
<polygon fill="#2fbc2f" stroke="black" points="1449.19,-256.69 1459,-252.7 1448.84,-249.7 1449.19,-256.69"/>
</g>
<!-- plugginger_testing_helpers&#45;&gt;plugginger_testing -->
<g id="edge160" class="edge">
<title>plugginger_testing_helpers&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1496,-226.64C1496,-218.9 1496,-210.12 1496,-201.94"/>
<polygon fill="blue" stroke="black" points="1499.5,-202.1 1496,-192.1 1492.5,-202.1 1499.5,-202.1"/>
</g>
<!-- plugginger_testing_mock_app&#45;&gt;plugginger_testing -->
<g id="edge161" class="edge">
<title>plugginger_testing_mock_app&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1680.59,-316.89C1639.56,-284.96 1568.14,-229.38 1527.21,-197.53"/>
<polygon fill="#2fbc2f" stroke="black" points="1529.46,-194.85 1519.42,-191.47 1525.16,-200.38 1529.46,-194.85"/>
</g>
<!-- plugginger_testing_mock_app&#45;&gt;plugginger_testing_helpers -->
<g id="edge162" class="edge">
<title>plugginger_testing_mock_app&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1674.96,-320.06C1668.08,-316.4 1660.89,-312.77 1654,-309.63 1617.21,-292.84 1574.09,-277.08 1542.2,-266.12"/>
<polygon fill="#2fbc2f" stroke="black" points="1543.45,-262.85 1532.85,-262.94 1541.19,-269.47 1543.45,-262.85"/>
</g>
<!-- pydantic -->
<g id="node48" class="node">
<title>pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#0606f9" stroke="black" cx="1514" cy="-1779.85" rx="35.49" ry="18"/>
<text text-anchor="middle" x="1514" y="-1776.73" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">pydantic</text>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge163" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M152,-1594.07C99.44,-1328.1 174.35,-1204.94 380,-1028.28 436.45,-979.79 650.47,-923.81 745.76,-900.66"/>
<polygon fill="#0606f9" stroke="black" points="746.4,-904.11 755.3,-898.36 744.76,-897.3 746.4,-904.11"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge164" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M76,-1391.26C26.89,-1097.64 224.32,-1006.62 483,-859.28 580.62,-803.68 621.87,-834.99 729,-801.16"/>
<path fill="none" stroke="black" d="M729,-799.16C741.41,-795.42 736.48,-782.98 748,-777.03 847.94,-725.49 1188.79,-719.13 1326.04,-718.72"/>
<polygon fill="#0606f9" stroke="black" points="1326,-722.22 1336,-718.7 1325.99,-715.22 1326,-722.22"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_app_plugin -->
<g id="edge165" class="edge">
<title>pydantic&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2597,-1594.07C2624.44,-1434.11 2790.2,-1445.12 2788,-1282.83"/>
<path fill="none" stroke="black" d="M2788,-1280.83C2788.67,-1225.22 2738.04,-1230.57 2712,-1181.43"/>
<path fill="none" stroke="black" d="M2712,-1179.43C2701.24,-1159.12 2368.84,-449.65 2351,-435.16"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_builder -->
<g id="edge166" class="edge">
<title>pydantic&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1549.22,-1776.45C1740.62,-1762.97 2645.46,-1693.89 2597,-1596.07"/>
<path fill="none" stroke="black" d="M2597,-1594.07C2599.23,-1579.56 2585.9,-1578.38 2573,-1571.38 2513.12,-1538.89 2484.93,-1565.86 2424,-1535.38 2335.31,-1491.02 2311.64,-1472.48 2252,-1393.26"/>
<path fill="none" stroke="black" d="M2252,-1391.26C2220.79,-1351.21 2255.44,-1314.82 2216,-1282.83"/>
<path fill="none" stroke="black" d="M2216,-1280.83C2203.83,-1270.96 2125.09,-1230.45 2073.45,-1204.29"/>
<polygon fill="#0606f9" stroke="black" points="2075.05,-1201.17 2064.54,-1199.78 2071.89,-1207.42 2075.05,-1201.17"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_plugin -->
<g id="edge167" class="edge">
<title>pydantic&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1478.17,-1778.56C1269.75,-1776.69 221.05,-1765.07 171,-1720.17 129.47,-1682.9 164.42,-1650.47 152,-1596.07"/>
<path fill="none" stroke="black" d="M152,-1594.07C142.7,-1560.49 115.04,-1566.82 100,-1535.38 72.36,-1477.59 87.28,-1456.32 76,-1393.26"/>
<path fill="none" stroke="black" d="M76,-1391.26C66.02,-1351.29 39.55,-1352.68 24,-1314.54 1.31,-1258.87 0,-1241.54 0,-1181.43 0,-1181.43 0,-1181.43 0,-1069 0,-851.09 102.84,-801.09 267,-657.78 396.18,-545.01 450.04,-536.45 616,-493.28 769.46,-453.37 1262.85,-439.51 1417.32,-436.09"/>
<polygon fill="#0606f9" stroke="black" points="1417.25,-439.59 1427.17,-435.87 1417.1,-432.59 1417.25,-439.59"/>
</g>
<!-- pydantic&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge168" class="edge">
<title>pydantic&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2788,-1280.83C2789.13,-1187.58 2788,-1164.26 2788,-1071 2788,-1071 2788,-1071 2788,-954.28 2788,-886.23 2795.71,-868.78 2788,-801.16"/>
<path fill="none" stroke="black" d="M2788,-799.16C2779.9,-735.03 2750,-723.42 2750,-658.78 2750,-658.78 2750,-658.78 2750,-433.16 2750,-359.4 2674.52,-306.5 2618.21,-277.29"/>
<polygon fill="#0606f9" stroke="black" points="2619.8,-274.18 2609.3,-272.8 2616.65,-280.43 2619.8,-274.18"/>
</g>
<!-- pydantic&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge169" class="edge">
<title>pydantic&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2788,-799.16C2784.83,-763.63 2785.41,-754.48 2788,-718.91"/>
</g>
<!-- pydantic&#45;&gt;plugginger_config_models -->
<g id="edge170" class="edge">
<title>pydantic&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2597,-1594.07C2583.02,-1565.85 2555.59,-1543.75 2531.02,-1528.47"/>
<polygon fill="#0606f9" stroke="black" points="2533.11,-1525.65 2522.73,-1523.53 2529.53,-1531.66 2533.11,-1525.65"/>
</g>
<!-- pydantic&#45;&gt;plugginger_testing_helpers -->
<g id="edge171" class="edge">
<title>pydantic&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2712,-1179.43C2682.67,-1116.68 2691.09,-1095.41 2674,-1028.28 2613.4,-790.32 2649.89,-710.84 2536,-493.28 2513.6,-450.5 2507.12,-434.66 2465,-411.03 2302.81,-320.05 1713.52,-267.88 1542.7,-254.44"/>
<polygon fill="#0606f9" stroke="black" points="1543.12,-250.96 1532.88,-253.67 1542.57,-257.94 1543.12,-250.96"/>
</g>
</g>
</svg>
