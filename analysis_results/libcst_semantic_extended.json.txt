{"files": {"/home/<USER>/Python/plugginger/src/plugginger/__init__.py": {"imports": ["Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='implementations',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='container',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='implementations',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='container',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='implementations',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='container',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "functions": ["__getattr__"], "control_flow": ["if", "if", "if", "if", "if", "if", "if"], "comments_density": [0.10476190476190476], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='implementations',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='container',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/core/constants.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"], "comments_density": [0.40384615384615385], "dependencies": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/core/exceptions.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"], "classes": ["PluggingerE<PERSON><PERSON>", "ConfigurationError", "PluginRegistrationError", "DependencyError", "MissingDependencyError", "CircularDependencyError", "DependencyVersionConflictError", "MissingTypeAnnotationForDIError", "DIContainerError", "DependencyResolutionError", "ServiceDefinitionError", "ServiceNameConflictError", "ServiceNotFoundError", "ServiceExecutionError", "EventDefinitionError", "EventListenerError", "EventListenerUnhandledError", "EventListenerTimeoutError", "EventPayloadValidationError", "PluginTeardownError", "BackgroundTaskError", "BackgroundTaskQueueError", "LockfileError", "FreezeConflictError", "ValidationError", "AppPluginError"], "functions": ["__init__", "__init__", "__init__", "__init__", "__init__", "__init__"], "comments_density": [0.030927835051546393], "dependencies": ["Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/core/__init__.py": {"comments_density": [0.09523809523809523], "imports": [], "dependencies": []}, "/home/<USER>/Python/plugginger/src/plugginger/core/types.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"], "comments_density": [0.3393939393939394], "dependencies": ["Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/core/config.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='enum',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"], "classes": ["EventListenerFaultPolicy", "LogLevel"], "comments_density": [0.07042253521126761], "dependencies": ["Name(\n    value='enum',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/interfaces/__init__.py": {"comments_density": [0.045454545454545456], "imports": [], "dependencies": []}, "/home/<USER>/Python/plugginger/src/plugginger/interfaces/services.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["Service<PERSON><PERSON><PERSON><PERSON><PERSON>", "ServiceRegistry", "ServiceProxy"], "functions": ["call_service", "add_service", "has_service", "list_services", "remove_service", "register_service", "unregister_plugin_services", "get_plugin_services", "__call__", "__getattr__"], "comments_density": [0.005988023952095809], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/interfaces/events.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventRegistry", "EventBridge", "EventValidator", "EventFaultHandler"], "functions": ["emit_event", "add_listener", "remove_listener", "list_patterns", "shutdown", "register_listener", "unregister_plugin_listeners", "get_plugin_listeners", "setup_bridge", "teardown_bridges", "validate_payload", "should_invoke", "handle_error"], "comments_density": [0.004901960784313725], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/implementations/__init__.py": {"comments_density": [0.045454545454545456], "imports": [], "dependencies": []}, "/home/<USER>/Python/plugginger/src/plugginger/implementations/container.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "inspect", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["DIContainer"], "functions": ["__init__", "register", "register_instance", "register_concrete_instance", "get", "_create_instance", "has", "clear", "list_registrations", "get_container", "set_container", "reset_container"], "control_flow": ["if", "if", "if", "if", "try", "try", "for", "if", "if", "if", "if", "try", "if", "for", "for", "if"], "container_get_calls": ["found", "found"], "comments_density": [0.0748502994011976], "dependencies": ["inspect", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/implementations/services.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='interfaces',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='services',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["SimpleServiceDispatcher", "SimpleServiceRegistry"], "functions": ["__init__", "call_service", "add_service", "has_service", "list_services", "remove_service", "__init__", "register_service", "unregister_plugin_services", "get_plugin_services"], "control_flow": ["if", "try", "if", "if", "if", "if", "if", "for"], "comments_density": [0.02654867256637168], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='interfaces',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='services',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/implementations/events.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "asyncio", "fnmatch", "inspect", "Name(\n    value='collections',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='interfaces',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='events',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["SimpleEventFaultHandler", "SimpleEventDispatcher", "SimpleEventRegistry"], "functions": ["__init__", "should_invoke", "handle_error", "__init__", "emit_event", "add_listener", "remove_listener", "list_patterns", "shutdown", "_cleanup_completed_tasks", "_safe_call_listener", "__init__", "register_listener", "unregister_plugin_listeners", "get_plugin_listeners"], "control_flow": ["if", "if", "if", "if", "if", "if", "try", "for", "if", "for", "if", "try", "if", "if", "try", "if", "for", "if", "if", "for", "try", "if", "try", "if", "if", "for", "if", "for", "if", "for", "if"], "comments_density": [0.04866180048661801], "dependencies": ["inspect", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "asyncio", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='interfaces',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='events',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "fnmatch", "Name(\n    value='collections',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/api/__init__.py": {"imports": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app_plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='background',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='depends',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='events',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='service',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='simple_builder',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "comments_density": [0.034482758620689655], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='simple_builder',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='events',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='depends',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app_plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='service',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='background',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/api/service.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "inspect", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "functions": ["service", "decorator", "get_service_metadata", "is_service_method", "extract_service_methods", "validate_service_method_signature", "get_service_call_signature"], "control_flow": ["if", "if", "if", "if", "if", "for", "if", "if", "if", "if", "if", "for", "if", "if"], "comments_density": [0.06808510638297872], "dependencies": ["inspect", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/api/events.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "inspect", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "functions": ["on_event", "decorator", "_normalize_event_patterns", "get_event_listener_metadata", "is_event_listener_method", "extract_event_listeners", "get_listener_patterns", "validate_event_listener_signature", "_validate_async_requirement", "_validate_basic_parameters", "_validate_parameter_types", "_validate_return_annotation"], "control_flow": ["if", "if", "if", "if", "for", "if", "if", "if", "if", "for", "if", "if", "for", "if", "if", "for", "for", "if", "if", "if", "if", "for", "if", "if", "if"], "comments_density": [0.07352941176470588], "dependencies": ["inspect", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/api/depends.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='implementations',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='container',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "inspect"], "classes": ["Depends", "ServiceProxy"], "functions": ["__init__", "plugin_identifier", "resolve", "_resolve_string_dependency", "__repr__", "_resolve_service_dependency", "inject_dependencies", "create_dependency_proxy", "__init__", "_resolve_service", "__getattr__", "__call__", "__repr__", "service_dependency", "config_dependency", "interface_dependency"], "control_flow": ["if", "if", "try", "if", "if", "if", "if", "if", "if", "try", "if", "if", "if", "if", "if", "if", "if", "if", "for", "if", "if", "if", "if", "if"], "container_get_calls": ["found", "found"], "comments_density": [0.05912596401028278], "dependencies": ["inspect", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='implementations',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='container',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/api/background.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "asyncio", "inspect", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='functools',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["BackgroundTaskCallable"], "functions": ["__call__", "background_task", "background_task", "background_task", "decorator", "async_wrapper", "is_background_task", "get_background_task_executor", "get_background_task_original"], "control_flow": ["if", "try", "if"], "comments_density": [0.08904109589041095], "dependencies": ["Name(\n    value='functools',\n    lpar=[],\n    rpar=[],\n)", "inspect", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "asyncio", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/api/app_plugin.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='abc',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["AppPluginBase"], "functions": ["__init__", "_configure_internal_app", "_bridge_events", "setup", "teardown", "internal_app", "_forward_to_internal", "_forward_to_outer"], "control_flow": ["try", "if", "if", "try", "if", "if", "if", "if", "if"], "comments_density": [0.04864864864864865], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='abc',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/api/simple_builder.py": {"imports": ["Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='implementations',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='events',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='implementations',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='services',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='service',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='events',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["SimplePluggingerAppBuilder"], "functions": ["__init__", "with_name", "register_plugin", "build", "_instantiate_plugins", "_register_services_and_events", "_register_plugin_services", "_register_plugin_events", "_create_app_instance", "_generate_instance_id", "app_name", "plugin_count", "is_built"], "control_flow": ["if", "if", "if", "if", "if", "if", "try", "if", "for", "try", "for", "try", "for", "for", "if"], "comments_density": [0.0972972972972973], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='events',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='implementations',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='events',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='service',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='implementations',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='services',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/api/plugin.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "inspect", "Name(\n    value='abc',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='depends',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "control_flow": ["if", "for", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if"], "classes": ["PluginBase"], "functions": ["__init__", "setup", "teardown", "plugin", "decorator", "get_plugin_metadata", "is_plugin_class"], "comments_density": [0.049429657794676805], "dependencies": ["inspect", "Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='abc',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='depends',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/api/builder.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "inspect", "logging", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='graph',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='runtime_facade',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='validation',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app_plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='depends',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='config',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='models',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='utils',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='generic_proxy',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "control_flow": ["if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "for", "for", "for", "if", "if", "try", "for", "for", "if", "try", "if", "try", "if", "if", "if", "if", "if", "if", "try", "for", "if", "for", "if", "if", "if", "if", "try", "if", "for"], "classes": ["PluggingerAppBuilder"], "functions": ["__init__", "_get_plugin_metadata_attr", "_register_item_class", "include", "include_app", "_generate_plugin_instance_id", "build", "get_class_by_reg_name", "get_version_by_reg_name", "_resolve_and_validate_global_config", "_register_all_plugin_interfaces", "_register_plugin_services_and_events", "_finalize_app_plugin_event_bridges"], "comments_density": [0.1064516129032258], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='validation',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "inspect", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='runtime_facade',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='config',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='models',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='graph',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app_plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='depends',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "logging", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='utils',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='generic_proxy',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/api/app.py": {"imports": ["asyncio", "logging", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='runtime_facade',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='config',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='models',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app_plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "control_flow": ["if", "if", "try", "if", "if", "try", "if", "if", "if", "if", "if", "if", "if", "if", "try"], "classes": ["PluggingerAppInstance"], "functions": ["__init__", "run", "stop_all_plugins", "get_plugin_instance", "call_service", "has_service", "list_services", "emit_event", "list_event_patterns", "create_managed_task", "app_name", "global_config", "logger"], "emit_event_calls": ["found"], "comments_density": [0.050359712230215826], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "asyncio", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='config',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='models',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app_plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "logging", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='runtime_facade',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/utils/validation.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "inspect", "re", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "functions": ["validate_plugin_name", "validate_service_name", "validate_event_pattern", "validate_version_string", "validate_timeout_value", "validate_priority_value", "validate_method_signature", "validate_dependency_list", "validate_event_data", "validate_app_name", "is_valid_identifier", "sanitize_name"], "control_flow": ["if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "if", "for", "if", "if", "if", "if", "for", "if", "if", "for", "if", "if", "if", "if", "if", "if", "if", "if", "if"], "comments_density": [0.027950310559006212], "dependencies": ["inspect", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "re", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/utils/graph.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["DependencyNode", "DependencyGraph"], "functions": ["__init__", "add_dependency", "remove_dependency", "add_dependent", "remove_dependent", "get_all_dependencies", "__repr__", "__init__", "add_node", "remove_node", "add_dependency", "get_node", "has_node", "get_all_nodes", "detect_circular_dependencies", "dfs", "topological_sort", "get_load_order", "get_dependencies", "collect_deps", "validate_graph", "__len__", "__contains__", "__repr__"], "control_flow": ["if", "if", "if", "for", "if", "for", "if", "if", "if", "if", "if", "if", "if", "for", "for", "if", "if", "for", "for", "while", "if", "for", "if", "if", "if", "if", "if", "for", "for", "for", "if"], "comments_density": [0.04043126684636118], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/utils/proxy.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["LazyProxy", "ServiceProxy", "ServiceMethodProxy", "EventProxy", "ConfigProxy"], "functions": ["__init__", "_resolve", "__getattr__", "__call__", "__repr__", "__init__", "_get_dispatcher", "call", "__getattr__", "__repr__", "__init__", "__call__", "__repr__", "__init__", "_get_dispatcher", "emit", "__repr__", "__init__", "_get_config", "get", "__getitem__", "__contains__", "__repr__", "create_service_proxy", "create_event_proxy", "create_config_proxy", "create_lazy_proxy"], "control_flow": ["if", "try", "if", "try", "if", "try", "if", "try"], "emit_event_calls": ["found"], "comments_density": [0.003048780487804878], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/utils/generic_proxy.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "control_flow": ["if", "if", "try", "if", "if", "if", "if", "if"], "classes": ["GenericPluginProxy"], "functions": ["__init__", "_resolve_plugin", "__getattr__", "__setattr__", "__call__", "__repr__", "__str__"], "comments_density": [0.029069767441860465], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/testing/mock_app.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "asyncio", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"], "classes": ["MockServiceDispatch<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MockPluggingerAppInstance"], "functions": ["__init__", "add_service", "call_service", "has_service", "list_services", "get_call_history", "clear_call_history", "was_called", "call_count", "remove_service", "__init__", "add_listener", "emit_event", "_pattern_matches", "remove_listener", "list_patterns", "shutdown", "get_event_history", "clear_event_history", "was_emitted", "emission_count", "__init__", "call_service", "emit_event", "create_managed_task", "get_managed_tasks", "cancel_managed_tasks", "_cleanup_task", "shutdown", "app_name", "is_shutdown", "task_count", "service_dispatcher", "event_dispatcher", "__repr__", "create_mock_app"], "control_flow": ["if", "if", "if", "if", "for", "if", "for", "try", "if", "if", "if", "if", "if", "try", "if", "if", "if", "if", "if", "for", "if", "if", "if"], "emit_event_calls": ["found"], "comments_density": [0.0472972972972973], "dependencies": ["asyncio", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/testing/collectors.py": {"imports": ["asyncio", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["EventCollector", "ServiceCallCollector", "CollectorManager"], "functions": ["__init__", "collect_event", "_should_collect", "get_events", "get_events_by_type", "count_events", "has_event", "clear", "get_latest_event", "wait_for_event", "__len__", "__repr__", "__init__", "collect_call", "get_calls", "get_calls_for_service", "count_calls", "was_called", "get_latest_call", "clear", "__len__", "__repr__", "create_event_collector", "create_service_call_collector", "__init__", "add_event_collector", "add_service_collector", "get_event_collector", "get_service_collector", "clear_all", "get_summary"], "control_flow": ["if", "if", "if", "if", "if", "if", "for", "if", "if", "if", "for", "if", "if", "for", "for"], "comments_density": [0.010362694300518135], "dependencies": ["asyncio", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/testing/helpers.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "asyncio", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='unittest',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='mock',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='testing',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='collectors',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='testing',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='mock_app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='service',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='events',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["Plugin<PERSON>est<PERSON><PERSON><PERSON>", "EmptyConfig", "TestFixture"], "functions": ["__init__", "inject_dependency", "inject_mock_service", "setup_plugin", "teardown_plugin", "get_plugin", "call_service", "emit_event", "_pattern_matches", "event_collector", "service_collector", "mock_app", "__aenter__", "__aexit__", "create_plugin_test_runner", "run_plugin_standalone", "__init__", "create_mock_plugin", "create_mock_service", "wait_for_event", "add_cleanup_task", "cleanup", "__aenter__", "__aexit__", "create_test_fixture"], "control_flow": ["if", "if", "if", "if", "for", "if", "try", "if", "if", "if", "if", "for", "if", "for", "try", "if", "if", "if"], "comments_density": [0.04289544235924933], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='testing',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='mock_app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "asyncio", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='events',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='unittest',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='mock',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='service',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='testing',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='collectors',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/testing/__init__.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='testing',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='collectors',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='testing',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='helpers',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='testing',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='mock_app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "comments_density": [0.13793103448275862], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='testing',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='helpers',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='testing',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='mock_app',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='testing',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='collectors',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/config/__init__.py": {"imports": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='config',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='models',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "comments_density": [0.038461538461538464], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='config',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='models',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/config/models.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='datetime',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["ExecutorConfig", "GlobalAppConfig", "PluginLockInfo", "PythonPackageLockInfo", "PluggingerLockfileMetadata", "PluggingerLockfile"], "comments_density": [0.010582010582010581], "dependencies": ["Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='datetime',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/_internal/__init__.py": {"comments_density": [0.0625], "imports": [], "dependencies": []}, "/home/<USER>/Python/plugginger/src/plugginger/_internal/runtime_facade.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='runtime',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='config',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='models',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["RuntimeFacade"], "functions": ["__init__", "finalize_setup", "add_service", "call_service", "has_service", "list_services", "remove_service", "add_event_listener", "emit_event", "remove_event_listener", "list_event_patterns", "register_executor", "get_executor", "setup_all_plugins", "teardown_all_plugins", "get_plugins_were_setup_flag", "shutdown"], "emit_event_calls": ["found"], "control_flow": ["if", "if", "if", "if"], "comments_density": [0.06701030927835051], "dependencies": ["Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='config',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='models',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='runtime',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/_internal/graph.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='collections',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["DependencyGraph"], "functions": ["__init__", "add_node", "add_dependency_edge", "get_prerequisites", "get_dependents", "get_all_nodes", "topological_sort", "__len__", "__contains__", "__repr__"], "control_flow": ["if", "if", "if", "for", "while", "for", "if", "for", "if"], "comments_density": [0.060109289617486336], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='collections',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/_internal/validation.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "inspect", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='packaging',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='specifiers',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='packaging',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='version',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='graph',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='depends',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["DependencyValidator"], "functions": ["__init__", "validate_graph_structure", "validate_dependency_versions_and_signatures", "_validate_version_constraint", "_validate_injection_signature"], "control_flow": ["for", "for", "if", "for", "try", "for", "if", "try", "try", "try", "if", "try", "for", "if", "for", "if", "for", "if", "if", "if"], "comments_density": [0.06611570247933884], "dependencies": ["inspect", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='_internal',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='graph',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='depends',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='packaging',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='specifiers',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='packaging',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='version',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/_internal/runtime/__init__.py": {"imports": ["Attribute(\n    value=Attribute(\n        value=Attribute(\n            value=Name(\n                value='plugginger',\n                lpar=[],\n                rpar=[],\n            ),\n            attr=Name(\n                value='_internal',\n                lpar=[],\n                rpar=[],\n            ),\n            dot=Dot(\n                whitespace_before=SimpleWhitespace(\n                    value='',\n                ),\n                whitespace_after=SimpleWhitespace(\n                    value='',\n                ),\n            ),\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='runtime',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='dispatcher',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Attribute(\n            value=Name(\n                value='plugginger',\n                lpar=[],\n                rpar=[],\n            ),\n            attr=Name(\n                value='_internal',\n                lpar=[],\n                rpar=[],\n            ),\n            dot=Dot(\n                whitespace_before=SimpleWhitespace(\n                    value='',\n                ),\n                whitespace_after=SimpleWhitespace(\n                    value='',\n                ),\n            ),\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='runtime',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='executors',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Attribute(\n            value=Name(\n                value='plugginger',\n                lpar=[],\n                rpar=[],\n            ),\n            attr=Name(\n                value='_internal',\n                lpar=[],\n                rpar=[],\n            ),\n            dot=Dot(\n                whitespace_before=SimpleWhitespace(\n                    value='',\n                ),\n                whitespace_after=SimpleWhitespace(\n                    value='',\n                ),\n            ),\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='runtime',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='fault_policy',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Attribute(\n            value=Name(\n                value='plugginger',\n                lpar=[],\n                rpar=[],\n            ),\n            attr=Name(\n                value='_internal',\n                lpar=[],\n                rpar=[],\n            ),\n            dot=Dot(\n                whitespace_before=SimpleWhitespace(\n                    value='',\n                ),\n                whitespace_after=SimpleWhitespace(\n                    value='',\n                ),\n            ),\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='runtime',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='lifecycle',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "comments_density": [0.047619047619047616], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Attribute(\n            value=Name(\n                value='plugginger',\n                lpar=[],\n                rpar=[],\n            ),\n            attr=Name(\n                value='_internal',\n                lpar=[],\n                rpar=[],\n            ),\n            dot=Dot(\n                whitespace_before=SimpleWhitespace(\n                    value='',\n                ),\n                whitespace_after=SimpleWhitespace(\n                    value='',\n                ),\n            ),\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='runtime',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='lifecycle',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Attribute(\n            value=Name(\n                value='plugginger',\n                lpar=[],\n                rpar=[],\n            ),\n            attr=Name(\n                value='_internal',\n                lpar=[],\n                rpar=[],\n            ),\n            dot=Dot(\n                whitespace_before=SimpleWhitespace(\n                    value='',\n                ),\n                whitespace_after=SimpleWhitespace(\n                    value='',\n                ),\n            ),\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='runtime',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='fault_policy',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Attribute(\n            value=Name(\n                value='plugginger',\n                lpar=[],\n                rpar=[],\n            ),\n            attr=Name(\n                value='_internal',\n                lpar=[],\n                rpar=[],\n            ),\n            dot=Dot(\n                whitespace_before=SimpleWhitespace(\n                    value='',\n                ),\n                whitespace_after=SimpleWhitespace(\n                    value='',\n                ),\n            ),\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='runtime',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='dispatcher',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Attribute(\n            value=Name(\n                value='plugginger',\n                lpar=[],\n                rpar=[],\n            ),\n            attr=Name(\n                value='_internal',\n                lpar=[],\n                rpar=[],\n            ),\n            dot=Dot(\n                whitespace_before=SimpleWhitespace(\n                    value='',\n                ),\n                whitespace_after=SimpleWhitespace(\n                    value='',\n                ),\n            ),\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='runtime',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='executors',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/_internal/runtime/fault_policy.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "asyncio", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["FaultPolicyHandler"], "functions": ["__init__", "should_invoke", "handle_error"], "control_flow": ["if", "if", "if", "if", "if"], "comments_density": [0.04716981132075472], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "asyncio", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='config',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/_internal/runtime/executors.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "os", "Attribute(\n    value=Name(\n        value='concurrent',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='futures',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='config',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='models',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["ExecutorRegistry"], "functions": ["__init__", "register_executor", "get_executor", "shutdown_executors"], "control_flow": ["if", "if", "if", "if", "if", "if", "for", "try"], "comments_density": [0.08053691275167785], "dependencies": ["Attribute(\n    value=Name(\n        value='concurrent',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='futures',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "os", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='config',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='models',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='constants',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/_internal/runtime/dispatcher.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "asyncio", "fnmatch", "inspect", "Name(\n    value='collections',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Attribute(\n            value=Name(\n                value='plugginger',\n                lpar=[],\n                rpar=[],\n            ),\n            attr=Name(\n                value='_internal',\n                lpar=[],\n                rpar=[],\n            ),\n            dot=Dot(\n                whitespace_before=SimpleWhitespace(\n                    value='',\n                ),\n                whitespace_after=SimpleWhitespace(\n                    value='',\n                ),\n            ),\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='runtime',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='fault_policy',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["Service<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "functions": ["__init__", "add_service", "call_service", "has_service", "list_services", "remove_service", "__init__", "add_listener", "remove_listener", "list_patterns", "_cleanup_completed_tasks", "emit_event", "_safe_call_listener", "shutdown"], "control_flow": ["if", "try", "try", "if", "if", "if", "if", "for", "try", "if", "if", "if", "if", "try", "for", "if", "for", "if", "if", "try", "if", "if", "for", "if"], "comments_density": [0.05938242280285035], "dependencies": ["Attribute(\n    value=Attribute(\n        value=Attribute(\n            value=Name(\n                value='plugginger',\n                lpar=[],\n                rpar=[],\n            ),\n            attr=Name(\n                value='_internal',\n                lpar=[],\n                rpar=[],\n            ),\n            dot=Dot(\n                whitespace_before=SimpleWhitespace(\n                    value='',\n                ),\n                whitespace_after=SimpleWhitespace(\n                    value='',\n                ),\n            ),\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='runtime',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='fault_policy',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "inspect", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "asyncio", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "fnmatch", "Name(\n    value='collections',\n    lpar=[],\n    rpar=[],\n)"]}, "/home/<USER>/Python/plugginger/src/plugginger/_internal/runtime/lifecycle.py": {"imports": ["Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"], "classes": ["LifecycleManager"], "functions": ["__init__", "setup_all_plugins", "teardown_all_plugins", "get_plugins_were_setup_flag"], "control_flow": ["if", "for", "if", "if", "try", "if", "if", "for", "try", "if", "if"], "comments_density": [0.05952380952380952], "dependencies": ["Name(\n    value='pydantic',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='exceptions',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Name(\n        value='collections',\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='abc',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='api',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='plugin',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='__future__',\n    lpar=[],\n    rpar=[],\n)", "Name(\n    value='typing',\n    lpar=[],\n    rpar=[],\n)", "Attribute(\n    value=Attribute(\n        value=Name(\n            value='plugginger',\n            lpar=[],\n            rpar=[],\n        ),\n        attr=Name(\n            value='core',\n            lpar=[],\n            rpar=[],\n        ),\n        dot=Dot(\n            whitespace_before=SimpleWhitespace(\n                value='',\n            ),\n            whitespace_after=SimpleWhitespace(\n                value='',\n            ),\n        ),\n        lpar=[],\n        rpar=[],\n    ),\n    attr=Name(\n        value='types',\n        lpar=[],\n        rpar=[],\n    ),\n    dot=Dot(\n        whitespace_before=SimpleWhitespace(\n            value='',\n        ),\n        whitespace_after=SimpleWhitespace(\n            value='',\n        ),\n    ),\n    lpar=[],\n    rpar=[],\n)"]}}, "cyclic_dependencies": []}