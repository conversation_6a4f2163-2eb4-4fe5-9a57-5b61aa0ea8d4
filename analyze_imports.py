#!/usr/bin/env python3

import re
from collections import defaultdict
from pathlib import Path


def analyze_imports():
    """Analyze import dependencies in the Plugginger codebase."""

    src_dir = Path("src/plugginger")
    if not src_dir.exists():
        print("src/plugginger directory not found")
        return

    # Find all Python files
    python_files = list(src_dir.rglob("*.py"))
    print(f"Found {len(python_files)} Python files")

    # Extract imports from each file
    imports = {}
    for file_path in python_files:
        try:
            with open(file_path, encoding='utf-8') as f:
                content = f.read()

            # Convert file path to module name
            relative_path = file_path.relative_to(Path("src"))
            if file_path.name == "__init__.py":
                module_name = str(relative_path.parent).replace("/", ".")
            else:
                module_name = str(relative_path.with_suffix("")).replace("/", ".")

            # Extract plugginger imports
            plugginger_imports = extract_plugginger_imports(content)
            imports[module_name] = plugginger_imports

        except Exception as e:
            print(f"Error processing {file_path}: {e}")

    # Print import analysis
    print("\n=== IMPORT ANALYSIS ===")
    for module, module_imports in imports.items():
        if module_imports:
            print(f"\n{module}:")
            for imp in module_imports:
                print(f"  -> {imp}")

    # Detect potential circular imports
    print("\n=== CIRCULAR IMPORT ANALYSIS ===")
    detect_circular_imports(imports)

def extract_plugginger_imports(content):
    """Extract plugginger imports from file content."""
    imports = []

    # Patterns for different import styles
    patterns = [
        r'from\s+plugginger\.([^\s]+)\s+import',
        r'import\s+plugginger\.([^\s,]+)',
    ]

    for pattern in patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            imports.append(f"plugginger.{match}")

    return imports

def detect_circular_imports(imports):
    """Detect circular import dependencies."""

    # Build dependency graph
    graph = defaultdict(set)
    for module, module_imports in imports.items():
        for imp in module_imports:
            graph[module].add(imp)

    # Find cycles using DFS
    visited = set()
    rec_stack = set()
    cycles = []

    def dfs(node, path):
        if node in rec_stack:
            # Found a cycle
            cycle_start = path.index(node)
            cycle = path[cycle_start:] + [node]
            cycles.append(cycle)
            return

        if node in visited:
            return

        visited.add(node)
        rec_stack.add(node)

        for neighbor in graph.get(node, []):
            dfs(neighbor, path + [node])

        rec_stack.remove(node)

    # Check each module
    for module in imports.keys():
        if module not in visited:
            dfs(module, [])

    # Report cycles
    if cycles:
        print("Found circular imports:")
        for i, cycle in enumerate(cycles, 1):
            print(f"  Cycle {i}: {' -> '.join(cycle)}")
    else:
        print("No circular imports detected in plugginger modules")

    # Check for potential problematic imports
    print("\n=== POTENTIAL ISSUES ===")
    problematic = []

    for module, module_imports in imports.items():
        for imp in module_imports:
            # Check if import might cause issues
            if "api" in module and "api" in imp:
                problematic.append(f"{module} -> {imp} (API cross-dependency)")
            elif "_internal" in imp and "api" in module:
                # This is actually OK - API can import internal
                pass
            elif "api" in imp and "_internal" in module:
                problematic.append(f"{module} -> {imp} (Internal importing API)")

    if problematic:
        print("Potential problematic imports:")
        for issue in problematic:
            print(f"  {issue}")
    else:
        print("No obvious problematic import patterns detected")

if __name__ == "__main__":
    analyze_imports()
